
name: ensuram
description: "A new Flutter project."

publish_to: 'none' # Remove this line if you wish to publish to pub.dev


environment:
  sdk: '>=3.4.3 <4.0.0'


dependencies:
  flutter:
    sdk: flutter
  intl: ^0.18.0
  image_cropper: ^9.0.0
  image_picker: ^1.1.2
  flutter_advanced_avatar: ^1.5.0
  cupertino_icons: ^1.0.8
  get: ^4.6.6
  shimmer: ^3.0.0
  cached_network_image: ^3.4.1
  flutter_screenutil: ^5.9.3
  flutter_easyloading: ^3.0.5
  pin_code_fields: ^8.0.1
  easy_stepper: ^0.8.5+1
  animated_splash_screen: ^1.3.0
  flutter_svg: ^2.0.17
  shared_preferences: ^2.5.1
  dio: ^5.8.0+1
  flutter_image_compress: ^2.1.0

  flutter_paypal: ^0.2.1
  smile_id: ^10.4.2
  flutterwave_standard: ^1.1.0


dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons:
  flutter_lints: ^5.0.0


flutter_icons:
  android: true
  ios: true
  image_path: "assets/images/app_icon.png"
  remove_alpha_ios: true
flutter:

  uses-material-design: true
  assets:
    - assets/
    - assets/icons/
    - assets/images/

  fonts:
    - family: Lato
      fonts:
        - asset: assets/fonts/Lato-Bold.ttf

    - family: LatoRegular
      fonts:
        - asset: assets/fonts/Lato-Regular.ttf
    - family: LatoThin
      fonts:
        - asset: assets/fonts/Lato-Thin.ttf
    - family: LatoLight
      fonts:
        - asset: assets/fonts/Lato-Light.ttf
    - family: OswaldLight
      fonts:
        - asset: assets/fonts/Oswald-Light.ttf

    - family: OswaldRegular
      fonts:
        - asset: assets/fonts/Oswald-Regular.ttf
    - family: OswaldBold
      fonts:
        - asset: assets/fonts/Oswald-Bold.ttf