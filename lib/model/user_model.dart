class User {
  String? userId;
  String? userSalutation;
  String? userFirstname;
  String? userLastname;
  String? userEmail;
  String? userPhoneno;
  String? userPhoto;
  String? userCountry;
  String? userAddress;
  String? userCity;String? accountBalnce;
  String? userRegion;
  String? userZipcode;String? verificationStatus;
  String? userCreated;String? userDOB;

  User(
      {this.userId,
        this.userSalutation,this.verificationStatus,
        this.userFirstname,
        this.userLastname,this.userDOB,
        this.userEmail,
        this.userPhoneno,this.accountBalnce,
        this.userPhoto,
        this.userCountry,
        this.userAddress,
        this.userCity,
        this.userRegion,
        this.userZipcode,
        this.userCreated});

  User.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'].toString();
    userSalutation = json['user_salutation'];
    userFirstname = json['user_firstname'];
    userLastname = json['user_lastname'];verificationStatus = json['verification_status'].toString();
    userEmail = json['user_email'];
    userPhoneno = json['user_phoneno'];accountBalnce = json['account_balance']!=null?json['account_balance'].toString():"0.00";
    userPhoto = json['user_photo'];
    userCountry = json['user_country'];
    userDOB = json['user_birthdate'];
    userAddress = json['user_address'];
    userCity = json['user_city'];
    userRegion = json['user_region'];
    userZipcode = json['user_zipcode'];
    userCreated = json['user_created'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['user_id'] = this.userId;
    data['user_salutation'] = this.userSalutation;
    data['user_firstname'] = this.userFirstname;
    data['user_lastname'] = this.userLastname;
    data['user_email'] = this.userEmail;
    data['user_phoneno'] = this.userPhoneno;
    data['user_photo'] = this.userPhoto;data['verification_status'] = this.verificationStatus;
    data['user_country'] = this.userCountry;
    data['user_address'] = this.userAddress;data['user_birthdate'] = this.userDOB;
    data['user_city'] = this.userCity;data['account_balance'] = this.accountBalnce;
    data['user_region'] = this.userRegion;
    data['user_zipcode'] = this.userZipcode;
    data['user_created'] = this.userCreated;
    return data;
  }
}
