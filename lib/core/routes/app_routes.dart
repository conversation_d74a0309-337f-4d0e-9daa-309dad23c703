import 'package:ensuram/view/modules/appointment/view/appointment_scheduler_view.dart';
import 'package:ensuram/view/modules/appointment/view/book_appointment_view.dart';
import 'package:ensuram/view/modules/billing/view/billing_view.dart';
import 'package:ensuram/view/modules/contributions/view/contributions_view.dart';
import 'package:ensuram/view/modules/expense/view/expenses_view.dart';
import 'package:ensuram/view/modules/finances/view/finances_view.dart';
import 'package:ensuram/view/modules/laboratory/view/lab_tests_view.dart';
import 'package:ensuram/view/modules/notifications/view/notifications_view.dart';
import 'package:ensuram/view/modules/profile/view/change_password_view.dart';
import 'package:ensuram/view/modules/profile/view/edit_location_view.dart';
import 'package:ensuram/view/modules/profile/view/edit_profile_view.dart';
import 'package:ensuram/view/starting/boarding_view.dart';
import 'package:get/get.dart';

import '../../view/dashboard/view/dashboard_view.dart';

import '../../view/modules/authentication/view/account_verification_view.dart';
import '../../view/modules/authentication/view/forgot_password_view.dart';
import '../../view/modules/authentication/view/login_view.dart';
import '../../view/modules/authentication/view/registration_view.dart';
import '../../view/modules/beneficiary/view/add_benefieries_view.dart';
import '../../view/modules/beneficiary/view/benefieries_view.dart';

import '../../view/modules/insurance/view/insurance_summary_view.dart';
import '../../view/modules/insurance/view/insurances_view.dart';
import '../../view/modules/wallets/view/add_wallet_view.dart';
import '../../view/modules/wallets/view/send_wallet_view.dart';

class AppRoutes {
  static const home = '/';
  static const boarding = '/boarding';
  static const userForgotPassword = '/user-forgot-password';
  static const userLogin = '/user-login';
  static const userRegistration = '/user-registration';
  static const userAccountVerification = '/user-account-verification';
  static const userSelectionIndividual = '/user-selection-individual';
  static const userSelectionBusiness = '/user-selection-business';
  static const userInsurances = '/user-insurances';
  static const userBeneficiariesList = '/user-beneficiaries';
  static const userAddBeneficiary = '/user-add-beneficiary';
  static const myLabTests = '/laboratory_tests';
  static const myInsurances = '/my-insurances';
  static const findInsurances = '/find-insurances';
  static const bookAppointment = '/book-appointment';
  static const appointmentScheduler = '/appointment-scheduler';
  static const userAddWallet = '/user-add-wallet';
  static const userBills= '/user-bills';
  static const userExpenses = '/expense-tracker';
  static const userSendWallet = '/user-send-wallet';
  static const userNotifications = '/user-notifications';
  static const userFinances = '/user-finances';
  static const userEditProfile = '/edit-profile';
  static const userEditLocation = '/edit-location';
  static const changePassword = '/change-password';
  static const contributions = '/contributions';

  static const userBeneficiaries = '/user-beneficiaries-family';
  static const userDashboard = '/user-dashboard';
  static const userSummaryInsurance = '/user-summary-insurance';
  static final routes = [
    GetPage(name: boarding, page: () => BoardingView()),
    GetPage(name: userLogin, page: () => UserLoginView()),
    GetPage(name: userForgotPassword, page: () => UserForgotPasswordView()),
    GetPage(name: userRegistration, page: () => UserRegistrationView()),
    GetPage(
        name: userAccountVerification,
        page: () => UserAccountVerificationView()),
    GetPage(
        name: userAccountVerification,
        page: () => UserAccountVerificationView()),

    GetPage(name: AppRoutes.userInsurances, page: () => UserInsurancesView()),
    GetPage(
        name: AppRoutes.userSummaryInsurance,
        page: () => UserInsuranceSummaryView()),
    GetPage(
        name: AppRoutes.userBeneficiariesList,
        page: () => UserBenefieriesListView()),
    GetPage(
        name: AppRoutes.userAddBeneficiary, page: () => AddBeneficiaryView()),
    GetPage(
        name: AppRoutes.userAddBeneficiary, page: () => AddBeneficiaryView()),

    GetPage(name: AppRoutes.appointmentScheduler, page: () => AppointmentSchedularView()),
    GetPage(name: AppRoutes.bookAppointment, page: () => BookAppointmentView()),
    GetPage(name: AppRoutes.userAddWallet, page: () => AddWalletView()),
    GetPage(name: AppRoutes.userSendWallet, page: () => SendWalletView()),
    GetPage(name: AppRoutes.userBills, page: () => BillingView()),
    GetPage(name: AppRoutes.userExpenses, page: () => ExpenseTrackerView()),
    GetPage(name: AppRoutes.userFinances, page: () => FinancesView()),
    GetPage(name: AppRoutes.userNotifications, page: () => NotificationView()),
    GetPage(name: AppRoutes.userEditLocation, page: () => EditLocationView()),
    GetPage(name: AppRoutes.userEditProfile, page: () => EditProfileView()),
    GetPage(name: AppRoutes.changePassword, page: () => ChangePasswordView()),
    GetPage(name: AppRoutes.contributions, page: () => ContributionsView()),


  ];
}
