import 'package:intl/intl.dart';

class Utils {
  static String formatDate(DateTime date, {String format = 'yyyy-MM-dd'}) {
    try {
      return DateFormat(format).format(date);
    } catch (e) {
      return '';
    }
  }

  static DateTime? parseDate(String date, {String format = 'yyyy-MM-dd'}) {
    try {
      return DateFormat(format).parse(date);
    } catch (e) {
      return null;
    }
  }

  // Get day number from date string
  static String getDayFromDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd').format(date);
    } catch (e) {
      return '';
    }
  }

  // Get month name from date string
  static String getMonthFromDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('MMM').format(date);
    } catch (e) {
      return '';
    }
  }
}