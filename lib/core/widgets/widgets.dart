import 'package:cached_network_image/cached_network_image.dart';
import 'package:ensuram/core/routes/app_routes.dart';
import 'package:ensuram/core/widgets/custom_checkbox.dart';
import 'package:ensuram/core/widgets/text_widgets.dart';
import 'package:ensuram/view/modules/insurance/model/insurances.dart';
import 'package:ensuram/view/modules/insurance/policy_model.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_typedefs/rx_typedefs.dart';

import '../../view/modules/appointment/model/appointment_model.dart';
import '../../view/modules/beneficiary/model/beneificery_model.dart';
import '../../view/modules/billing/model/insurances_model.dart';
import '../../view/modules/contributions/model/invite_org_model.dart';
import '../../view/modules/expense/model/expense_model.dart';
import '../../view/modules/laboratory/model/lab_book_model.dart';
import '../../view/modules/pharmacy/model/pharmacy_order_model.dart';
import '../../view/modules/pre_authorization/model/pre_auth_model.dart';
import '../../view/modules/wallets/model/saving_transactions.dart';
import '../constants/assets_constants.dart';
import '../constants/color_constants.dart';
import '../utils/date_utils.dart';

class Widgets {
  static var heightSpaceH05 = SizedBox(
    height: 0.005.sh,
  );
  static var heightSpaceH1 = SizedBox(
    height: 0.01.sh,
  );
  static var heightSpaceH2 = SizedBox(
    height: 0.02.sh,
  );
  static var heightSpaceH3 = SizedBox(
    height: 0.03.sh,
  );
  static var heightSpaceH4 = SizedBox(
    height: 0.04.sh,
  );
  static var heightSpaceH5 = SizedBox(
    height: 0.05.sh,
  );
  static var widthSpaceW1 = SizedBox(
    width: 0.01.sw,
  );
  static var widthSpaceW2 = SizedBox(
    width: 0.02.sw,
  );
  static var widthSpaceW3 = SizedBox(
    width: 0.03.sw,
  );

  static noRecordsFound({required String? title}) {
    return Center(
        child: Padding(
      padding: EdgeInsets.only(top: 240),
      child: Text(
        title ?? "",
        style: TextStyle(color: Colors.black54, fontSize: 16),
      ),
    ));
  }

  static noFound() {
    return const Center(
        child: Padding(
      padding: EdgeInsets.only(top: 0),
      child: Text(
        "No Data Found",
        style: TextStyle(color: Colors.black54, fontSize: 16),
      ),
    ));
  }

  static Widget networkImage(String url,
      {double? height, double? width, int? cacheSize}) {
    return CachedNetworkImage(
      imageUrl: url,
      fit: BoxFit.cover,
      height: height,
      width: width,
      memCacheHeight: cacheSize,
      memCacheWidth: cacheSize,
      progressIndicatorBuilder: (context, url, downloadProgress) => Image.asset(
        Assets.placeholder,
        fit: BoxFit.cover,
        height: height,
        width: width,
      ),
      errorWidget: (context, url, error) => Image.asset(
        Assets.placeholder,
        fit: BoxFit.cover,
        height: height,
        width: width,
      ),
    );
  }

  static profileMenu(
      {required String text,
      required IconData icon,
      required Callback press,
      required bool? isBadge,
      String? count}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 5),
      child: TextButton(
        style: TextButton.styleFrom(
          foregroundColor: ColorConstants.primaryColor,
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          backgroundColor: Colors.white,
        ),
        onPressed: press,
        child: Row(
          children: [
            Stack(
              children: [
                Icon(
                  icon,
                  color: Colors.black87,
                ),
                Positioned(
                    right: 0,
                    top: 0,
                    child: isBadge == true
                        ? CircleAvatar(
                            backgroundColor: Colors.red,
                            child: Text(
                              count!,
                              style:
                                  TextStyle(fontSize: 7, color: Colors.white),
                            ),
                            radius: 6,
                          )
                        : const SizedBox.shrink())
              ],
            ),
            const SizedBox(width: 10),
            Expanded(
              child: Text(
                text,
                style: TextStyle(
                  color: Colors.black87,
                ),
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: Color(0xFF757575),
              size: 18,
            ),
          ],
        ),
      ),
    );
  }

  static buildDropdown(
      {String? hint,
      String? value,
      required Callback? onTap,
      Color? color,
      double? textSize,
      IconData? icon,
      double? iconSize,
      Color? textColor,
      FontWeight? fontWeight,
      TextAlign? align}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 1.sw,
        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        decoration: BoxDecoration(
          color: color ?? ColorConstants.whiteColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
                child: Texts.textBlock(value == null ? hint ?? "" : value ?? "",
                    align: align ?? TextAlign.center,
                    color: textColor ?? Colors.white,
                    fontWeight: fontWeight ?? FontWeight.w500,
                    size: textSize ?? 14)),
            Icon(
              icon ?? Icons.keyboard_arrow_down_sharp,
              color: textColor ?? Colors.white,
              size: iconSize ?? 22,
            )
          ],
        ),
      ),
    );
  }

  static var blockDecoration = BoxDecoration(
      color: Colors.white, borderRadius: BorderRadius.circular(8));
  static customDivider(
      {bool isVertical = false,
      Color? color,
      double? padding,
      double? thickness}) {
    return Padding(
      padding: padding != null
          ? EdgeInsets.symmetric(vertical: padding)
          : const EdgeInsets.only(top: 5.0),
      child: isVertical
          ? VerticalDivider(
              thickness: 1.0,
              color: color ?? Colors.black12,
            )
          : Divider(
              height: 1.0,
              thickness: thickness ?? 1.0,
              color: color ?? Colors.black12,
            ),
    );
  }

  static buildRatingStar(num starValue, {bool? isCenter, double? size}) {
    Color color = starValue < 2 ? Colors.orangeAccent : Colors.orangeAccent;
    var starIconsMap = [1, 2, 3, 4, 5].map((e) {
      if (starValue >= e) {
        return Icon(
          Icons.star_rate,
          color: color,
          size: size ?? 14,
        );
      } else if (starValue < e && starValue > e - 1) {
        return Icon(
          Icons.star_half,
          size: size ?? 14,
          color: color,
        );
      } else {
        return Icon(
          Icons.star_border,
          color: color,
          size: size ?? 14,
        );
      }
    }).toList();

    return Row(
        mainAxisAlignment: isCenter ?? false
            ? MainAxisAlignment.center
            : MainAxisAlignment.start,
        children: starIconsMap);
  }

  static Widget assetImage(String url, double width, double height) {
    return Image.asset(url, fit: BoxFit.cover, width: width, height: height);
  }

  static void hideLoader() {
    EasyLoading.dismiss();
  }

  static void showSnackBar(String title, String message) {
    Get.snackbar(
      duration: const Duration(seconds: 10),
      title,
      message,
      backgroundColor: title == "Success" ? Colors.green : Colors.red,
      colorText: Colors.white,
    );
  }

  static Widget widgetLoader() {
    return Center(
      child: CircularProgressIndicator(
        color: ColorConstants.primaryColor,
      ),
    );
  }

  static Widget divider({bool isVertical = false}) {
    return Padding(
      padding: const EdgeInsets.only(top: 5.0),
      child: isVertical
          ? const VerticalDivider(thickness: 1.0, color: Colors.black12)
          : const Divider(height: 1.0, thickness: 1.0, color: Colors.black12),
    );
  }

  static void showLoader(String message) {
    EasyLoading.instance
      ..displayDuration = const Duration(milliseconds: 2000)
      ..loadingStyle = EasyLoadingStyle.custom
      ..backgroundColor = ColorConstants.secondaryColor
      ..indicatorColor = Colors.white
      ..textColor = Colors.white
      ..maskColor = ColorConstants.secondaryColor
      ..dismissOnTap = false;

    EasyLoading.show(
      maskType: EasyLoadingMaskType.none,
      indicator: const CircularProgressIndicator(color: Colors.white),
      status: message,
    );
  }

  static Future<bool> confirmationDialogue(
      BuildContext context, String title, String content) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: true,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(title),
              content: Text(content),
              actions: [
                TextButton(
                  onPressed: () => Get.back(result: false),
                  child: Text('No',
                      style: TextStyle(color: ColorConstants.primaryColor)),
                ),
                TextButton(
                  onPressed: () => Get.back(result: true),
                  child: Text('Yes',
                      style: TextStyle(color: ColorConstants.primaryColor)),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  static insuranceCard(Insurances product,{required bool isSelected, required VoidCallback onCheckboxChanged}) {
    return InkWell(
        child: Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Row(children: [
                InkWell(
                  onTap: () {},
                  child: Widgets.networkImage(product.insuranceLogo??"",height: 90,width: 80)
                ),
                const SizedBox(
                  width: 7.0,
                ),
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Texts.textBlock(product.insuranceName??"",
                            fontWeight: FontWeight.w500,
                            maxline: 2,
                            color: Colors.black,
                            size: 14),
                        const SizedBox(
                          height: 4.0,
                        ),
                        Texts.textBlock('Coverage: ${product.coverageValue??""} ${product.currency??""}',
                            fontWeight: FontWeight.w500,
                            color: Colors.black54,
                            size: 12),   const SizedBox(
                          height: 2.0,
                        ),
                        Texts.textBlock('Hospital Covered: ${product.hospitalCovered??""}',
                            fontWeight: FontWeight.w500,
                            color: Colors.black54,
                            size: 12),
                        const SizedBox(
                          height: 4.0,
                        ),
                        Texts.textBlock("${product.yearCost??""} ${product.currency??""}/yearly",
                            fontWeight: FontWeight.w800,
                            color: Colors.black,
                            size: 13),
                      ],
                    ),
                  ),
                ),
              ])),
        ),

        Positioned(
            right: 10,
            top: -10,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 20,
                  child: Transform.scale(
                    scale: .8, // Adjust the scale factor as needed
                    child: Checkbox(
                        fillColor: MaterialStateProperty.resolveWith((states) {
                          // Check for inactive state
                          if (states.contains(MaterialState.disabled) ||
                              !states.contains(MaterialState.selected)) {
                            return Colors.white; // Inactive color
                          }
                          return ColorConstants
                              .primaryColor; // Active color (optional, same as above)
                        }),
                        activeColor: Colors.white,
                        value: isSelected,
                      onChanged: (_) => onCheckboxChanged(),
                  ),
                )),
                Texts.textBlock("Compare",
                    size: 9, color: Colors.black38, fontFamily: "LatoLight")
              ],
            )),
        Positioned(
            left: -3,
            top: -3,
            child: ClipRRect(
                child: Container(
              color: Colors.orangeAccent,
              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              child: Texts.textMedium(product.planName??"", size: 11, color: Colors.white),
            ))),
      ],
    ));
  }

  static currentInsuranceCard() {
    return InkWell(
        child: Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Row(children: [
                InkWell(
                  onTap: () {},
                  child: Container(color: Colors.grey, width: 70, height: 70),
                ),
                const SizedBox(
                  width: 7.0,
                ),
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Texts.textBlock("Insurance name",
                            fontWeight: FontWeight.w500,
                            maxline: 2,
                            color: Colors.black,
                            size: 15),
                        const SizedBox(
                          height: 4.0,
                        ),
                        Row(
                          children: [
                            Icon(
                              Icons.location_pin,
                              color: Colors.grey,
                              size: 14,
                            ),
                            Texts.textBlock(' Address',
                                fontWeight: FontWeight.w500,
                                color: Colors.black54,
                                size: 13),
                          ],
                        ),
                        const SizedBox(
                          height: 4.0,
                        ),
                        Texts.textBlock("\$234/MO",
                            fontWeight: FontWeight.w800,
                            color: Colors.black,
                            size: 16),
                      ],
                    ),
                  ),
                ),
              ])),
        ),
        Positioned(
            left: -3,
            top: -3,
            child: ClipRRect(
                child: Container(
              color: Colors.orangeAccent,
              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              child: Texts.textMedium("Bronze", size: 11, color: Colors.white),
            ))),
      ],
    ));
  }

  static beneficiaryCard(
      {required Beneficary beneficiary,
      required Function() onEditTap,
      required Function() onDeleteTap}) {
    return InkWell(
        child: Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Row(children: [
                InkWell(
                  onTap: () {},
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: Container(
                        color: Colors.grey,
                        width: 70,
                        height: 70,
                        child: Widgets.networkImage(beneficiary.photo ?? "")),
                  ),
                ),
                const SizedBox(
                  width: 7.0,
                ),
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Texts.textBlock(beneficiary.name ?? "",
                            fontWeight: FontWeight.w500,
                            maxline: 2,
                            color: Colors.black,
                            size: 15),
                        const SizedBox(
                          height: 2.0,
                        ),
                        Texts.textBlock(beneficiary.beneficiaryId ?? "",
                            fontWeight: FontWeight.w300,
                            color: Colors.black54,
                            size: 14),
                        const SizedBox(
                          height: 4.0,
                        ),
                        Texts.textBlock(beneficiary.phoneNumber ?? "",
                            fontWeight: FontWeight.w300,
                            color: Colors.black54,
                            size: 14),
                      ],
                    ),
                  ),
                ),
              ])),
        ),
        Positioned(
            right: 10,
            bottom: 10,
            child: InkWell(
              onTap: () {},
              child: Row(
                children: [
                  InkWell(
                    onTap: onDeleteTap,
                    child: Icon(
                      Icons.delete,
                      size: 18,
                      color: Colors.red,
                    ),
                  ),
                  SizedBox(
                    width: 10,
                  ),
                  InkWell(
                    onTap: onEditTap,
                    child: Icon(
                      Icons.edit,
                      size: 18,
                      color: ColorConstants.primaryColor,
                    ),
                  )
                ],
              ),
            )),
      ],
    ));
  }

  static labOrderCard(LabOrder order) {
    return InkWell(
        child: Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
              width: 1.sw,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Texts.textBlock("Lab Name",
                      color: Colors.black45,
                      fontWeight: FontWeight.w300,
                      size: 12),
                  Texts.textBlock(order.labName ?? "",
                      fontWeight: FontWeight.w500,
                      maxline: 2,
                      color: Colors.black,
                      size: 16),
                  const SizedBox(
                    height: 3.0,
                  ),
                  Texts.textBlock("Test Name",
                      fontWeight: FontWeight.w300,
                      color: Colors.black54,
                      size: 12),
                  Texts.textBlock(order.testName ?? "",
                      fontWeight: FontWeight.w500,
                      maxline: 2,
                      color: Colors.black,
                      size: 16),
                  const SizedBox(
                    height: 4.0,
                  ),
                  Row(children: [
                    InkWell(
                      onTap: () {},
                      child: Container(
                        width: 25,
                        height: 25,
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: ColorConstants.primaryColor),
                        child: Icon(
                          Icons.calendar_month,
                          color: ColorConstants.whiteColor,
                          size: 12,
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 7.0,
                    ),
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 5.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Texts.textBlock("Appointment Date & Time",
                                fontWeight: FontWeight.w300,
                                color: Colors.black54,
                                size: 10),
                            Texts.textBlock(
                                "${order.appointmentTime ?? ""} ${order.appointmentDate ?? ""}",
                                fontWeight: FontWeight.w500,
                                color: Colors.black,
                                size: 13),
                          ],
                        ),
                      ),
                    ),
                  ])
                ],
              )),
        ),
        Positioned(
            right: 10,
            top: 10,
            child: InkWell(
              onTap: () {
                Get.toNamed(AppRoutes.userSummaryInsurance);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                decoration: BoxDecoration(
                  color: order.status == "Pending"
                      ? Colors.red.shade100
                      : Colors.green.shade100,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Texts.textMedium(order.status ?? "Pending",
                    size: 10,
                    color:
                        order.status == "Pending" ? Colors.red : Colors.green),
              ),
            )),
      ],
    ));
  }

  static pharmacyOrderCard(PharmacyOrder order) {
    return InkWell(
        child: Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
              width: 1.sw,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Texts.textBlock("Order No.",
                      color: Colors.black45,
                      fontWeight: FontWeight.w300,
                      size: 12),
                  Texts.textBlock(order.orderNumber ?? "",
                      fontWeight: FontWeight.w500,
                      maxline: 2,
                      color: Colors.black,
                      size: 16),
                  const SizedBox(
                    height: 3.0,
                  ),
                  Texts.textBlock("Order Total:",
                      fontWeight: FontWeight.w300,
                      color: Colors.black54,
                      size: 12),
                  Texts.textBlock(order.orderTotal ?? "",
                      fontWeight: FontWeight.w500,
                      maxline: 2,
                      color: Colors.black,
                      size: 16),
                  const SizedBox(
                    height: 4.0,
                  ),
                  Row(children: [
                    InkWell(
                      onTap: () {},
                      child: Container(
                        width: 25,
                        height: 25,
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: ColorConstants.primaryColor),
                        child: Icon(
                          Icons.calendar_month,
                          color: ColorConstants.whiteColor,
                          size: 12,
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 7.0,
                    ),
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 5.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Texts.textBlock(" Date & Time",
                                fontWeight: FontWeight.w300,
                                color: Colors.black54,
                                size: 10),
                            Texts.textBlock("${order.createdAt ?? ""}",
                                fontWeight: FontWeight.w500,
                                color: Colors.black,
                                size: 13),
                          ],
                        ),
                      ),
                    ),
                  ])
                ],
              )),
        ),
        Positioned(
            right: 10,
            top: 10,
            child: InkWell(
              onTap: () {
                Get.toNamed(AppRoutes.userSummaryInsurance);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                decoration: BoxDecoration(
                  color: order.status == "Pending"
                      ? Colors.red.shade100
                      : Colors.green.shade100,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Texts.textMedium(order.status ?? "Pending",
                    size: 10,
                    color:
                        order.status == "Pending" ? Colors.red : Colors.green),
              ),
            )),
      ],
    ));
  }

  static policyOrderCard(PolicyOrder order) {
    return InkWell(
        child: Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
              width: 1.sw,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Texts.textBlock("Subscription Type",
                      color: Colors.black45,
                      fontWeight: FontWeight.w300,
                      size: 10),
                  Texts.textBlock(order.subscriptionType ?? "",
                      fontWeight: FontWeight.w500,
                      maxline: 2,
                      color: Colors.black,
                      size: 13),
                  const SizedBox(
                    height: 3.0,
                  ),
                  Texts.textBlock("Created Date",
                      fontWeight: FontWeight.w300,
                      color: Colors.black54,
                      size: 10),
                  Texts.textBlock(order.policyCreated ?? "",
                      fontWeight: FontWeight.w500,
                      maxline: 2,
                      color: Colors.black,
                      size: 13),
                  const SizedBox(
                    height: 3.0,
                  ),
                  const SizedBox(
                    height: 4.0,
                  ),
                  Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Texts.textBlock("Total Cost",
                                fontWeight: FontWeight.w300,
                                color: Colors.black54,
                                size: 10),
                            Texts.textBlock("\$${order.totalCost ?? ""}",
                                fontWeight: FontWeight.w500,
                                color: Colors.black,
                                size: 13),
                          ],
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Texts.textBlock("No of People",
                                fontWeight: FontWeight.w300,
                                color: Colors.black54,
                                size: 10),
                            Texts.textBlock("${order.numPeople ?? ""}",
                                fontWeight: FontWeight.w500,
                                color: Colors.black,
                                size: 13),
                          ],
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Texts.textBlock("Plan Name",
                                fontWeight: FontWeight.w300,
                                color: Colors.black54,
                                size: 10),
                            Texts.textBlock("${order.planName ?? ""}",
                                fontWeight: FontWeight.w500,
                                color: Colors.black,
                                size: 13),
                          ],
                        ),
                      ])
                ],
              )),
        ),
        Positioned(
            right: 10,
            top: 10,
            child: InkWell(
              onTap: () {
                Get.toNamed(AppRoutes.userSummaryInsurance);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                decoration: BoxDecoration(
                  color: order.policyStatus == "Pending"
                      ? Colors.red.shade100
                      : Colors.green.shade100,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Texts.textMedium(order.policyStatus ?? "Pending",
                    size: 10,
                    color: order.policyStatus == "Pending"
                        ? Colors.red
                        : Colors.green),
              ),
            )),
      ],
    ));
  }

  static appointmentCard(
    Appointment appointment,
    onDeleteTap,
    onEditTap,
  ) {
    return InkWell(
        child: Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
              width: 1.sw,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Texts.textBlock("Appointment scheduled with",
                      fontWeight: FontWeight.w800,
                      maxline: 2,
                      color: Colors.black,
                      size: 12),
                  Texts.textBlock(appointment.doctor ?? " ",
                      fontWeight: FontWeight.w500,
                      maxline: 2,
                      color: Colors.black,
                      size: 16),
                  const SizedBox(
                    height: 3.0,
                  ),
                  Texts.textBlock("At ${appointment.hospital ?? ""} ",
                      fontWeight: FontWeight.w300,
                      color: Colors.black54,
                      size: 12),
                  const SizedBox(
                    height: 3.0,
                  ),
                  Texts.textBlock(
                      "Speciality in ${appointment.specialty ?? ""}",
                      fontWeight: FontWeight.w300,
                      color: Colors.black54,
                      size: 12),
                  const SizedBox(
                    height: 4.0,
                  ),
                  Row(children: [
                    InkWell(
                      onTap: () {},
                      child: Container(
                        width: 25,
                        height: 25,
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: ColorConstants.primaryColor),
                        child: Icon(
                          Icons.calendar_month,
                          color: ColorConstants.whiteColor,
                          size: 12,
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 7.0,
                    ),
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 5.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Texts.textBlock("Date & Time",
                                fontWeight: FontWeight.w300,
                                color: Colors.black54,
                                size: 10),
                            Texts.textBlock(
                                "${appointment.time ?? ""} ${appointment.date ?? ""} ",
                                fontWeight: FontWeight.w500,
                                color: Colors.black,
                                size: 13),
                          ],
                        ),
                      ),
                    ),
                  ])
                ],
              )),
        ),
        Positioned(
            right: 10,
            top: 10,
            child: InkWell(
              onTap: () {
                Get.toNamed(AppRoutes.userSummaryInsurance);
              },
              child: Row(
                children: [
                  InkWell(
                    onTap: onDeleteTap,
                    child: Icon(
                      Icons.delete,
                      size: 18,
                      color: Colors.red,
                    ),
                  ),
                  SizedBox(
                    width: 10,
                  ),
                  InkWell(
                    onTap: onEditTap,
                    child: Icon(
                      Icons.edit,
                      size: 18,
                      color: ColorConstants.primaryColor,
                    ),
                  )
                ],
              ),
            )),
      ],
    ));
  }

  static notificationCard() {
    return InkWell(
        child: Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
              width: 1.sw,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Row(children: [
                InkWell(
                  onTap: () {},
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: ColorConstants.primaryColor),
                    child: Icon(
                      CupertinoIcons.bell,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(
                  width: 7.0,
                ),
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Texts.textBlock("This is the Title",
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                            size: 13),
                        Texts.textBlock("this is the subtitle of notifications",
                            fontWeight: FontWeight.w300,
                            color: Colors.black54,
                            size: 10),
                      ],
                    ),
                  ),
                ),
              ])),
        ),
      ],
    ));
  }

  static transactionCard({SavingTransactions? transaction}) {
    // Get date components from transaction date
    final day = transaction?.createAt != null
        ? Utils.getDayFromDate(transaction!.createAt!)
        : "13";

    final month = transaction?.createAt != null
        ? Utils.getMonthFromDate(transaction!.createAt!)
        : "May";

    return InkWell(
        child: Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
              width: 1.sw,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Row(children: [
                InkWell(
                  onTap: () {},
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.lightGreen),
                    child: Column(
                      children: [
                        Texts.textBlock(day,
                            fontWeight: FontWeight.w800,
                            color: Colors.white,
                            size: 13),
                        Texts.textBlock(month,
                            fontWeight: FontWeight.w300,
                            color: Colors.white,
                            size: 10),
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  width: 7.0,
                ),
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Texts.textBlock("Payment from",
                            fontWeight: FontWeight.w300,
                            color: Colors.black54,
                            size: 10),
                        Texts.textBlock(transaction?.paymentMethod ?? "",
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                            size: 13),
                        Texts.textBlock(transaction?.txnId ?? "",
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                            size: 13),
                      ],
                    ),
                  ),
                ),
                Texts.textBlock(
                    "+${transaction?.paidAmount} ${transaction?.currency ?? ""}",
                    fontWeight: FontWeight.w800,
                    color: Colors.black,
                    size: 13),
              ])),
        ),
      ],
    ));
  }

  static contributionCard(OrganizationBill transaction) {
    // Get date components from transaction date
    final day = transaction?.dateDone != null
        ? Utils.getDayFromDate(transaction!.dateDone!)
        : "13";

    final month = transaction?.dateDone != null
        ? Utils.getMonthFromDate(transaction!.dateDone!)
        : "May";
    return InkWell(
        child: Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
              width: 1.sw,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Row(children: [
                InkWell(
                  onTap: () {},
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.lightGreen),
                    child: Column(
                      children: [
                        Texts.textBlock(day,
                            fontWeight: FontWeight.w800,
                            color: Colors.white,
                            size: 13),
                        Texts.textBlock(month,
                            fontWeight: FontWeight.w300,
                            color: Colors.white,
                            size: 10),
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  width: 7.0,
                ),
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Texts.textBlock("Amount",
                            fontWeight: FontWeight.w300,
                            color: Colors.black54,
                            size: 10),
                        Texts.textBlock("\$${transaction.amount}",
                            fontWeight: FontWeight.w800,
                            color: Colors.black,
                            size: 15),
                        SizedBox(
                          height: 3,
                        )
                      ],
                    ),
                  ),
                ),
              ])),
        ),
      ],
    ));
  }

  static authorizationCard(PreAuth transaction,Callback? onTap) {
    return InkWell(
        child: Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
              width: 1.sw,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Row(children: [
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Texts.textBlock(
                            "${transaction.firstName} ${transaction.lastName}",
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                            size: 12),
                        Texts.textBlock("Code: ${transaction.code}",
                            fontWeight: FontWeight.w500,
                            color: Colors.black54,
                            size: 11),
                      ],
                    ),
                  ),
                ),
                Column(
                  children: [
                    Texts.textBlock("+\$${transaction.amount}",
                        fontWeight: FontWeight.w800,
                        color: Colors.black,
                        size: 13),
                    SizedBox(
                      height: 3,
                    ),
                    InkWell(onTap: onTap,
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                        decoration: BoxDecoration(
                            color: ColorConstants.primaryColor,
                            borderRadius: BorderRadius.circular(5)),
                        child: Texts.textBlock("Update",
                            fontWeight: FontWeight.w800,
                            color: Colors.white,
                            size: 8),
                      ),
                    )
                  ],
                )
              ])),
        ),
      ],
    ));
  }

  static sendTransactionCard(SentTransaction transaction) {
    // Get date components from transaction date
    final day = transaction?.createdAt != null
        ? Utils.getDayFromDate(transaction!.createdAt!)
        : "13";

    final month = transaction?.createdAt != null
        ? Utils.getMonthFromDate(transaction!.createdAt!)
        : "May";
    return InkWell(
        child: Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
              width: 1.sw,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Row(children: [
                InkWell(
                  onTap: () {},
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.red),
                    child: Column(
                      children: [
                        Texts.textBlock(day,
                            fontWeight: FontWeight.w800,
                            color: Colors.white,
                            size: 13),
                        Texts.textBlock(month,
                            fontWeight: FontWeight.w300,
                            color: Colors.white,
                            size: 10),
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  width: 7.0,
                ),
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Texts.textBlock("Payment to",
                            fontWeight: FontWeight.w300,
                            color: Colors.black54,
                            size: 10),
                        Texts.textBlock(transaction.recipientName ?? "",
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                            size: 13),
                      ],
                    ),
                  ),
                ),
                Texts.textBlock("-${transaction.amount}USD",
                    fontWeight: FontWeight.w800, color: Colors.black, size: 13),
              ])),
        ),
      ],
    ));
  }

  static receivedTransactionCard(SentTransaction transaction) {
    // Get date components from transaction date
    final day = transaction?.createdAt != null
        ? Utils.getDayFromDate(transaction!.createdAt!)
        : "13";

    final month = transaction?.createdAt != null
        ? Utils.getMonthFromDate(transaction!.createdAt!)
        : "May";
    return InkWell(
        child: Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
              width: 1.sw,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Row(children: [
                InkWell(
                  onTap: () {},
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.green),
                    child: Column(
                      children: [
                        Texts.textBlock(day,
                            fontWeight: FontWeight.w800,
                            color: Colors.white,
                            size: 13),
                        Texts.textBlock(month,
                            fontWeight: FontWeight.w300,
                            color: Colors.white,
                            size: 10),
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  width: 7.0,
                ),
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Texts.textBlock("Payment from",
                            fontWeight: FontWeight.w300,
                            color: Colors.black54,
                            size: 10),
                        Texts.textBlock(transaction.recipientFrom ?? "",
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                            size: 13),
                      ],
                    ),
                  ),
                ),
                Texts.textBlock("+${transaction.amount}USD",
                    fontWeight: FontWeight.w800, color: Colors.black, size: 13),
              ])),
        ),
      ],
    ));
  }

  static expenseCard(Expense expense) {
    // Get date components from transaction date
    final day = expense?.expenseDate != null
        ? Utils.getDayFromDate(expense!.expenseDate!)
        : "13";

    final month = expense?.expenseDate != null
        ? Utils.getMonthFromDate(expense!.expenseDate!)
        : "May";
    return InkWell(
        child: Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
              width: 1.sw,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Row(children: [
                InkWell(
                  onTap: () {},
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.red),
                    child: Column(
                      children: [
                        Texts.textBlock(day,
                            fontWeight: FontWeight.w800,
                            color: Colors.white,
                            size: 13),
                        Texts.textBlock(month,
                            fontWeight: FontWeight.w300,
                            color: Colors.white,
                            size: 10),
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  width: 7.0,
                ),
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Texts.textBlock("Expense Type",
                            fontWeight: FontWeight.w300,
                            color: Colors.black54,
                            size: 10),
                        Texts.textBlock(expense.expenseType ?? "",
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                            size: 13),
                      ],
                    ),
                  ),
                ),
                Texts.textBlock("\$${expense.expenseAmount}",
                    fontWeight: FontWeight.w800, color: Colors.black, size: 13),
              ])),
        ),
      ],
    ));
  }

  static financeCard() {
    return InkWell(
        child: Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
              width: 1.sw,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Row(children: [
                InkWell(
                  onTap: () {},
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.lightGreen),
                    child: Column(
                      children: [
                        Texts.textBlock("13",
                            fontWeight: FontWeight.w800,
                            color: Colors.white,
                            size: 13),
                        Texts.textBlock("May",
                            fontWeight: FontWeight.w300,
                            color: Colors.white,
                            size: 10),
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  width: 7.0,
                ),
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Texts.textBlock("Invoice 23232323232",
                            fontWeight: FontWeight.w300,
                            color: Colors.black54,
                            size: 10),
                        SizedBox(
                          height: 3,
                        ),
                        Texts.textBlock("Paypal",
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                            size: 13),
                      ],
                    ),
                  ),
                ),
                Column(
                  children: [
                    Texts.textBlock("\$3334",
                        fontWeight: FontWeight.w800,
                        color: Colors.black,
                        size: 13),
                    SizedBox(
                      height: 3,
                    ),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                      decoration: BoxDecoration(
                          color: Colors.green.shade100,
                          borderRadius: BorderRadius.circular(5)),
                      child: Texts.textBlock("Paid",
                          fontWeight: FontWeight.w800,
                          color: Colors.green,
                          size: 8),
                    )
                  ],
                )
              ])),
        ),
      ],
    ));
  }

  static billsCard(
      {required Callback? onTap,
      String? title,
      String? date,
      String? status,
      String? billingNo,
      String? amount}) {
    // Get date components from transaction date
    final day = date != null ? Utils.getDayFromDate(date) : "13";

    final month = date != null ? Utils.getMonthFromDate(date) : "May";
    return InkWell(
        child: ClipRRect(
      borderRadius: BorderRadius.circular(10),
      child: Container(
          width: 1.sw,
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: Colors.white,
          ),
          child: Row(children: [
            InkWell(
              onTap: () {},
              child: Container(
                padding: EdgeInsets.symmetric(
                    horizontal: 15,
                    vertical: status?.toLowerCase() == "paid" ? 10 : 5),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: status?.toLowerCase() == "paid"
                        ? Colors.green
                        : Colors.red),
                child: Column(
                  children: [
                    Texts.textBlock(day,
                        fontWeight: FontWeight.w800,
                        color: Colors.white,
                        size: 13),
                    Texts.textBlock(month,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                        size: 12),
                    status?.toLowerCase() == "paid"
                        ? SizedBox.shrink()
                        : Texts.textBlock(
                            status?.toLowerCase() == "paid" ? "Paid" : "Due",
                            fontWeight: FontWeight.w300,
                            color: Colors.white,
                            size: 10),
                  ],
                ),
              ),
            ),
            const SizedBox(
              width: 7.0,
            ),
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 5.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Texts.textBlock(title ?? " ",
                        fontWeight: FontWeight.w800,
                        color: Colors.black,
                        size: 13),
                    SizedBox(
                      height: 2,
                    ),
                    Texts.textBlock(billingNo ?? " ",
                        fontWeight: FontWeight.w300,
                        color: Colors.black,
                        size: 10),
                    SizedBox(
                      height: 5,
                    ),
                  ],
                ),
              ),
            ),
            Column(children: [
              Texts.textBlock("\$ ${amount ?? "0.00"}",
                  fontWeight: FontWeight.w800, color: Colors.black, size: 13),
              SizedBox(
                height: 5,
              ),
              status?.toLowerCase() == "paid"
                  ? Texts.textBlock("Paid",
                      fontWeight: FontWeight.w300,
                      color: Colors.green,
                      size: 12)
                  : GestureDetector(
                      onTap: onTap,
                      child: Text("Pay Now",
                          style: TextStyle(
                              fontWeight: FontWeight.w800,
                              color: ColorConstants.secondaryColor,
                              fontSize: 12,
                              decoration: TextDecoration.underline)),
                    )
            ])
          ])),
    ));
  }

  static Column columTexts({required String title, required String value}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Texts.textMedium(title, size: 13, color: Colors.black54),
        Widgets.heightSpaceH05,
        Texts.textBlock(value, size: 17, maxline: 3, color: Colors.black),
      ],
    );
  }

  static textTapped(
      {required String? title, required String? subTitle, required var onTap}) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10.0),
        child: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: title ?? "",
                style: const TextStyle(
                  fontFamily: "RobotoRegular",
                  fontWeight: FontWeight.w600,
                  fontSize: 13,
                  color: Colors.black, // Change the color as needed
                ),
              ),
              TextSpan(
                text: " - ${subTitle ?? ""}",
                style: const TextStyle(
                  fontFamily: "RobotoRegular",
                  fontWeight: FontWeight.w400,
                  fontSize: 13,
                  color: Colors.black87, // Change the color as needed
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
