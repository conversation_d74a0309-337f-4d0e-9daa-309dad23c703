import 'package:ensuram/core/widgets/text_widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../constants/color_constants.dart';

class CustomCheckbox extends StatelessWidget {
  CustomCheckbox({super.key, required this.value, required this.callBack ,required this.label});
  bool? value;
  var callBack;String? label;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Transform.scale(
          scale: 1, // Adjust the scale factor as needed
          child: Checkbox(fillColor: MaterialStateProperty.resolveWith((states) {
            // Check for inactive state
            if (states.contains(MaterialState.disabled) || !states.contains(MaterialState.selected)) {
              return Colors.white; // Inactive color
            }
            return ColorConstants.primaryColor; // Active color (optional, same as above)
          }),
              activeColor: Colors.white,
              value: value, // Example value
              onChanged: callBack),
        ),Texts.textBlock(label??"",size: 12,color: Colors.white)
      ],
    );
  }
}