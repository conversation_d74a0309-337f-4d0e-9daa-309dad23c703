import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../constants/color_constants.dart';

class Texts {
  static textBold(String label,
      {double? size, Color? color, FontWeight? fontWeight,TextAlign? textAlign}) {
    return Text(
      label,
      style: TextStyle(
          fontSize: size ?? 18.0,
          fontFamily: "OswaldBold",
          fontWeight: fontWeight ?? FontWeight.bold,
          color: color ?? Colors.black,
          letterSpacing: .3),textAlign: textAlign??TextAlign.start,
    );
  }

  static textNormal(String label,
      {double? size, Color? color, String? fontFamily,}) {
    return Text(
      label,
      style: TextStyle(
        fontSize: size ?? 16.0,
        fontFamily: fontFamily??"LatoLight",
        color: color ?? Colors.black,
      ),textAlign:TextAlign.center
    );
  }

  static textMedium(String label,
      {double? size, Color? color, String? fontFamily,}) {
    return Text(
      label,
      style: TextStyle(
        fontSize: size ?? 18.0,
        fontFamily: "OswaldMedium",
        color: color ?? Colors.black,
      ),
    );
  }

  static TextUrbanistCenter(String label,
      {double? size,
      Color? color,
      FontWeight? fontWeight,
      String? fontFamily}) {
    return Text(
      label,
      style: TextStyle(
        fontSize: size ?? 18.0,
        fontFamily: fontFamily ?? "",
        fontWeight: fontWeight ?? FontWeight.bold,
        color: color ?? Colors.black,
      ),
      textAlign: TextAlign.center,
    );
  }

  static textBlock(String label,
      {double? size,
      Color? color,
      FontWeight? fontWeight,
      String? fontFamily,
      var overflow,
      int? maxline,
      var align}) {
    return Text(
      label,
      style: TextStyle(
          fontSize: size ?? 18.0,
          fontFamily: fontFamily ?? "LatoRegular",
          fontWeight: fontWeight ?? FontWeight.bold,
          color: color ?? Colors.black),
      overflow: overflow ?? TextOverflow.ellipsis,
      textAlign: align ?? TextAlign.start,
      maxLines: maxline ?? 1,
    );
  }

  static textUnderlineBlock(String label,
      {double? size,
      Color? color,
      FontWeight? fontWeight,
      String? fontFamily,TextAlign? align,
      var overflow,
      int? maxline,
      bool? underline}) {
    return Text(
      label,
      style: TextStyle(
          decoration: TextDecoration.underline,decorationColor: Colors.white,
          fontSize: size ?? 18.0,
          fontFamily: fontFamily ?? "Roboto",
          fontWeight: fontWeight ?? FontWeight.bold,
          color: color ?? Colors.white),
      overflow: overflow ?? TextOverflow.ellipsis,textAlign:align??TextAlign.center ,
      maxLines: maxline ?? 1,
    );
  }
}
