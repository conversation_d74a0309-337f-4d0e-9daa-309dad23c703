import 'package:ensuram/core/routes/app_routes.dart';
import 'package:ensuram/core/widgets/text_widgets.dart';
import 'package:ensuram/core/widgets/widgets.dart';
import 'package:ensuram/view/modules/pre_authorization/view/pre_authorization_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';

import '../../view/dashboard/controller/dashboard_controller.dart';
import '../constants/assets_constants.dart';
import '../constants/color_constants.dart';

class DrawerSection extends StatefulWidget {
  @override
  State<DrawerSection> createState() => _DrawerSectionState();
}

class _DrawerSectionState extends State<DrawerSection> {
  DashboardController bottomNavController = Get.find();

  String? selectedLanguage = "en";

  menuItemBox({required var icon, required String title, required var onTap}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 13),
        child: Row(
          children: [
            Image.asset(
              icon,
              width: 20,
              height: 20,
              color: Colors.white,
            ),
            const SizedBox(
              width: 10,
            ),
            Expanded(
                child: Texts.textBlock(title,
                    size: 13,
                    color: Colors.white,
                    fontWeight: FontWeight.w400)),
            const SizedBox(
              width: 8,
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.white,
              size: 10,
            )
          ],
        ),
      ),
    );
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
        borderRadius: const BorderRadius.only(topLeft: Radius.circular(50.0)),
        child: Drawer(
            width: .70.sw,
            backgroundColor: ColorConstants.blackColor,
            shadowColor: Colors.white,
            elevation: 20,
            child: SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 20),
                child: Column(
                  children: [
                    Image.asset(
                      Assets.primaryLogo,
                      width: 150,
                      height: 80,
                      color: Colors.white,
                    ),
                    menuItemBox(
                        icon: Assets.identityIcon,
                        title: "Identity Verification",
                        onTap: () {
                          Get.back();
                          bottomNavController.changeTabIndex(0);
                        }),
                    menuItemBox(
                        icon: Assets.peopleIcon,
                        title: "Beneficiary",
                        onTap: () {
                          Get.toNamed(AppRoutes.userBeneficiariesList);
                        }),
                    menuItemBox(
                        icon: Assets.authorizedIcon,
                        title: "Pre-Authorization",
                        onTap: () {
                           Get.to(() => PreAuthorizationView());
                        }),
                    menuItemBox(
                        icon: Assets.personIcon,
                        title: "Profile",
                        onTap: () {
                          Get.back();
                          bottomNavController.changeTabIndex(3);
                        }),
                    menuItemBox(
                        icon: Assets.appointmentIcon,
                        title: "Appointment",
                        onTap: () {
                          Get.back();
                          bottomNavController.changeTabIndex(1);
                        }),
                    menuItemBox(
                        icon: Assets.clipboardTickIcon,
                        title: "Bills",
                        onTap: () {
                          Get.toNamed(AppRoutes.userBills);
                        }),
                    menuItemBox(
                        icon: Assets.addItemIcon,
                        title: "Contributions",
                        onTap: () {
                          Get.toNamed(AppRoutes.contributions);
                        }),
                    menuItemBox(
                        icon: Assets.moneySendIcon,
                        title: "Expense Tracker",
                        onTap: () {
                          Get.toNamed(AppRoutes.userExpenses);
                        }),
                    menuItemBox(
                        icon: Assets.walletIcon,
                        title: "E-Health Wallet",
                        onTap: () {
                          Get.back();
                          bottomNavController.changeTabIndex(0);
                        }),
                    menuItemBox(
                        icon: Assets.shopIcon,
                        title: "Shop",
                        onTap: () {
                          Get.back();
                          bottomNavController.changeTabIndex(2);
                        }),
                    menuItemBox(
                        icon: Assets.financeIcon,
                        title: "Finances",
                        onTap: () {
                          Get.toNamed(AppRoutes.userFinances);
                        }),
                    Widgets.heightSpaceH1,
                  ],
                ))));
  }
}
