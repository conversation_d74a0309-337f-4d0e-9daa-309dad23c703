import 'package:ensuram/core/widgets/widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../constants/assets_constants.dart';
import '../constants/color_constants.dart';

class RoundBody extends StatelessWidget {
  RoundBody( {
    super.key,this.widget,this.isBack
  });
  Widget? widget;bool? isBack;
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Column(
          children: [
            Widgets.heightSpaceH3,
            Image.asset(
              Assets.primaryLogo,
              width: 180,
            ),
            Widgets.heightSpaceH4,
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                    color: ColorConstants.secondaryGreyColor,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    )),
                child: widget,
              ),
            )
          ],
        ),Positioned(
            top: 48,left: 15,
            child: isBack??false?InkWell(onTap: ()=>Get.back(),
                child: Icon(Icons.arrow_back)):SizedBox.shrink())
      ],
    );
  }
}
