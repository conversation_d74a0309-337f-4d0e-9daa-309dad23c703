class Endpoints {
  static const domain = "https://ensuram.com";
  static const baseURL = '$domain/api/';
  static const utilsBaseUrl = 'https://countrycityservice.xcodie.com/';

  static const register = 'user/register';
  static const login = 'user/login';
  static const userInfo = 'user/get-user';
  static const forgotPassword = 'user/forgot-password';
  static const resetPassword = 'user/reset-password';
  static const confirmEmail =
      'user/confirm-email'; // Add token as query parameter
  static const updateProfile =
      'user/update-profile'; // Add token as query parameter
  static const updatePassword =
      'user/update-password'; // Add token as query parameter
  static const updateLocation =
      'user/update-location'; // Add token as query parameter
  static const cities = 'api/cities';
  static const countries = 'api/countries';
  static const states = 'api/states';
  static const userVerification = 'user/update-verification';
  static const getTransactions =
      'user/get-user-saving-transactions'; // Add token as query parameter
  static const sentTransactions =
      'user/sent-and-received-transactions'; // Add token as query parameter
  static const sendPayment = 'user/send-money'; // Add token as query parameter

  static const addPaypalPayment =
      'user/paypal_payment'; // Add token as query parameter

  static const getExpenses =
      'user/get-expenses'; // Add token as query parameter
  static const getLabTestOrders =
      'user/get-lab-test-orders'; // Add token as query parameter
  static const getMedicationOrders =
      'user/get-medication-orders'; // Add token as query parameter
  static const getInsuranceOrders =
      'user/get-insurance-orders'; // Add token as query parameter
  static const getOrgInvites =
      'user/get-org-invitees'; // Add token as query parameter
  static const getUserOrganizations =
      'user/get-user-organizations'; // Add token as query parameter
  static const actionOrganization =
      'user/accept-decline-organization'; // Add token as query parameter
  static const getUserOrganization =
      'user/get-user-organization'; // Add token as query parameter
  static const getUserBeneficiaries =
      'user/get-all-user-beneficiaries'; // Add token as query parameter
  static const getUserAppointments =
      'user/get-appointments'; // Add token as query parameter

  static const getUserBeneficiariesEligible = 'user/get-user-eligible-beneficiaries';
  static const subscribeToAppointment =
      "user/subscribe-to-appointment-scheduler"; // Add token as query parameter

  static const deleteBeneficiary =
      'user/delete-beneficiary'; // Add token as query parameter
  static const updateBeneficiary =
      'user/update-beneficiary'; // Add token as query parameter
  static const addBeneficiary =
      'user/add-beneficiary'; // Add token as query parameter
  static const getInsuranceBills =
      'user//get-user-insurance-bills'; // Add token as query parameter
  static const getLabBills =
      'user/get-user-labs-bills'; // Add token as query parameter
  static const getHospitalBills =
      'user/get-user-hospital-bills'; // Add token as query parameter
  static const getPharmacyBills =
      'user/get-user-pharmacy-bills'; // Add token as query parameter
  static const payInsuranceBill =
      'user/pay-insurance-bill'; // Add token as query parameter
  static const payLabBill =
      'user/pay-labs-bill'; // Add token as query parameter
  static const payHospitalBill =
      'user/pay-hospital-bill'; // Add token as query parameter
  static const payPharmacyBill =
      'user/pay-pharmacy-bill'; // Add token as query parameter
  static const payBill = 'user/pay-bill'; // Add token as query parameter
  static const getHospitalsByCountry =
      'user/get-hospitals-by-country'; // Add token as query parameter
  static const getDoctorsByHospital =
      'user/get-doctors-by-hospital'; // Add token as query parameter
static const bookAppointment =
      'user/book-appointment'; // Add token as query parameter
  static const cancelAppointment =
      'user/cancel-appointment';
static const getDoctorOpeningHours =
      "user/get-doctor-opening-hours"; // Add token as query parameter

  static const editAppointment =
      'user/edit-appointment'; // Add token as query parameter


  static const getPreAuths =
      'user/get-preauths'; // Add token as query parameter
  static const updatePreAuth =
      'user/update-preauth'; // Add token as query parameter
  static const allocatePreAuth =
      'user/allocate-preauth'; // Add token as query parameter
static const getLabTests =
      'public/lab-tests'; // Add token as query parameter
  static const getLabTestsDetails =
      'public/lab-test-details'; // Add token as query parameter
  static const getLabTestsBalance =
      'check-lab-test-balance'; // Add token as query parameter
static const bookLabTest =
      'user/book-lab-test'; // Add token as query parameter
  static const searchInsurances =
      'user/search-insurance';
  static const getInsuranceDetail =
      'user/get-insurance-details';
  static const getProviders =
      'user/get-active-insurances';
  static const buyInsurances =
      'user/buy-insurance';


}
