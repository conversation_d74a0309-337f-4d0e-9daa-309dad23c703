import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_paypal/flutter_paypal.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import '../widgets/widgets.dart';

class PayPalService {
  // PayPal Sandbox Credentials - Replace with your own for production
  static const String clientId = 'YOUR_PAYPAL_CLIENT_ID';
  static const String secret = 'YOUR_PAYPAL_SECRET';
  static const String returnURL = 'com.rixxsol.enusram://paypalpay';
  static const String cancelURL = 'com.rixxsol.enusram://cancel';
  static const bool sandbox = true; // Set to false for production

  // Process payment with PayPal



}