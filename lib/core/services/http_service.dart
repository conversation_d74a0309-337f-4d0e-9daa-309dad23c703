import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:get/get.dart';

import '../../controller/user_controller.dart';

import '../../model/service_model.dart';
import '../constants/api_endpoints.dart';
import '../widgets/widgets.dart';

class ApiService {
  static Dio dioService = Dio();

  static Future<ServiceResponseModel> getData(String endpoint) async {
    final userController = Get.find<UserController>();

    try {
      final response = await dioService.get(
        '${Endpoints.baseURL}$endpoint',
        options: Options(
            headers: { 'Accept': 'application/json; charset=UTF-8',"Authorization": "Bearer ${userController.token}"}),
      );

      log("url : ${Endpoints.baseURL}$endpoint");
      log("response: ${response.data.toString()}");

      return ServiceResponseModel(
        status: response.statusCode == 200,
        message:
            response.statusCode == 200 ? "Success" : "Something went wrong",
        data: response.statusCode == 200 ? response.data : "",
      );
    } on DioException catch (e) {
      log("url : ${Endpoints.baseURL}$endpoint");
      log("response: ${e.response?.data['message'].toString()}");

      if (e.toString().contains("401")) {
        userController.logout();
        Widgets.showSnackBar("Error", "Token is expired");
        return ServiceResponseModel(
          status: false,
          message: "Something went wrong",
          data: "",
        );
      } else {
        return ServiceResponseModel(
          status: false,
          message:e.response?.data['message']??"",
          data: "",
        );
      }
    }
  }
  static Future<ServiceResponseModel> getDataLocation(String endpoint) async {
    final userController = Get.find<UserController>();

    try {
      final response = await dioService.get(
        '${Endpoints.utilsBaseUrl}$endpoint',
        options: Options(
            headers: { 'Accept': 'application/json; charset=UTF-8',"Authorization": "Bearer ${userController.token}"}),
      );

      log("url : ${Endpoints.utilsBaseUrl}$endpoint");
      log("response: ${response.data.toString()}");

      return ServiceResponseModel(
        status: response.statusCode == 200,
        message:
        response.statusCode == 200 ? "Success" : "Something went wrong",
        data: response.statusCode == 200 ? response.data : "",
      );
    } on DioException catch (e) {
      log("url : ${Endpoints.baseURL}$endpoint");
      log("response: ${e.response?.data['message'].toString()}");

      if (e.toString().contains("401")) {
        userController.logout();
        Widgets.showSnackBar("Error", "Token is expired");
        return ServiceResponseModel(
          status: false,
          message: "Something went wrong",
          data: "",
        );
      } else {
        return ServiceResponseModel(
          status: false,
          message:e.response?.data['message']??"",
          data: "",
        );
      }
    }
  }

  static Future<ServiceResponseModel> putData(
      String endpoint, dynamic data) async {
    final userController = Get.find<UserController>();
    try {
      final response = await dioService.put(
        '${Endpoints.baseURL}$endpoint',
        data: data,
        options: Options(headers: {
          'Accept': 'application/json; charset=UTF-8',
          "Authorization": "Bearer ${userController.token}",
        }),
      );
      log("url : ${Endpoints.baseURL}$endpoint");
      log("payload: $data");
      log("response: ${response.data}");

      final responseData = response.data;

      if (responseData['status'] == "success") {
        return ServiceResponseModel(
          status: true,
          message: responseData["message"],
          data: responseData,
        );
      } else {
        return ServiceResponseModel(
          status: false,
          message: responseData["message"],
          data: "",
        );
      }
    } on DioException catch (e) {log("url : ${Endpoints.baseURL}$endpoint");
    log("payload: $data");
    log("response: ${e.response?.data.toString()}");


    return ServiceResponseModel(
      status: false,
      message:e.response?.data['message']??"",
      data: "",
    );

    }
  }
  static Future<ServiceResponseModel> postData(
      String endpoint, dynamic data) async {
    final userController = Get.find<UserController>();
    try {
      final response = await dioService.post(
        '${Endpoints.baseURL}$endpoint',
        data: jsonEncode(data),
        options: Options(headers: {
          'Accept': 'application/json; charset=UTF-8',
          "Authorization": "Bearer ${userController.token}",
        }),
      );
      log("url : ${Endpoints.baseURL}$endpoint");
      log("payload: $data");
      log("response: ${response.data}");

      final responseData = response.data;

      if (responseData['status'] == "success") {
        return ServiceResponseModel(
          status: true,
          message: responseData["message"],
          data: responseData,
        );
      } else {
        return ServiceResponseModel(
          status: false,
          message: responseData["message"],
          data: responseData,
        );
      }
    } on DioException catch (e) {log("url : ${Endpoints.baseURL}$endpoint");
    log("payload: $data");
    log("response: ${e.response?.data.toString()}");


      return ServiceResponseModel(
        status: false,
        message:e.response?.data['message']??"",
        data: "",
      );

    }
  }

}
