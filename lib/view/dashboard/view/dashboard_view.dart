import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:get/get.dart';

import '../../../../../core/constants/assets_constants.dart';
import '../../../../../core/constants/color_constants.dart';
import '../../../../../core/widgets/drawer_section.dart';
import '../../../../../core/widgets/text_widgets.dart';
import '../../../controller/user_controller.dart';
import '../../../core/constants/api_endpoints.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/widgets/widgets.dart';
import '../controller/dashboard_controller.dart';

class UserDashboardView extends StatelessWidget {
  UserDashboardView({super.key});

  final DashboardController bottomNavController =
      Get.put(DashboardController());

  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: scaffoldKey,
      endDrawer: DrawerSection(),
      appBar: AppBar(
        backgroundColor: ColorConstants.backgroundColor,
        automaticallyImplyLeading: false,
        elevation: 0,
        title: GetBuilder<UserController>(
          init: UserController(),
          builder: (controller) {
            return Row(
              children: [
                GetBuilder<UserController>(
                  builder: (profileController) {
                    return AdvancedAvatar(
                      animated: true,
                      size: 30,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: ColorConstants.secondaryColor,
                        border: Border.all(
                          color: Colors.grey,
                          width: 0.0,
                        ),
                      ),
                      child: controller.userModel?.userPhoto == null
                          ? Text(
                              controller.userModel!.userFirstname!.substring(
                                0,
                                1,
                              ),
                              style: TextStyle(color: Colors.white),
                            )
                          : Widgets.networkImage(
                              "${Endpoints.domain}/customer/${profileController.userModel?.userId}/profile/${profileController.userModel?.userPhoto}",
                              width: 90,
                              height: 90,
                            ),
                    );
                  },
                ),
                SizedBox(
                  width: 5,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Texts.textNormal(
                          "Hey,  ",
                          size: 15,
                        ),
                        Texts.textBlock(
                            controller.userModel?.userFirstname ?? "",
                            color: Colors.black,
                            size: 15),
                      ],
                    ),
                    SizedBox(
                      height: 2,
                    ),
                    Texts.textBlock(
                        " ENS-${controller.userModel?.userId ?? ""}",
                        color: Colors.black54,
                        size: 11)
                  ],
                )
              ],
            );
          },
        ),
        actions: [
          GestureDetector(
            onTap: () {
              Get.toNamed(AppRoutes.userNotifications);
            },
            child: Icon(
              CupertinoIcons.bell,
              size: 20,
            ),
          ),
          SizedBox(
            width: 10,
          ),
          InkWell(
            onTap: () {
              scaffoldKey.currentState?.openEndDrawer();
            },
            child: Icon(
              Icons.menu,
              size: 25,
            ),
          ),
          SizedBox(
            width: 10,
          ),
        ],
      ),

      backgroundColor: ColorConstants.backgroundColor,
      bottomNavigationBar: Obx(
        () => BottomNavigationBar(
          backgroundColor: ColorConstants.secondaryGreyColor,
          type: BottomNavigationBarType.fixed,
          unselectedItemColor: Colors.white60,
          onTap: (index) => bottomNavController.changeTabIndex(index),
          selectedItemColor: Colors.white,
          selectedLabelStyle: const TextStyle(
              fontSize: 11, fontWeight: FontWeight.bold, color: Colors.white),
          unselectedLabelStyle: const TextStyle(
              fontSize: 11.0,
              fontFamily: "Roboto",
              fontWeight: FontWeight.bold,
              color: Colors.white70),
          currentIndex: bottomNavController.currentIndex.value,
          items: bottomNavController.menuOptions
              .map(
                (tab) => BottomNavigationBarItem(
                  icon: Icon(
                    tab['icon'],
                    color: Colors.white60,
                    size: 26.0,
                  ),
                  activeIcon: Icon(
                    tab['icon'],
                    color: Colors.white,
                    size: 26.0,
                  ),
                  label: tab['name'],
                ),
              )
              .toList(),
        ),
      ),

      // Reactive Body (updates with Obx)
      body: Obx(
        () => bottomNavController
            .menuPages[bottomNavController.currentIndex.value],
      ),
    );
  }
}
