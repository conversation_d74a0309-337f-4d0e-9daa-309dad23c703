
import 'package:ensuram/view/modules/appointment/view/my_appointments_view.dart';
import 'package:ensuram/view/modules/profile/view/profile_view.dart';
import 'package:ensuram/view/modules/shop/view/shop_view.dart';
import 'package:ensuram/view/modules/wallets/view/wallet_view.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

import '../../../controller/user_controller.dart';



class DashboardController extends GetxController {
  RxInt currentIndex = 0.obs;

  @override
  void onInit() {
    super.onInit();

  }
  final List<Map<String, dynamic>> menuOptions = [
    {'icon': Icons.home, 'name': 'Dashboard'},
    {'icon': Icons.calendar_month, 'name': 'Appointment'},
    {'icon': Icons.shopping_cart, 'name': 'Shop'},
    {'icon': Icons.person, 'name': 'Profile'},
  ];

  final List<Widget> menuPages = [
    WalletView(),
    MyAppointmentsView(),
    ShopView(),
    ProfileView(),
  ];

  void changeTabIndex(int index) {
    currentIndex.value = index;
  }
}
