import 'package:animated_splash_screen/animated_splash_screen.dart';
import 'package:ensuram/view/dashboard/view/dashboard_view.dart';
import 'package:ensuram/view/starting/boarding_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_navigation/src/root/get_material_app.dart';

import '../../controller/user_controller.dart';
import '../../core/constants/assets_constants.dart';
import '../../core/constants/color_constants.dart';
import '../../core/routes/app_routes.dart';

class SplashView extends StatefulWidget {
  const SplashView({super.key});

  @override
  State<SplashView> createState() => _SplashViewState();
}

class _SplashViewState extends State<SplashView> {


  late UserController userController;
  @override
  void initState() {
    super.initState();
    userController=Get.put(UserController());

    userController.navigateToNextScreen();
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(360, 690),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return GetMaterialApp(

            getPages: AppRoutes.routes,
            builder: EasyLoading.init(),
            debugShowCheckedModeBanner: false,
            title: 'Ensuram',
            theme: ThemeData(
                fontFamily: 'LatoRegular',
                primaryColor: ColorConstants.primaryBlackColor,
                scaffoldBackgroundColor: ColorConstants.backgroundColor),
            home: Scaffold(
                backgroundColor: ColorConstants.backgroundColor,
                body:  Center(
                  child: Image(
                    width: 300,
                    image: AssetImage(Assets.primaryLogo),
                  ),
                )));
      },
    );
  }
}
