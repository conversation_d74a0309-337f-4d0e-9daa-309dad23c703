import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/constants/padding_constants.dart';
import 'package:ensuram/core/routes/app_routes.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../core/widgets/round_body.dart';
import '../../core/widgets/text_widgets.dart';

class BoardingView extends StatelessWidget {
  const BoardingView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: RoundBody(
          widget: Padding(
            padding: PaddingConstants.screenPadding,
            child: Column(
              children: [
                Widgets.heightSpaceH4,
                Texts.textBold(
                    "Connecting Africans home and abroad to Africa’s health marketplace for seamless digital health payments and savings via individual and employer contributions.",
                    color: Colors.white,
                    textAlign: TextAlign.center,
                    size: 24),
                Widgets.heightSpaceH2,
                Texts.textNormal(
                    "Reduce your medical expenses in Africa by 50% with Ensuram.",
                    color: Colors.white,
                    size: 14),
                Widgets.heightSpaceH2,
                CustomButton(
                  label: "LOGIN INTO YOUR ACCOUNT",
                  onTap: () {
                    Get.toNamed(AppRoutes.userLogin);
                  },
                  color: ColorConstants.whiteColor,
                  textColor: Colors.black87,
                ),
                Widgets.heightSpaceH1,
                CustomButton(
                  label: "CREATE ACCOUNT",
                  onTap: () {
                    Get.toNamed(AppRoutes.userRegistration);
                  },
                  color: ColorConstants.primaryColor,
                ),
                Widgets.heightSpaceH3,
                Divider(),
                Widgets.heightSpaceH2,
                Texts.textUnderlineBlock(
                    "Join Us\nas Health Insurer, Hospital, Pharmacy, Lab and Organization",
                    color: Colors.white,
                    maxline: 3,
                    size: 12),
                Spacer(),
                Texts.textNormal(
                    "© 2024 Ensuram - Beta Version. All rights reserved.",
                    color: Colors.white,
                    size: 11),
                Widgets.heightSpaceH2,
              ],
            ),
          ),
        ),
      ),
    );
  }

  void showLoginOptions(BuildContext context) {
    showModalBottomSheet(
      backgroundColor: Colors.white,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Texts.textBold("   LOGIN AS"),
              SizedBox(height: 10),
              TextButton(
                onPressed: () {
                  Get.toNamed(AppRoutes.userLogin);
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Texts.textMedium('Diaspora | African resident',
                        color: ColorConstants.secondaryColor, size: 15),
                    Icon(
                      Icons.arrow_right_alt,
                      color: Colors.black38,
                    )
                  ],
                ),
              ),
              Divider(
                color: Colors.black12,
              ),
              TextButton(
                onPressed: () {},
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Texts.textMedium('Insurance',
                        color: ColorConstants.secondaryColor, size: 15),
                    Icon(
                      Icons.arrow_right_alt,
                      color: Colors.black38,
                    )
                  ],
                ),
              ),
              Divider(
                color: Colors.black12,
              ),
              TextButton(
                onPressed: () {},
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Texts.textMedium('Hospital',
                        color: ColorConstants.secondaryColor, size: 15),
                    Icon(
                      Icons.arrow_right_alt,
                      color: Colors.black38,
                    )
                  ],
                ),
              ),
              Divider(
                color: Colors.black12,
              ),
              TextButton(
                onPressed: () {},
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Texts.textMedium('Pharmacy',
                        color: ColorConstants.secondaryColor, size: 15),
                    Icon(
                      Icons.arrow_right_alt,
                      color: Colors.black38,
                    )
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
