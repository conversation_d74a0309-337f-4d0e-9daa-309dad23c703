import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/widgets.dart';
import 'package:ensuram/view/modules/pharmacy/view/pharmacy_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../core/constants/color_constants.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../controller/pharmacy_controller.dart';
import 'add_to_cart_view.dart';

class MedicationDetailView extends StatefulWidget {
  const MedicationDetailView({super.key});

  @override
  State<MedicationDetailView> createState() => _MedicationDetailViewState();
}

class _MedicationDetailViewState extends State<MedicationDetailView> {
  PharmacyController controller = Get.find();
  RxInt quantity = 1.obs;


  @override
  initState() {
    super.initState();
    controller.fetchBenefiecieriesBackground();
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Medication Detail",
            size: 17, fontWeight: FontWeight.w700),
        actions: [],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                  height: .30.sh,
                  width: 1.sw,
                  decoration: BoxDecoration(
                    color: const Color(0xFF979797).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(15),
                    child: Widgets.networkImage(
                        controller.selectedMedication.value.image ?? ""),
                  )),
              Widgets.heightSpaceH2,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Texts.textBlock(
                      controller.selectedMedication.value.medName ?? "",
                      color: ColorConstants.primaryColor),
                  Row(
                    children: [
                      InkWell(
                        onTap: () {
                          if (quantity.value > 1) {
                            quantity.value--;
                          }
                        },
                        child: Icon(
                          Icons.remove_circle_outlined,
                          color: quantity.value > 1
                              ? ColorConstants.secondaryColor
                              : Colors.grey,
                        ),
                      ),
                      SizedBox(width: 5),
                      CircleAvatar(
                        radius: 14,
                        backgroundColor: Colors.white,
                        child: Obx(() =>
                            Texts.textMedium("${quantity.value}", size: 9)),
                      ),
                      SizedBox(width: 5),
                      InkWell(
                        onTap: () {
                          // Get the available stock
                          int availableStock = int.tryParse(
                                  controller.selectedMedication.value.stock ??
                                      "0") ??
                              0;

                          // Only increment if we haven't reached the stock limit
                          if (quantity.value < availableStock) {
                            quantity.value++;
                          } else {
                            // Show a message when trying to exceed stock
                            Widgets.showSnackBar("Stock Limit",
                                "Maximum available stock is $availableStock");
                          }
                        },
                        child: Obx(() {
                          // Get the available stock for the icon color
                          int availableStock = int.tryParse(
                                  controller.selectedMedication.value.stock ??
                                      "0") ??
                              0;

                          return Icon(Icons.add_circle_outlined,
                              color: quantity.value < availableStock
                                  ? ColorConstants.secondaryColor
                                  : Colors.grey);
                        }),
                      )
                    ],
                  )
                ],
              ),
              const SizedBox(height: 3),
              Text(
                "\$${controller.selectedMedication.value.price}",
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w800,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 3),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.location_on_outlined,
                    size: 17,
                    color: Colors.red,
                  ),
                  Texts.textBlock(
                      controller.selectedMedication.value.pharAddress ?? " ",
                      color: Colors.black,
                      fontWeight: FontWeight.w400,
                      size: 14),
                ],
              ),
              Widgets.heightSpaceH2,
              Texts.textBlock(controller.selectedMedicationDetail.value.description ?? " ",
                  color: Colors.black54, fontWeight: FontWeight.w300, size: 13),
              Widgets.heightSpaceH2,
              CustomDropdown(
                  onTap: () {showBeneficiariesBottomSheet(context);},
                  value: controller.selectedBeneficiary.value.beneficiaryId,
                  hint: "Select here",textColor: Colors.black,
                  label: "Beneficiary"),
              Widgets.heightSpaceH2,
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: controller.isFileSelected.value
                        ? Colors.green
                        : Colors.orange,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(8),
                  color: controller.isFileSelected.value
                      ? Colors.green.withOpacity(0.05)
                      : Colors.orange.withOpacity(0.05),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.upload_file,
                          color: controller.isFileSelected.value
                              ? Colors.green
                              : Colors.orange,
                          size: 20,
                        ),
                        SizedBox(width: 8),
                        Texts.textBlock("Upload Prescription",
                            color: Colors.black, fontWeight: FontWeight.w600, size: 13),
                        Text(" *", style: TextStyle(color: Colors.red, fontSize: 16)),
                      ],
                    ),
                    SizedBox(height: 8),
                    Texts.textMedium(
                      "A valid prescription is required to purchase this medication",
                      size: 11,
                      color: Colors.grey[600],
                    ),
                    SizedBox(height: 12),
                    Obx(() => CustomButton(
                      label: controller.isFileSelected.value
                          ? "✓ ${controller.selectedFileName.value}"
                          : "Browse file",
                      onTap: () {
                        controller.pickPrescriptionFile();
                      },
                      color: controller.isFileSelected.value
                          ? Colors.green.withOpacity(0.2)
                          : Colors.black12,
                    )),
                  ],
                ),
              ),
              if (controller.isFileSelected.value) ...[
                Widgets.heightSpaceH1,
                Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green, size: 16),
                    SizedBox(width: 8),
                    Expanded(
                      child: Texts.textMedium(
                        "Prescription uploaded successfully",
                        size: 12,
                        color: Colors.green,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        controller.clearSelectedFile();
                      },
                      child: Texts.textMedium("Change", size: 12, color: Colors.blue),
                    ),
                  ],
                ),
              ]
            ],
          ),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: Obx(() => CustomButton(
          color: _canAddToCart()
              ? ColorConstants.secondaryColor
              : Colors.grey,
          onTap: () {
            _handleAddToCart();
          },
          label: "Add to cart",
        )),
      ),
    );
  }

  void showBeneficiariesBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.8,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Column(
              children: [
                // Title
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Select Beneficiaries',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
                Divider(height: 1, color: Colors.grey[300]),
                // List of Beneficiaries
                Expanded(
                  child: Obx(() {
                    if (controller.benefieries.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.person_off,
                                size: 48, color: Colors.grey),
                            SizedBox(height: 16),
                            Text(
                              'No beneficiaries found',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      );
                    } else {
                      return ListView.builder(
                        controller: scrollController,
                        itemCount: controller.benefieries.length,
                        itemBuilder: (context, index) {
                          final beneficiary = controller.benefieries[index];
                          return ListTile(
                            leading: beneficiary.photo != null &&
                                    beneficiary.photo!.isNotEmpty
                                ? CircleAvatar(
                                    backgroundImage:
                                        NetworkImage(beneficiary.photo!),
                                    backgroundColor:
                                        ColorConstants.primaryColor,
                                  )
                                : CircleAvatar(
                                    backgroundColor:
                                        ColorConstants.primaryColor,
                                    child: Text(
                                      beneficiary.firstName?.substring(0, 1) ??
                                          "",
                                      style: TextStyle(color: Colors.white),
                                    ),
                                  ),
                            title: Text(
                              '${beneficiary.firstName ?? ""} ${beneficiary.lastName ?? ""}',
                              style:
                                  TextStyle(fontSize: 16, color: Colors.black),
                            ),
                            subtitle: Text(
                              beneficiary.beneficiaryId ?? 'No ID',
                              style:
                                  TextStyle(fontSize: 14, color: Colors.grey),
                            ),
                            trailing: Icon(
                              Icons.chevron_right,
                              color: Colors.grey,
                            ),
                            onTap: () {
                              controller.selectedBeneficiary.value =
                                  beneficiary;

                              Get.back();
                            },
                          );
                        },
                      );
                    }
                  }),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Helper method to check if user can add to cart
  bool _canAddToCart() {
    return controller.selectedBeneficiary.value.beneficiaryId != null &&
           controller.selectedBeneficiary.value.beneficiaryId!.isNotEmpty &&
           controller.isPrescriptionFileSelected();
  }

  // Handle add to cart with validation
  void _handleAddToCart() {
    // Check if beneficiary is selected
    if (controller.selectedBeneficiary.value.beneficiaryId == null ||
        controller.selectedBeneficiary.value.beneficiaryId!.isEmpty) {
      Widgets.showSnackBar(
        "Beneficiary Required",
        "Please select a beneficiary before adding to cart"
      );
      showBeneficiariesBottomSheet(context);
      return;
    }

    // Check if prescription file is uploaded
    if (!controller.isPrescriptionFileSelected()) {
      Widgets.showSnackBar(
        "Prescription Required",
        "Please upload a prescription file before adding to cart"
      );
      return;
    }

    // All validations passed, add to cart
    controller.addToCart({
      'med_id': controller.selectedMedication.value.medId,
      'quantity': quantity.value,
      'beneficiary_id': controller.selectedBeneficiary.value.beneficiaryId,
      // Note: File upload would typically be handled separately
      // as it requires multipart form data
    });
  }
}
