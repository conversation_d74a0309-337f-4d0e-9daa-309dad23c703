import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/widgets.dart';
import 'package:ensuram/view/modules/pharmacy/view/pharmacy_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../core/constants/color_constants.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/text_widgets.dart';
import 'add_to_cart_view.dart';

class MedicationDetailView extends StatefulWidget {
  const MedicationDetailView({super.key});

  @override
  State<MedicationDetailView> createState() => _MedicationDetailViewState();
}

class _MedicationDetailViewState extends State<MedicationDetailView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Medication Detail",
            size: 17, fontWeight: FontWeight.w700),
        actions: [],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

          Container(
            height: .30.sh,
            width: 1.sw,
            decoration: BoxDecoration(
              color: const Color(0xFF979797).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: Image.network(
                  demoProducts.first.images[0],
                  fit: BoxFit.cover,
                )),
          ),
       Widgets.heightSpaceH2,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Texts.textBlock("Paracetamol 250",
                  color: ColorConstants.primaryColor),
              Row(
                children: [
                  Icon(Icons.remove_circle_outlined,color: ColorConstants.secondaryColor,),SizedBox(width: 5,),
                  CircleAvatar(

                    radius: 14,
                    backgroundColor: Colors.white,
                    child: Texts.textMedium("1",size: 9),
                  ),SizedBox(width: 5,),
                  Icon(Icons.add_circle_outlined,color: ColorConstants.secondaryColor)
                ],
              )

            ],
          ),

          const SizedBox(height: 3), Text(
              "\$${ demoProducts.first.price}",
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w800,
                color: Colors.black,
              ),
            ), const SizedBox(height: 3),
          Row(crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.location_on_outlined,
                size: 17,
                color: Colors.red,
              ),
              Texts.textBlock("Lagoss, Nigeria",
                  color: Colors.black, fontWeight: FontWeight.w400, size: 14),
            ],
          ),
            Widgets.heightSpaceH2,
            Texts.textBlock("Detail Description of medication",
                color: Colors.black54, fontWeight: FontWeight.w300, size: 13),
            Widgets.heightSpaceH2,
            CustomDropdown2(onTap: (){}, value: null, hint: "Select your beneficiary", label:null)
            ,    Widgets.heightSpaceH2,Texts.textBlock("Upload Prescription",
                color: Colors.black, fontWeight: FontWeight.w600, size: 13),
            Widgets.heightSpaceH1,  CustomButton(label:"Browse file",onTap: (){},color: Colors.black12,)
        ],),
      ),
      bottomNavigationBar:
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: CustomButton(color: ColorConstants.secondaryColor,
          onTap: () {     Get.to(() => CartScreen());},
          label: "Add to cart",
        ),
      ),
    );
  }
}






