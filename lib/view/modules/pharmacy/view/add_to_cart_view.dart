import 'package:ensuram/view/modules/pharmacy/view/pharmacy_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../../core/constants/color_constants.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/shimmer_effect.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../../../../core/widgets/widgets.dart';
import '../controller/pharmacy_controller.dart';
import '../model/cart_model.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  PharmacyController controller = Get.find();
  // Add this RxBool to track terms acceptance
  RxBool termsAccepted = false.obs;

  @override
  void initState() {
    super.initState();
    controller.fetchCarts();
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Obx(() => Texts.textBlock(
            "Cart (${controller.cartItems.length})",
            size: 17, fontWeight: FontWeight.w700)),
        actions: [],
      ),
      body: Obx(() {
        if (controller.isCartLoading.value) {
          return  Padding(
            padding: const EdgeInsets.all(15.0),
            child: ShimmerListSkeleton(),
          );
        }

        if (controller.cartItems.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.shopping_cart_outlined,
                  size: 80,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Texts.textMedium(
                  "Your cart is empty",
                  size: 18,
                  color: Colors.grey[600],
                ),
                const SizedBox(height: 8),
                Texts.textNormal(
                  "Add some medications to get started",
                  size: 14,
                  color: Colors.grey[500],
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: controller.cartItems.length,
                itemBuilder: (context, index) {
                  final cartItem = controller.cartItems[index];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: CartItemCard(cartItem: cartItem),
                  );
                },
              ),
            ),
            _buildCartSummary(),
          ],
        );
      }),
    );
  }

  Widget _buildCartSummary() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [



          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Texts.textBold("Total:", size: 18),
              Obx(() => Texts.textBold(
                "\$${controller.cartSubtotal.value.toStringAsFixed(2)}",
                size: 18,
                color: ColorConstants.primaryColor,
              )),
            ],
          ),
          const SizedBox(height: 16),

          // Terms and Conditions Checkbox
          Row(
            children: [
              Obx(() => Container(width: 20,
                child: Checkbox(
                  value: termsAccepted.value,
                  onChanged: (value) {
                    termsAccepted.value = value ?? false;
                  },
                  activeColor: ColorConstants.primaryColor,
                ),
              )),SizedBox(width: 10,),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    termsAccepted.value = !termsAccepted.value;
                  },
                  child: RichText(
                    text: TextSpan(
                      text: 'I agree to the ',
                      style: TextStyle(color: Colors.black87, fontSize: 14),
                      children: [
                        TextSpan(
                          text: 'Terms & Conditions',
                          style: TextStyle(
                            color: ColorConstants.primaryColor,
                            fontWeight: FontWeight.bold,
                            decoration: TextDecoration.underline,
                          ),
                          // You can add a recognizer here to open terms page
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: Obx(() => CustomButton(
              label: "Proceed to Payment",
              onTap: termsAccepted.value ? () {
                // Check if beneficiary is selected
                if (controller.selectedBeneficiary.value.id == null ||
                    controller.selectedBeneficiary.value.id!.isEmpty) {
                  _showBeneficiarySelectionDialog(context);
                } else {
                  // Proceed with checkout
                  controller.checkoutOrder({
                    "beneficiary_id": controller.selectedBeneficiary.value.id ?? "",
                    "terms_accepted": true,'cart_session':controller.cartItems.first.cartSession
                  });
                }
              } : null, // Disable button if terms not accepted
              color: termsAccepted.value ? null : Colors.grey,
            )),
          ),
        ],
      ),
    );
  }

  // Add this method to show beneficiary selection dialog
  void _showBeneficiarySelectionDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        title: Texts.textBold("Select Beneficiary"),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Texts.textNormal(
              "Please select a beneficiary for this order.",
            ),
            const SizedBox(height: 16),
            // You can add a dropdown or list of beneficiaries here
            // For now, just show a message
            Texts.textNormal(
              "No beneficiary selected.",
              color: Colors.red,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Texts.textMedium("Cancel", color: Colors.grey),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              // Navigate to beneficiary selection or creation screen
              // Get.to(() => BeneficiarySelectionScreen());
              Widgets.showSnackBar("Info", "Please select a beneficiary first");
            },
            child: Texts.textMedium("Select", color: ColorConstants.primaryColor),
          ),
        ],
      ),
    );
  }
}

class CartItemCard extends StatelessWidget {
  const CartItemCard({
    Key? key,
    required this.cartItem,
  }) : super(key: key);

  final Cart cartItem;

  @override
  Widget build(BuildContext context) {
    final PharmacyController controller = Get.find();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // Medication Image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CachedNetworkImage(
                  imageUrl: cartItem.image ?? '',
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    width: 60,
                    height: 60,
                    color: Colors.grey[200],
                    child: const Icon(Icons.medication, color: Colors.grey),
                  ),
                  errorWidget: (context, url, error) => Container(
                    width: 60,
                    height: 60,
                    color: Colors.grey[200],
                    child: const Icon(Icons.medication, color: Colors.grey),
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // Medication Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Texts.textBold(
                      cartItem.medName ?? 'Unknown Medication',
                      size: 16,

                    ),
                    const SizedBox(height: 4),
                    Texts.textNormal(
                      cartItem.pharName ?? 'Unknown Pharmacy',
                      size: 12,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(height: 4),
                    Texts.textMedium(
                      "\$${cartItem.price ?? '0'}",
                      size: 14,
                      color: ColorConstants.primaryColor,
                    ),
                  ],
                ),
              ),

              // Remove Button
              IconButton(
                onPressed: () {
                  _showRemoveDialog(context, controller);
                },
                icon: const Icon(
                  Icons.delete_outline,
                  color: Colors.red,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Quantity Controls and Total
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Quantity Controls
              Row(
                children: [
                  Texts.textMedium("Qty:", size: 14),
                  const SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        InkWell(
                          onTap: () {
                            if ((num.tryParse(cartItem.quantity ?? '0') ?? 0) > 1) {
                              controller.updateCartItemQuantity(
                                cartItem,
                                (int.tryParse(cartItem.quantity ?? '0'))! - 1
                              );
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            child: const Icon(Icons.remove, size: 16),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          child: Texts.textMedium(
                            "${cartItem.quantity ?? 0}",
                            size: 14,
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            controller.updateCartItemQuantity(
                              cartItem,
                              (int.tryParse(cartItem.quantity ?? '0') ?? 0) + 1
                            );
                          },
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            child: const Icon(Icons.add, size: 16),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // Total Price
              Texts.textBold(
                "\$${double.tryParse(cartItem.subtotal ?? '0')?.toStringAsFixed(2) ?? '0'}",
                size: 16,
                color: ColorConstants.primaryColor,
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showRemoveDialog(BuildContext context, PharmacyController controller) {
    Get.dialog(
      AlertDialog(
        title: Texts.textBold("Remove Item"),
        content: Texts.textNormal(
          "Are you sure you want to remove ${cartItem.medName} from your cart?",
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Texts.textMedium("Cancel", color: Colors.grey),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              controller.removeCartItem(cartItem);
            },
            child: Texts.textMedium("Remove", color: Colors.red),
          ),
        ],
      ),
    );
  }
}

