import 'package:ensuram/view/modules/pharmacy/view/pharmacy_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../core/constants/color_constants.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/text_widgets.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Add To Cart",
            size: 17, fontWeight: FontWeight.w700),
        actions: [],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: ListView.builder(
          itemCount: demoCarts.length,
          itemBuilder: (context, index) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Dismissible(
              key: Key(demoCarts[index].product.id.toString()),
              direction: DismissDirection.endToStart,
              onDismissed: (direction) {
                setState(() {
                  demoCarts.removeAt(index);
                });
              },
              background: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFE6E6),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Row(
                  children: [
                    const Spacer(),
                    Icon(Icons.delete,color: Colors.red,)
                  ],
                ),
              ),
              child: CartCard(cart: demoCarts[index]),
            ),
          ),
        ),
      ),
      bottomNavigationBar: const CheckoutCard(),
    );
  }
}

class CartCard extends StatelessWidget {
  const CartCard({
    Key? key,
    required this.cart,
  }) : super(key: key);

  final Cart cart;

  @override
  Widget build(BuildContext context) {
    return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            SizedBox(
              width: 60,
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    "https://i.postimg.cc/JhJPLnpH/paracemtol.jpg",
                    fit: BoxFit.cover,
                    width: 60,
                    height: 60,
                  )),
            ),
            const SizedBox(width: 10),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Parcetmol 2",
                  style: const TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.bold),
                  maxLines: 2,
                ),
                const SizedBox(height: 3),
                Text.rich(
                  TextSpan(
                    text: "${cart.product.price} USD",
                    style:
                        TextStyle(fontWeight: FontWeight.w300, color: Colors.black),
                    children: [
                      TextSpan(
                          text: " x${cart.numOfItem}",
                          style: TextStyle(color: Colors.black54)),
                    ],
                  ),
                )
              ],
            ),
          ],
        ),
        Row(
          children: [
            Icon(Icons.remove_circle_outlined,color: ColorConstants.secondaryColor,),SizedBox(width: 5,),
            CircleAvatar(

              radius: 14,
              backgroundColor: Colors.white,
              child: Texts.textMedium("23",size: 9),
            ),SizedBox(width: 5,),
            Icon(Icons.add_circle_outlined,color: ColorConstants.secondaryColor)
          ],
        )
      ],
    );
  }
}

class CheckoutCard extends StatelessWidget {
  const CheckoutCard({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: 16,
        horizontal: 20,
      ),
      // height: 174,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, -15),
            blurRadius: 20,
            color: const Color(0xFFDADADA).withOpacity(0.15),
          )
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Texts.textMedium("Subtotal", size: 12),
                Texts.textBlock("566 INR", size: 15),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Texts.textMedium("Tax fee", size: 12),
                Texts.textBlock("0.56 INR", size: 15),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Texts.textMedium("Total", size: 14),
                Texts.textBlock("1566 INR"),
              ],
            ),
            const SizedBox(height: 16),
            CustomButton(color: ColorConstants.secondaryColor,
              onTap: () {},
              label: "Pay Now",
            )
          ],
        ),
      ),
    );
  }
}

class Cart {
  final Product product;
  final int numOfItem;

  Cart({required this.product, required this.numOfItem});
}

// Demo data for our cart

List<Cart> demoCarts = [
  Cart(product: demoProducts[0], numOfItem: 2),
  Cart(product: demoProducts[1], numOfItem: 1),
  Cart(product: demoProducts[2], numOfItem: 1),
];

class Product {
  final int id;
  final String title, description;
  final List<String> images;
  final List<Color> colors;
  final double rating, price;
  final bool isFavourite, isPopular;

  Product({
    required this.id,
    required this.images,
    required this.colors,
    this.rating = 0.0,
    this.isFavourite = false,
    this.isPopular = false,
    required this.title,
    required this.price,
    required this.description,
  });
}


