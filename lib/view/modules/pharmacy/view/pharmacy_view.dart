import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/widgets.dart';
import 'package:ensuram/view/modules/pharmacy/controller/pharmacy_controller.dart';
import 'package:ensuram/view/modules/pharmacy/view/add_to_cart_view.dart';
import 'package:ensuram/view/modules/pharmacy/view/medication_detail_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_rx/src/rx_typedefs/rx_typedefs.dart';

import '../../../../core/constants/color_constants.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/shimmer_effect.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../model/medication_model.dart';
// TODO: add flutter_svg to pubspec.yaml

class PharmacyView extends StatefulWidget {
  const PharmacyView({super.key});

  @override
  State<PharmacyView> createState() => _PharmacyViewState();
}

class _PharmacyViewState extends State<PharmacyView> {
  PharmacyController controller = Get.find();
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    scrollController.addListener(scrollListener);
    controller.fetchMedications(page: 1);
  }

  Widget widgetOrdersCard() {
    return Obx(() {
      return SingleChildScrollView(
        controller: scrollController,
        padding: EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomBorderButton(
              onTap: () {
                _showFilterBottomSheet(context);
              },
              label: "Filter Medications",
              icon: Icon(Icons.filter_alt_outlined),
              textColor: Colors.black,
              padding: 9,
            ),
            Widgets.heightSpaceH2,
            controller.isMedicationsLoading.value
                ? const ShimmerListSkeleton()
                : controller.medications.isNotEmpty
                    ? ListView.separated(
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          return Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: productCard(product: controller.medications[index], onPress:() {
                                controller.selectedMedication.value = controller.medications[index];
                                controller.fetchMedicationDetail();
                                Get.to(() =>MedicationDetailView());
                              }));
                        },
                        separatorBuilder: (context, index) {
                          return Widgets.divider();
                        },
                        itemCount: controller.medications.length ?? 0)
                    : Widgets.noRecordsFound(title: "No Medications Found"),
            if (controller.isMedicationsMoreLoading.value)
              Center(child: CircularProgressIndicator()),
            Widgets.heightSpaceH2,
            Widgets.heightSpaceH2,
            Widgets.heightSpaceH2,
          ],
        ),
      );
    });
  }

  scrollListener() {
    if (scrollController.position.pixels ==
            scrollController.position.maxScrollExtent &&
        controller.totalMedicationsCount.value >
            controller.medications.length) {
      controller.fetchMedications() >
          (page: controller.currentMedicationsPage.value + 1);
      controller.currentMedicationsPage.value++; // Increment the page counter
    }
  }
  Widget productCard({
    required Medication product,
    required Callback onPress,
  }) {
    return GestureDetector(
      onTap: onPress,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: .20.sh,
            width: 1.sw,
            decoration: BoxDecoration(
              color: const Color(0xFF979797).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: Widgets.networkImage(
                  product.image??""),

                )),

          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Texts.textBlock(product.medName??" ",
                  color: ColorConstants.primaryColor),
              GestureDetector(
                  onTap: () {controller.selectedMedication.value = product;controller.fetchMedicationDetail();
                    Get.to(() =>MedicationDetailView());

                  },
                  child: Icon(
                    Icons.shopping_cart,
                    size: 25,
                    color: ColorConstants.primaryColor,
                  ))
            ],
          ),
          Text(
            "\$${product.price}",
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w800,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 3),
          Row(
            children: [
              Icon(
                Icons.location_on_outlined,
                size: 15,
                color: Colors.red,
              ),
              Texts.textBlock(product.pharAddress??"",
                  color: Colors.black, fontWeight: FontWeight.w400, size: 12),
            ],
          ),
        ],
      ),
    );
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        leadingWidth: 30,
        title: Texts.textBlock("Medications",
            size: 20, fontWeight: FontWeight.w700),
        actions: [
        
          SizedBox(
            width: 10,
          ),
          Obx(() => Stack(
            children: [
              IconButton(
                onPressed: () async {
                  Get.to(() => CartScreen());
                },
                icon: Icon(Icons.shopping_cart),
              ),
              if (controller.cartItems.isNotEmpty)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '${controller.cartItems.length}',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          )),
        ],
      ),
      body: widgetOrdersCard(),
    );
  }

  void _showFilterBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
            left: 16,
            right: 16,
            top: 24,
          ),
          child: StatefulBuilder(
            builder: (context, setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text("Filter Medications",
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  SizedBox(height: 20),
                  _buildFilterField("Search Medications", "Enter  name",
                      controller.testController),
                  SizedBox(height: 16),
                  _buildFilterField("Search by Location", "Enter location",
                      controller.locationController),
                  SizedBox(height: 16),
                  _buildFilterField(
                      "Min Price", "0", controller.minPriceController,
                      isNumeric: true),
                  SizedBox(height: 16),
                  _buildFilterField(
                      "Max Price", "1000", controller.maxPriceController,
                      isNumeric: true),
                  SizedBox(height: 24),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            controller.testController.clear();
                            controller.locationController.clear();
                            controller.minPriceController.text = "0";
                            controller.maxPriceController.text = "1000";
                            Navigator.pop(context); // Close sheet
                            controller.fetchMedications(page: 1); // Reset
                          },
                          child: Text("Reset"),
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(color: Colors.cyan),
                            foregroundColor: Colors.cyan,
                            shape: StadiumBorder(),
                          ),
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context); // Close sheet
                            controller.fetchMedications(
                              page: 1,
                            );
                          },
                          child: Text("Search"),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.cyan,
                            shape: StadiumBorder(),
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                ],
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildFilterField(
      String label, String hint, TextEditingController controller,
      {bool isNumeric = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: TextStyle(fontWeight: FontWeight.w600)),
        SizedBox(height: 6),
        TextField(
          controller: controller,
          keyboardType: isNumeric ? TextInputType.number : TextInputType.text,
          decoration: InputDecoration(
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            hintText: hint,
            filled: true,
            fillColor: Colors.grey.shade200,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(30),
              borderSide: BorderSide.none,
            ),
          ),
        ),
      ],
    );
  }
}
