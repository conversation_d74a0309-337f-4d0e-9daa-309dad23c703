import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/widgets.dart';
import 'package:ensuram/view/modules/pharmacy/view/add_to_cart_view.dart';
import 'package:ensuram/view/modules/pharmacy/view/medication_detail_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_rx/src/rx_typedefs/rx_typedefs.dart';

import '../../../../core/constants/color_constants.dart';
import '../../../../core/widgets/text_widgets.dart';
// TODO: add flutter_svg to pubspec.yaml

class PharmacyView extends StatelessWidget {
  const PharmacyView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        leadingWidth: 30,
        title: Texts.textBlock("Medications",
            size: 20, fontWeight: FontWeight.w700),
        actions: [Texts.textBlock("Manage",size: 14),SizedBox(
          width: 10,
        ),
          cartIcon(
            () {
              Get.to(() => CartScreen());
            },
          ),
          SizedBox(
            width: 20,
          )
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(vertical: 16, horizontal: 15),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10)),
                child: TextField(
                  textAlignVertical: TextAlignVertical.center,
                  // focusNode: _searchFocusNode,
                  // controller: spotController.searchController,
                  onChanged: (value) {},
                  decoration: InputDecoration(
                      prefixIcon: IconButton(
                        padding: EdgeInsets.zero,
                        icon: const Icon(CupertinoIcons.search,
                            color: Colors.black38, size: 23),
                        onPressed: () {
                          // Perform the search here
                        },
                      ),
                      hintStyle: const TextStyle(
                          color: Colors.black38,
                          fontSize: 15,
                          decoration: TextDecoration.none),
                      border: InputBorder.none,
                      hintText: "Search medication"),
                  style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 15,
                      decoration: TextDecoration.none),
                ),
              ),
              Widgets.heightSpaceH1,
              CustomDropdown2(
                  onTap: () {}, value: null, hint: "Country", label: null),
              Widgets.heightSpaceH1,
              Row(
                children: [
                  Expanded(
                    child: CustomDropdown2(
                        onTap: () {},
                        value: null,
                        hint: "Region/Province",
                        label: null),
                  ),
                  Widgets.widthSpaceW2,
                  Expanded(
                    child: CustomDropdown2(
                        onTap: () {},
                        value: null,
                        hint: "City/Town",
                        label: null),
                  ),
                ],
              ),
              productSection(),
              SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget productSection() {
    return Column(
      children: [
        Widgets.heightSpaceH2,
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: demoProducts.length,
          itemBuilder: (context, index) {
            return productCard(
              product: demoProducts[index],
              onPress: () {},
            );
          },
          separatorBuilder: (BuildContext context, int index) {
            return Divider(
              color: Colors.black12,
            );
          },
        ),
      ],
    );
  }

  Widget productCard({
    required Product product,
    required Callback onPress,
  }) {
    return GestureDetector(
      onTap: onPress,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: .20.sh,
            width: 1.sw,
            decoration: BoxDecoration(
              color: const Color(0xFF979797).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: Image.network(
                  product.images[0],
                  fit: BoxFit.cover,
                )),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Texts.textBlock("Paracetamol 250",
                  color: ColorConstants.primaryColor),
              GestureDetector(
                  onTap: () {
                    Get.to(() =>MedicationDetailView());

                  },
                  child: Icon(
                    Icons.shopping_cart,
                    size: 25,
                    color: ColorConstants.primaryColor,
                  ))
            ],
          ),
          Text(
            "\$${product.price}",
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w800,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 3),
          Row(
            children: [
              Icon(
                Icons.location_on_outlined,
                size: 15,
                color: Colors.red,
              ),
              Texts.textBlock("Lagoss, Nigeria",
                  color: Colors.black, fontWeight: FontWeight.w400, size: 12),
            ],
          ),
        ],
      ),
    );
  }

  Widget cartIcon(Callback press) {
    return InkWell(
      borderRadius: BorderRadius.circular(100),
      onTap: press,
      child: Stack(
        children: [
          Icon(Icons.shopping_cart, color: ColorConstants.secondaryGreyColor),
          Positioned(
              right: 2,
              child: CircleAvatar(
                backgroundColor: Colors.red,
                radius: 4,
              ))
        ],
      ),
    );
  }
}
List<Product> demoProducts = [
  Product(
    id: 1,
    images: ["https://i.postimg.cc/JhJPLnpH/paracemtol.jpg"],
    colors: [
      const Color(0xFFF6625E),
      const Color(0xFF836DB8),
      const Color(0xFFDECB9C),
      Colors.white,
    ],
    title: "Paracetamol 250",
    price: 64.99,
    description: "",
    rating: 4.8,
    isFavourite: true,
    isPopular: true,
  ),
  Product(
    id: 2,
    images: [
      "https://i.postimg.cc/JhJPLnpH/paracemtol.jpg",
    ],
    colors: [
      const Color(0xFFF6625E),
      const Color(0xFF836DB8),
      const Color(0xFFDECB9C),
      Colors.white,
    ],
    title: "Paracetamol 250",
    price: 50.5,
    description: "",
    rating: 4.1,
    isPopular: true,
  ),
  Product(
    id: 3,
    images: [
      "https://i.postimg.cc/JhJPLnpH/paracemtol.jpg",
    ],
    colors: [
      const Color(0xFFF6625E),
      const Color(0xFF836DB8),
      const Color(0xFFDECB9C),
      Colors.white,
    ],
    title: "Paracetamol 250",
    price: 36.55,
    description: "",
    rating: 4.1,
    isFavourite: true,
    isPopular: true,
  ),
  Product(
    id: 4,
    images: [
      "https://i.postimg.cc/d1QWXMYW/Image-Popular-Product-3.png",
    ],
    colors: [
      const Color(0xFFF6625E),
      const Color(0xFF836DB8),
      const Color(0xFFDECB9C),
      Colors.white,
    ],
    title: "Paracetamol 250",
    price: 36.55,
    description: "",
    rating: 4.1,
    isFavourite: false,
    isPopular: true,
  ),
];