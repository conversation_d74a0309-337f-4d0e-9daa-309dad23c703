import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/widgets/shimmer_effect.dart' show ShimmerListSkeleton;
import 'package:ensuram/core/widgets/text_widgets.dart';
import 'package:ensuram/view/modules/laboratory/controller/lab_controller.dart';
import 'package:ensuram/view/modules/laboratory/view/lab_tests_view.dart';
import 'package:ensuram/view/modules/pharmacy/controller/pharmacy_controller.dart';
import 'package:ensuram/view/modules/pharmacy/view/pharmacy_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/widgets.dart';

class PharmacyOrderView extends StatefulWidget {
  const PharmacyOrderView({super.key});

  @override
  State<PharmacyOrderView> createState() => _PharmacyOrderViewState();
}

class _PharmacyOrderViewState extends State<PharmacyOrderView> {


 PharmacyController controller =Get.put(PharmacyController());
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    scrollController.addListener(scrollListener);
    controller.fetchOrders(page: 1);
  }

  scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent &&
        controller.totalOrdersCount.value >
            controller.orders.length) {
      controller.fetchOrders(
          page: controller.currentOrdersPage.value + 1);
      controller
          .currentOrdersPage.value++; // Increment the page counter
    }
  }
  @override
  Widget build(BuildContext context) {
  return Scaffold(

    bottomNavigationBar: Padding(
      padding: const EdgeInsets.all(15.0),
      child: CustomButton(label: "Buy Medication(s)",onTap: (){
        Get.to(()=>PharmacyView());
      },),
    ),
    backgroundColor: ColorConstants.halfWhite,
    appBar: AppBar(
      backgroundColor: ColorConstants.backgroundColor,
      elevation: 0,
      leadingWidth: 30,centerTitle: true,
      title: Texts.textBlock("Medication Orders",
          size: 20, fontWeight: FontWeight.w700),

    ),
    body: widgetOrdersCard(),
  );
  }

  Widget widgetOrdersCard() {
    return Obx(() {
      return SingleChildScrollView(controller: scrollController,padding: EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [


            controller.isOrdersLoading.value
                ? const ShimmerListSkeleton()
                : controller.orders.isNotEmpty
                ? ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  return Widgets.pharmacyOrderCard(controller.orders[index]);
                },
                separatorBuilder: (context, index) {
                  return Widgets.heightSpaceH1;
                },
                itemCount:
                controller.orders.length ?? 0)
                : Widgets.noRecordsFound(title: "No Orders Found"),
            if (controller.isOrdersLoading.value)
              Center(child: CircularProgressIndicator()),

            Widgets.heightSpaceH2, Widgets.heightSpaceH2, Widgets.heightSpaceH2,
          ],
        ),
      );
    });
  }

}
