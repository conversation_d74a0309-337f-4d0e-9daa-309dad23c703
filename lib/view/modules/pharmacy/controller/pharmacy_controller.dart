import 'package:ensuram/view/modules/pharmacy/model/medication_model.dart';
import 'package:ensuram/view/modules/pharmacy/model/pharmacy_order_model.dart';
import 'package:ensuram/view/modules/pharmacy/model/cart_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:ensuram/core/services/http_service.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';

import '../../../../core/constants/api_endpoints.dart';
import '../../../../core/widgets/widgets.dart';
import '../../beneficiary/model/beneificery_model.dart';
import '../../laboratory/model/lab_book_model.dart';
import '../../laboratory/model/lab_detail_model.dart';
import '../model/medication_detail_model.dart';

class PharmacyController extends GetxController {
  RxList orders = <PharmacyOrder>[].obs;
  RxBool isOrdersLoading = false.obs;
  RxBool isOrdersMoreLoading = false.obs;
  RxInt totalOrdersCount = 0.obs;
  RxInt currentOrdersPage = 0.obs;
  RxList medications = <Medication>[].obs;
  RxBool isMedicationsLoading = false.obs;
  RxBool isMedicationsMoreLoading = false.obs;
  RxInt totalMedicationsCount = 0.obs;
  RxInt currentMedicationsPage = 0.obs;
  final TextEditingController testController = TextEditingController();
  final TextEditingController locationController = TextEditingController();
  final TextEditingController minPriceController = TextEditingController(text: "0");
  final TextEditingController maxPriceController = TextEditingController(text: "1000");
  var selectedMedication=Medication().obs;
  var selectedMedicationDetail=MedicationDetail().obs;
  RxList benefieries = <Beneficary>[].obs;
  // Cart related properties
  RxList<Cart> cartItems = <Cart>[].obs;
  RxBool isCartLoading = false.obs;
  RxDouble cartSubtotal = 0.0.obs;
  RxDouble cartTotal = 0.0.obs;

  RxString cartCurrency = 'USD'.obs;

  // File upload properties
  Rx<File?> selectedPrescriptionFile = Rx<File?>(null);
  RxString selectedFileName = ''.obs;
  RxBool isFileSelected = false.obs;
 var selectedBeneficiary=Beneficary().obs;
  @override
  void onInit() {
    super.onInit();
    fetchCarts();
  }

  fetchOrders({int page = 1}) async {
    try {
      if (isOrdersLoading.value) return;
      if (page == 1) {
        isOrdersLoading.value = true;
      } else {
        isOrdersMoreLoading.value = true;
      }

      var response = await ApiService.getData(
          "${Endpoints.getMedicationOrders}?page=$page&limit=15");
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          orders.clear();
          totalOrdersCount.value = 0;
          currentOrdersPage.value = 1;
        }
        orders.addAll(
          (response.data['data'] as List)
              .map((e) => PharmacyOrder.fromJson(e))
              .toList(),
        );

        totalOrdersCount.value =
            int.parse(response.data['pagination']['total_records'].toString());
      }
    } catch (e) {
      print(e);
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    } finally {
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    }
  }
  fetchMedications({int page = 1}) async {
    try {
      if (isMedicationsLoading.value) return;
      if (page == 1) {
        isMedicationsLoading.value = true;
      } else {
        isMedicationsMoreLoading.value = true;
      }


      var response = await ApiService.getData(
          "${Endpoints.getMedications}?page=$page&limit=15&search=${testController.text}&location=${locationController.text}&min_price=${minPriceController.text}&max_price=${maxPriceController.text}");

      isMedicationsLoading.value = false;
      isMedicationsMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          medications.clear();
          totalMedicationsCount.value = 0;
          currentMedicationsPage.value = 1;
        }if(response.data['data']!=null){
          medications.addAll(
            (response.data['data']['medications'] as List)
                .map((e) => Medication.fromJson(e))
                .toList(),
          );

          totalMedicationsCount.value =
              int.parse(response.data['data']['total_pages'].toString());}
      }
    } catch (e) {
      print(e);
      isMedicationsLoading.value = false;
      isMedicationsMoreLoading.value = false;
    } finally {
      isMedicationsLoading.value = false;
      isMedicationsMoreLoading.value = false;
    }
  }
  fetchMedicationDetail() async {
    try {


      var response = await ApiService.getData(
          "${Endpoints.getMedicationDetail}?id=1");

      if (response.status == true) {


        selectedMedicationDetail.value= MedicationDetail.fromJson(response.data['data']);


      }} catch (e) {

    } finally {

    }
  }  fetchBenefiecieriesBackground({int page = 1}) async {
    try {

      var response = await ApiService.getData(
          Endpoints.getUserBeneficiaries);

      if (response.status == true) {

        benefieries.clear();


        benefieries.addAll(
          (response.data['data'] as List)
              .map((e) => Beneficary.fromJson(e))
              .toList(),
        );


      }
    } catch (e) {
      print(e);

    } finally {

    }
  }

  addToCart(var payload) async {
    try {
      Widgets.showLoader("Adding to cart...");

      // If a prescription file is selected, we should handle file upload
      // For now, we'll add the basic payload and note that file upload
      // would typically require a separate multipart request
      if (isPrescriptionFileSelected()) {
        payload['has_prescription'] = true;
        payload['prescription_file_name'] = selectedFileName.value;
        // In a real implementation, you would upload the file here
        // using a multipart request with the selectedPrescriptionFile.value
      }

      var response = await ApiService.postData(Endpoints.addToCart, payload);

      Widgets.hideLoader();

      if (response.data['success']??false == true) {
        Widgets.showSnackBar(
          "Success",
          response.message ?? "Item added to cart successfully",
        );
        // Clear the selected file after successful addition
        clearSelectedFile();
        // Refresh cart after adding item
        await fetchCarts();
        // Navigate back or to cart
        Get.back();
      } else {
        Widgets.showSnackBar(
          "Error",
          response.message ?? "Failed to add item to cart",
        );
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar(
        "Error",
        "An error occurred while adding item to cart",
      );
      print("Error adding to cart: $e");
    }
  }
  fetchCarts() async {
    try {
      isCartLoading.value = true;

      var response = await ApiService.getData(Endpoints.getCart);
      cartItems.clear();
      if (response.status == true && response.data != null) {


        if (response.data['data'] != null) {
          // Handle cart items
          if (response.data['data']['pharmacy_cart'] != null) {

            Map<String, dynamic> pharmacyCart = response.data['data']['pharmacy_cart'];

            pharmacyCart.forEach((pharmacyName, items) {
              for (var item in items) {
                cartItems.add(Cart.fromJson(item));
              }
            });

          }

          cartSubtotal.value = double.tryParse(
              response.data['data']['overall_total']?.toString() ?? '0') ?? 0.0;

        }
      }
    } catch (e) {
      print("Error fetching cart: $e");
    } finally {
      isCartLoading.value = false;
    }
  }  fetchCartsBackground() async {
    try {

      var response = await ApiService.getData(Endpoints.getCart);
      cartItems.clear();
      if (response.status == true && response.data != null) {


        if (response.data['data'] != null) {
          // Handle cart items
          if (response.data['data']['pharmacy_cart'] != null) {

            Map<String, dynamic> pharmacyCart = response.data['data']['pharmacy_cart'];

            pharmacyCart.forEach((pharmacyName, items) {
              for (var item in items) {
                cartItems.add(Cart.fromJson(item));
              }
            });

          }

          cartSubtotal.value = double.tryParse(
              response.data['data']['overall_total']?.toString() ?? '0') ?? 0.0;

        }
      }
    } catch (e) {
      print("Error fetching cart: $e");
    } finally {
    }
  }
  updateToCart(var payload) async {
    try {
      Widgets.showLoader("Updating cart...");

      var response = await ApiService.postData(Endpoints.updateCart, payload);

      Widgets.hideLoader();
      // Refresh cart after update
     fetchCartsBackground();
      if (response.status == true) {


      } else {

      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar(
        "Error",
        "An error occurred while updating cart",
      );
      print("Error updating cart: $e");
    }
  }

  // Method to update quantity locally and on server
  updateCartItemQuantity(Cart item, int newQuantity) async {
    if (newQuantity <= 0) {
      removeCartItem(item);
      return;
    }

    // Update locally first for immediate UI feedback
    int index = cartItems.indexWhere((cartItem) => cartItem.cartId == item.cartId);
    if (index != -1) {
      cartItems[index].quantity = newQuantity.toString();
      cartItems.refresh();
      _recalculateCartTotals();
    }

    // Update on server
    var payload = {
      'cart_id': item.cartId,
      'quantity': newQuantity,'cart_session': item.cartSession
    };

    await updateToCart(payload);
  }  removeFromCart(var payload) async {
    try {
      Widgets.showLoader("Removing item...");

      var response = await ApiService.postData(Endpoints.removeCart, payload);

      Widgets.hideLoader();
fetchCartsBackground();
      if (response.status == true) {

      } else {

      }
    } catch (e) {
      Widgets.hideLoader();

      print("Error removing from cart: $e");
    }
  }

  // Method to remove cart item
  removeCartItem(Cart item) async {
    // Remove locally first for immediate UI feedback
    cartItems.removeWhere((cartItem) => cartItem.cartId == item.cartId);
    _recalculateCartTotals();

    // Remove from server
    var payload = {
      'cart_id': item.cartId,"cart_session": item.cartSession
    };

    await removeFromCart(payload);
  }

  // Helper method to recalculate cart totals locally
  void _recalculateCartTotals() {
    double subtotal = 0.0;
    for (var item in cartItems) {
      subtotal += num.tryParse(item.subtotal ?? '0') ?? 0.0;
    }
    cartSubtotal.value = subtotal;

  }


  // File picker methods
  Future<void> pickPrescriptionFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        selectedPrescriptionFile.value = File(result.files.single.path!);
        selectedFileName.value = result.files.single.name;
        isFileSelected.value = true;

        Widgets.showSnackBar(
          "File Selected",
          "Prescription file '${result.files.single.name}' selected successfully"
        );
      } else {
        // User canceled the picker
        Widgets.showSnackBar("Cancelled", "File selection was cancelled");
      }
    } catch (e) {
      print("Error picking file: $e");
      Widgets.showSnackBar(
        "Error",
        "Failed to select file. Please try again."
      );
    }
  }

  // Clear selected file
  void clearSelectedFile() {
    selectedPrescriptionFile.value = null;
    selectedFileName.value = '';
    isFileSelected.value = false;
  }

  // Validate if file is selected
  bool isPrescriptionFileSelected() {
    return isFileSelected.value && selectedPrescriptionFile.value != null;
  }

  checkoutOrder(var payload) async {
    try {

      Widgets.showLoader("Loading..");

      var response = await ApiService.postData(Endpoints.checkout, payload);

      Widgets.hideLoader();

      if (response.data['success']??false == true) {
        Get.back();Get.back();
        Widgets.showSnackBar(
          "Success",
          response.message ?? "Order Submitted successfully",
        );
        fetchOrders(page: 1);fetchCartsBackground();
      } else {
        Widgets.showSnackBar(
          "Error",
          response.message ?? "Failed to add funds to wallet",
        );
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar(
        "Error",
        "An error occurred while adding funds",
      );
      print("Error adding funds to wallet: $e");
    }
  }
}
