import 'package:ensuram/view/modules/pharmacy/model/medication_model.dart';
import 'package:ensuram/view/modules/pharmacy/model/pharmacy_order_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:ensuram/core/services/http_service.dart';

import '../../../../core/constants/api_endpoints.dart';
import '../../../../core/widgets/widgets.dart';
import '../../laboratory/model/lab_book_model.dart';
import '../../laboratory/model/lab_detail_model.dart';

class PharmacyController extends GetxController {
  RxList orders = <PharmacyOrder>[].obs;
  RxBool isOrdersLoading = false.obs;
  RxBool isOrdersMoreLoading = false.obs;
  RxInt totalOrdersCount = 0.obs;
  RxInt currentOrdersPage = 0.obs;
  RxList medications = <Medication>[].obs;
  RxBool isMedicationsLoading = false.obs;
  RxBool isMedicationsMoreLoading = false.obs;
  RxInt totalMedicationsCount = 0.obs;
  RxInt currentMedicationsPage = 0.obs;
  final TextEditingController testController = TextEditingController();
  final TextEditingController locationController = TextEditingController();
  final TextEditingController minPriceController = TextEditingController(text: "0");
  final TextEditingController maxPriceController = TextEditingController(text: "1000");
  var selectedMedication=Medication().obs; var selectedLabDetail=LabDetail().obs;
  fetchOrders({int page = 1}) async {
    try {
      if (isOrdersLoading.value) return;
      if (page == 1) {
        isOrdersLoading.value = true;
      } else {
        isOrdersMoreLoading.value = true;
      }

      var response = await ApiService.getData(
          "${Endpoints.getMedicationOrders}?page=$page&limit=15");
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          orders.clear();
          totalOrdersCount.value = 0;
          currentOrdersPage.value = 1;
        }
        orders.addAll(
          (response.data['data'] as List)
              .map((e) => PharmacyOrder.fromJson(e))
              .toList(),
        );

        totalOrdersCount.value =
            int.parse(response.data['pagination']['total_records'].toString());
      }
    } catch (e) {
      print(e);
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    } finally {
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    }
  }
  fetchMedications({int page = 1}) async {
    try {
      if (isMedicationsLoading.value) return;
      if (page == 1) {
        isMedicationsLoading.value = true;
      } else {
        isMedicationsMoreLoading.value = true;
      }


      var response = await ApiService.getData(
          "${Endpoints.getMedications}?page=$page&limit=15&search=${testController.text}&location=${locationController.text}&min_price=${minPriceController.text}&max_price=${maxPriceController.text}");

      isMedicationsLoading.value = false;
      isMedicationsMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          medications.clear();
          totalMedicationsCount.value = 0;
          currentMedicationsPage.value = 1;
        }if(response.data['data']!=null){
          medications.addAll(
            (response.data['data']['medications'] as List)
                .map((e) => Medication.fromJson(e))
                .toList(),
          );

          totalMedicationsCount.value =
              int.parse(response.data['data']['total_pages'].toString());}
      }
    } catch (e) {
      print(e);
      isMedicationsLoading.value = false;
      isMedicationsMoreLoading.value = false;
    } finally {
      isMedicationsLoading.value = false;
      isMedicationsMoreLoading.value = false;
    }
  }
  fetchMedicationDetail() async {
    try {


      var response = await ApiService.getData(
          "${Endpoints.getMedicationDetail}?id=1");

      if (response.status == true) {


        selectedLabDetail.value= LabDetail.fromJson(response.data['data']);


      }} catch (e) {

    } finally {

    }
  }
  addToCart(var payload) async {
    try {
      Get.back();
      Widgets.showLoader("Booking...");

      var response = await ApiService.postData(Endpoints.addToCart, payload);

      Widgets.hideLoader();

      if (response.status == true) {
        Get.back();Get.back();
        Widgets.showSnackBar(
          "Success",
          response.message ?? "Failed to add funds to wallet",
        );
     ;
      } else {
        Widgets.showSnackBar(
          "Error",
          response.message ?? "Failed to add funds to wallet",
        );
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar(
        "Error",
        "An error occurred while adding funds",
      );
      print("Error adding funds to wallet: $e");
    }
  }
  fetchCarts() async {
    try {



      var response = await ApiService.getData(
          Endpoints.getCart);


      if (response.status == true) {
        // if (page == 1) {
        //   medications.clear();
        //   totalMedicationsCount.value = 0;
        //   currentMedicationsPage.value = 1;
        // }if(response.data['data']!=null){
        //   medications.addAll(
        //     (response.data['data']['lab_tests'] as List)
        //         .map((e) => LabTests.fromJson(e))
        //         .toList(),
        //   );
        //
        //   totalMedicationsCount.value =
        //       int.parse(response.data['data']['total_pages'].toString());}
      }
    } catch (e) {
      print(e);
      isMedicationsLoading.value = false;
      isMedicationsMoreLoading.value = false;
    } finally {
      isMedicationsLoading.value = false;
      isMedicationsMoreLoading.value = false;
    }
  }
  updateToCart(var payload) async {
    try {
      Get.back();
      Widgets.showLoader("Booking...");

      var response = await ApiService.postData(Endpoints.updateCart, payload);

      Widgets.hideLoader();

      if (response.status == true) {
        Get.back();Get.back();
        Widgets.showSnackBar(
          "Success",
          response.message ?? "Failed to add funds to wallet",
        );
        ;
      } else {
        Widgets.showSnackBar(
          "Error",
          response.message ?? "Failed to add funds to wallet",
        );
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar(
        "Error",
        "An error occurred while adding funds",
      );
      print("Error adding funds to wallet: $e");
    }
  }  removeFromCart(var payload) async {
    try {
      Get.back();
      Widgets.showLoader("Booking...");

      var response = await ApiService.postData(Endpoints.removeCart, payload);

      Widgets.hideLoader();

      if (response.status == true) {
        Get.back();Get.back();
        Widgets.showSnackBar(
          "Success",
          response.message ?? "Failed to add funds to wallet",
        );
        ;
      } else {
        Widgets.showSnackBar(
          "Error",
          response.message ?? "Failed to add funds to wallet",
        );
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar(
        "Error",
        "An error occurred while adding funds",
      );
      print("Error adding funds to wallet: $e");
    }
  }
  checkoutOrder(var payload) async {
    try {
      Get.back();
      Widgets.showLoader("Booking...");

      var response = await ApiService.postData(Endpoints.checkout, payload);

      Widgets.hideLoader();

      if (response.status == true) {
        Get.back();Get.back();
        Widgets.showSnackBar(
          "Success",
          response.message ?? "Failed to add funds to wallet",
        );
        ;
      } else {
        Widgets.showSnackBar(
          "Error",
          response.message ?? "Failed to add funds to wallet",
        );
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar(
        "Error",
        "An error occurred while adding funds",
      );
      print("Error adding funds to wallet: $e");
    }
  }
}
