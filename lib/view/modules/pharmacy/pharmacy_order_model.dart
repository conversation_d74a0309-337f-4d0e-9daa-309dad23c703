class PharmacyOrder {
  String? orderNumber;
  String? orderTotal;
  String? status;
  String? createdAt;

  PharmacyOrder(
      {this.orderNumber, this.orderTotal, this.status, this.createdAt});

  PharmacyOrder.fromJson(Map<String, dynamic> json) {
    orderNumber = json['order_number'];
    orderTotal = json['order_total'].toString();
    status = json['status'];
    createdAt = json['created_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['order_number'] = this.orderNumber;
    data['order_total'] = this.orderTotal;
    data['status'] = this.status;
    data['created_at'] = this.createdAt;
    return data;
  }
}
