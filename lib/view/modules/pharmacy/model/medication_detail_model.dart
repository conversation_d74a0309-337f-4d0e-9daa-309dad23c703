class MedicationDetail {
  String? medId;
  String? medName;
  String? category;
  String? onCondition;
  String? price;
  String? stock;
  String? description;
  String? indications;
  String? image;
  String? pharId;
  String? pharName;
  String? pharAddress;
  String? pharState;
  String? pharCity;
  String? pharCountry;

  MedicationDetail(
      {this.medId,
        this.medName,
        this.category,
        this.onCondition,
        this.price,
        this.stock,
        this.description,
        this.indications,
        this.image,
        this.pharId,
        this.pharName,
        this.pharAddress,
        this.pharState,
        this.pharCity,
        this.pharCountry});

  MedicationDetail.fromJson(Map<String, dynamic> json) {
    medId = json['med_id'];
    medName = json['med_name'];
    category = json['category'];
    onCondition = json['on_condition'];
    price = json['price'];
    stock = json['stock'];
    description = json['description'];
    indications = json['indications'];
    image = json['image'];
    pharId = json['phar_id'];
    pharName = json['phar_name'];
    pharAddress = json['phar_address'];
    pharState = json['phar_state'];
    pharCity = json['phar_city'];
    pharCountry = json['phar_country'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['med_id'] = this.medId;
    data['med_name'] = this.medName;
    data['category'] = this.category;
    data['on_condition'] = this.onCondition;
    data['price'] = this.price;
    data['stock'] = this.stock;
    data['description'] = this.description;
    data['indications'] = this.indications;
    data['image'] = this.image;
    data['phar_id'] = this.pharId;
    data['phar_name'] = this.pharName;
    data['phar_address'] = this.pharAddress;
    data['phar_state'] = this.pharState;
    data['phar_city'] = this.pharCity;
    data['phar_country'] = this.pharCountry;
    return data;
  }
}
