class Medication {
  int? medId;
  String? medName;
  String? price;
  String? image;
  int? pharId;
  int? stock;
  String? pharName;
  String? pharState;
  String? pharCity;
  String? pharAddress;

  Medication(
      {this.medId,
        this.medName,
        this.price,
        this.image,
        this.pharId,
        this.stock,
        this.pharName,
        this.pharState,
        this.pharCity,
        this.pharAddress});

  Medication.fromJson(Map<String, dynamic> json) {
    medId = json['med_id'];
    medName = json['med_name'];
    price = json['price'].toString();
    image = json['image'];
    pharId = json['phar_id'];
    stock = json['stock'];
    pharName = json['phar_name'];
    pharState = json['phar_state'];
    pharCity = json['phar_city'];
    pharAddress = json['phar_address'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['med_id'] = this.medId;
    data['med_name'] = this.medName;
    data['price'] = this.price;
    data['image'] = this.image;
    data['phar_id'] = this.pharId;
    data['stock'] = this.stock;
    data['phar_name'] = this.pharName;
    data['phar_state'] = this.pharState;
    data['phar_city'] = this.pharCity;
    data['phar_address'] = this.pharAddress;
    return data;
  }
}
