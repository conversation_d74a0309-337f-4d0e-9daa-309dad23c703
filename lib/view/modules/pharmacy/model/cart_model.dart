class Cart {
  String? cartId;
  String? medId;
  String? quantity;
  String? prescriptionFile;
  String? price;
  String? pharId;
  String? cartSession;
  String? medName;
  String? image;
  String? pharName;
  String? subtotal;

  Cart(
      {this.cartId,
        this.medId,
        this.quantity,
        this.prescriptionFile,
        this.price,
        this.pharId,
        this.cartSession,
        this.medName,
        this.image,
        this.pharName,
        this.subtotal});

  Cart.fromJson(Map<String, dynamic> json) {
    cartId = json['cart_id'];
    medId = json['med_id'];
    quantity = json['quantity'];
    prescriptionFile = json['prescription_file'];
    price = json['price'];
    pharId = json['phar_id'];
    cartSession = json['cart_session'];
    medName = json['med_name'];
    image = json['image'];
    pharName = json['phar_name'];
    subtotal = json['subtotal'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['cart_id'] = this.cartId;
    data['med_id'] = this.medId;
    data['quantity'] = this.quantity;
    data['prescription_file'] = this.prescriptionFile;
    data['price'] = this.price;
    data['phar_id'] = this.pharId;
    data['cart_session'] = this.cartSession;
    data['med_name'] = this.medName;
    data['image'] = this.image;
    data['phar_name'] = this.pharName;
    data['subtotal'] = this.subtotal;
    return data;
  }
}
