import 'dart:convert';
import 'dart:developer';
import 'dart:math';

import 'package:ensuram/view/modules/pre_authorization/model/pre_auth_model.dart';
import 'package:ensuram/view/modules/wallets/model/saving_transactions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_paypal/flutter_paypal.dart';
import 'package:flutterwave_standard/core/flutterwave.dart';
import 'package:flutterwave_standard/models/requests/customer.dart';
import 'package:flutterwave_standard/models/requests/customizations.dart';
import 'package:flutterwave_standard/models/responses/charge_response.dart';
import 'package:get/get.dart';
import 'package:ensuram/core/services/http_service.dart';
import 'package:ensuram/core/widgets/widgets.dart';

import '../../../../controller/user_controller.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../../beneficiary/model/beneificery_model.dart';

class PreAuthorController extends GetxController {

  final RxString selectedBeneficiaryId = "".obs;
  final RxString selectedBeneficiaryName = "".obs;
  final TextEditingController generatedCodeController = TextEditingController();
  RxBool isLoadingBeneficiaries = false.obs;
  final TextEditingController amountController = TextEditingController();


  RxList transactions = <PreAuth>[].obs;
  RxBool isTransactionsLoading = false.obs;
  RxBool isTransactionsMoreLoading = false.obs;
  RxInt totalTransactionsCount = 0.obs;
  RxInt currentPage = 0.obs;

  // For beneficiaries list
  RxList<Beneficary> beneficiaries = <Beneficary>[].obs;

  final TextEditingController searchTransactionController =
  TextEditingController();


  @override
  void onInit() {
    super.onInit();

    fetchBeneficiaries();
  }

  // Fetch beneficiaries from API
  Future<void> fetchBeneficiaries() async {
    try {
      isLoadingBeneficiaries.value = true;
      var response = await ApiService.getData(Endpoints.getUserBeneficiaries);
      isLoadingBeneficiaries.value = false;

      if (response.status == true) {
        beneficiaries.clear();
        beneficiaries.addAll(
          (response.data['data'] as List)
              .map((e) => Beneficary.fromJson(e))
              .toList(),
        );
      }
    } catch (e) {
      print("Error fetching beneficiaries: $e");
      isLoadingBeneficiaries.value = false;
      Widgets.showSnackBar("Error", "Failed to load beneficiaries");
    }
  }

  // Select a beneficiary
  void selectBeneficiary(Beneficary beneficiary) {
    selectedBeneficiaryId.value = beneficiary.id ?? "";
    selectedBeneficiaryName.value =
    "${beneficiary.firstName ?? ""} ${beneficiary.lastName ?? ""}";
  }

  @override
  void onClose() {
    generatedCodeController.dispose();
    amountController.dispose();
    super.onClose();
  }

  fetchTransactions({int page = 1}) async {
    try {
      if (isTransactionsLoading.value) return;
      if (page == 1) {
        isTransactionsLoading.value = true;
      } else {
        isTransactionsMoreLoading.value = true;
      }

      var response = await ApiService.getData(
          "${Endpoints.getPreAuths}?page=$page&limit=15");
      isTransactionsLoading.value = false;
      isTransactionsMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          transactions.clear();
          totalTransactionsCount.value = 0;
          currentPage.value = 1;
        }
        transactions.addAll(
          (response.data['data'] as List)
              .map((e) => PreAuth.fromJson(e))
              .toList(),
        );

        totalTransactionsCount.value =
            response.data['pagination']['total_records'] ?? 0;
      }
    } catch (e) {
      print(e);
      isTransactionsLoading.value = false;
      isTransactionsMoreLoading.value = false;
    } finally {
      isTransactionsLoading.value = false;
      isTransactionsMoreLoading.value = false;
    }
  }

  Future<void> submitRequest() async {
    try {
      Widgets.showLoader("Loading");
      var response =
      await ApiService.postData(Endpoints.allocatePreAuth, {
        "beneficiary_id": selectedBeneficiaryId.value,
        "amount": amountController.text,
        "code": generatedCodeController.text
      });
      Widgets.hideLoader();

      if (response.status == true) {
        Widgets.showSnackBar(
            "Success", response.message ?? " Pre-Authorization Successful!");

        amountController.clear();
        generatedCodeController.clear();
        final randomCode = (1000 + Random().nextInt(9000)).toString();
        generatedCodeController.text = randomCode;

        selectedBeneficiaryId.value = "";
        selectedBeneficiaryName.value = "";
        fetchTransactions(page: 1);
      } else {
        Widgets.showSnackBar(
            "Error", response.message ?? "Failed");
      }
    } catch (e) {
      print(e);
      Widgets.hideLoader();
      Widgets.showSnackBar(
          "Error", "An error occurred while scheduling appointment try");
    }
  }

  // Update pre-authorization
  Future<void> updatePreAuthorization(Map<String, dynamic> updateData) async {
    try {
      Widgets.showLoader("Updating...");

      var response = await ApiService.postData(
          Endpoints.updatePreAuth, updateData);

      Widgets.hideLoader();

      if (response.status == true) {
        Widgets.showSnackBar("Success",
            response.message ?? "Pre-authorization updated successfully");
        // Refresh transactions list
        fetchTransactions(page: 1);
      } else {
        Widgets.showSnackBar(
            "Error", response.message ?? "Failed to update pre-authorization");
      }
    } catch (e) {
      print("Error updating pre-authorization: $e");
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", "Failed to update pre-authorization");
    }
  }

}
