class PreAuth {
  String? id;
  String? firstName;
  String? lastName;
  String? amount;
  String? code;

  PreAuth({this.id, this.firstName, this.lastName, this.amount, this.code});

  PreAuth.fromJson(Map<String, dynamic> json) {
    id = json['id'].toString();
    firstName = json['first_name'].toString();
    lastName = json['last_name'].toString();
    amount = json['amount'].toString();
    code = json['code'].toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['first_name'] = this.firstName;
    data['last_name'] = this.lastName;
    data['amount'] = this.amount;
    data['code'] = this.code;
    return data;
  }
}
