import 'dart:math';

import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../core/widgets/shimmer_effect.dart';
import '../controller/pre_author_controller.dart';


class PreAuthorizationView extends StatefulWidget {
  const PreAuthorizationView({super.key});

  @override
  State<PreAuthorizationView> createState() => _PreAuthorizationViewState();
}

class _PreAuthorizationViewState extends State<PreAuthorizationView> {
  final ScrollController scrollController = ScrollController();
  final PreAuthorController controller =
      Get.put(PreAuthorController());

  @override
  void initState() {
    super.initState();

    final randomCode = (1000 + Random().nextInt(9000)).toString();
    controller.generatedCodeController.text = randomCode;
    scrollController.addListener(scrollListener);
    controller.fetchTransactions(page: 1);
  }
  void showBeneficiariesBottomSheet(BuildContext context, PreAuthorController controller) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.8,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Select Beneficiary',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
                Divider(height: 1, color: Colors.grey[300]),
                Expanded(
                  child: Obx(() {
                    if (controller.isLoadingBeneficiaries.value) {
                      return Center(child: CircularProgressIndicator());
                    } else if (controller.beneficiaries.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.person_off, size: 48, color: Colors.grey),
                            SizedBox(height: 16),
                            Text(
                              'No beneficiaries found',
                              style: TextStyle(color: Colors.grey),
                            ),

                          ],
                        ),
                      );
                    } else {
                      return ListView.builder(
                        controller: scrollController,
                        itemCount: controller.beneficiaries.length,
                        itemBuilder: (context, index) {
                          final beneficiary = controller.beneficiaries[index];
                          return ListTile(
                            leading: beneficiary.photo != null && beneficiary.photo!.isNotEmpty
                                ? CircleAvatar(
                              backgroundImage: NetworkImage(beneficiary.photo!),
                              backgroundColor: ColorConstants.primaryColor,
                            )
                                : CircleAvatar(
                              backgroundColor: ColorConstants.primaryColor,
                              child: Text(
                                '${beneficiary.firstName?.substring(0, 1) ?? ""}',
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                            title: Text(
                              '${beneficiary.firstName ?? ""} ${beneficiary.lastName ?? ""}',
                              style: TextStyle(fontSize: 16, color: Colors.black),
                            ),
                            subtitle: Text(
                              beneficiary.idNumber ?? 'No ID',
                              style: TextStyle(fontSize: 14, color: Colors.grey),
                            ),
                            trailing: Icon(
                              Icons.chevron_right,
                              color: Colors.grey,
                            ),
                            onTap: () {
                              controller.selectBeneficiary(beneficiary);
                              Navigator.pop(context);
                            },
                          );
                        },
                      );
                    }
                  }),
                ),
              ],
            );
          },
        );
      },
    );
  }

  scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent &&
        controller.totalTransactionsCount.value >
            controller.transactions.length) {
      controller.fetchTransactions(
          page: controller.currentPage.value + 1);
      controller
          .currentPage.value++; // Increment the page counter
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Pre-Authorization",
            size: 17, fontWeight: FontWeight.w700),
        actions: [],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [
            widgetAddAmount(),Widgets.heightSpaceH2,widgetTransactionsCard()
          ],
        ),
      ),
    );
  }

  widgetAddAmount() {
    return Container(
      width: 1.sw,
      padding: EdgeInsets.all(15),
      decoration: Widgets.blockDecoration,
      child: Column(
        children: [
          Widgets.heightSpaceH1,

          Obx(() => CustomDropdown(textColor: Colors.black,
            value: controller.selectedBeneficiaryName.value.isEmpty ? null : controller.selectedBeneficiaryName.value,
            label: "Beneficiary",color: ColorConstants.halfWhite,
            onTap: () {
              showBeneficiariesBottomSheet(context, controller);
            },
            hint: 'Select here',
          )),Widgets.heightSpaceH2,
          EntryField(color: ColorConstants.halfWhite,
            label: "Amount Allocated",controller: controller.amountController,
            hint: "0.0",
            textInputType: TextInputType.numberWithOptions(decimal: true),
            borderRadius: 10,
          ),
          EntryField(
            color: ColorConstants.halfWhite,
            label: "Generated Code",
            controller: controller.generatedCodeController,
            hint: "",
            readOnly: true,
            textInputType: TextInputType.number,
            borderRadius: 10,
          ),
          Widgets.heightSpaceH1,
          CustomButton(
            onTap: () {
              // Validate inputs
              if (controller.selectedBeneficiaryId.value.isEmpty) {
                Widgets.showSnackBar("Error", "Please select a beneficiary");
                return;
              }

              if (controller.amountController.text.isEmpty ||
                  double.tryParse(controller.amountController.text) == null ||
                  double.parse(controller.amountController.text) <= 0) {
                Widgets.showSnackBar("Error", "Please enter a valid amount");
                return;
              }
controller.submitRequest();


            },
            label: "Submit",
          ),
        ],
      ),
    );
  }


  Widget widgetTransactionsCard() {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Texts.textBlock("Manage Pre-Authorization", size: 15),






          Widgets.heightSpaceH2,
          controller.isTransactionsLoading.value
              ? const ShimmerListSkeleton()
              : controller.transactions.isNotEmpty
              ? ListView.separated(
              physics: const NeverScrollableScrollPhysics(),
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return Widgets.authorizationCard(
                  controller.transactions[index],
                  () {
                    _showUpdatePreAuthDialog(context, controller.transactions[index]);
                  }
                );
              },
              separatorBuilder: (context, index) {
                return Widgets.heightSpaceH1;
              },
              itemCount:
              controller.transactions.length ?? 0)
              : Widgets.noRecordsFound(title: "No Transactions Found"),
          if (controller.isTransactionsLoading.value)
            Center(child: CircularProgressIndicator()),

          Widgets.heightSpaceH2, Widgets.heightSpaceH2, Widgets.heightSpaceH2,
        ],
      );
    });
  }

  void _showUpdatePreAuthDialog(BuildContext context, dynamic transaction) {
    final amountController = TextEditingController(text: transaction.amount?.toString() ?? "");
    final codeController = TextEditingController(text: transaction.code?.toString() ?? "");

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        child: Container(
          padding: EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Texts.textBlock(
                    "Update Pre-Authorization",
                    size: 18,
                    fontWeight: FontWeight.bold
                  ),
                  IconButton(
                    icon: Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                    padding: EdgeInsets.zero,
                    constraints: BoxConstraints(),
                  )
                ],
              ),



              Widgets.heightSpaceH2,

              // Amount field
              EntryField(
                color: ColorConstants.halfWhite,
                label: "Amount",
                controller: amountController,
                hint: "0.0",
                textInputType: TextInputType.numberWithOptions(decimal: true),
                borderRadius: 10,
              ),
              // Amount field
              EntryField(
                color: ColorConstants.halfWhite,
                label: "Code",
                controller: codeController,
                hint: "0",
                textInputType: TextInputType.number,
                borderRadius: 10,
              ),

              Widgets.heightSpaceH3,

              // Save Button
              CustomButton(
                onTap: () {
                  // Validate amount
                  if (amountController.text.isEmpty ||
                      double.tryParse(amountController.text) == null ||
                      double.parse(amountController.text) <= 0) {
                    Widgets.showSnackBar("Error", "Please enter a valid amount");
                    return;
                  }
                  if (codeController.text.isEmpty ||
                     codeController.text.length!=4) {
                    Widgets.showSnackBar("Error", "Please enter a 4 digit code");
                    return;
                  }
                  // Prepare update data
                  final updateData = {
                    "id": transaction.id,
                    "amount": amountController.text,

                      "code": codeController.text,

                  };

                  // Call update method
                  controller.updatePreAuthorization(updateData);

                  // Close dialog
                  Navigator.pop(context);
                },
                label: "SAVE CHANGES",
              )
            ],
          ),
        ),
      ),
    );
  }
}
