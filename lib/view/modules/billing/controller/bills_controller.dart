import 'package:ensuram/view/modules/expense/model/expense_model.dart';
import 'package:ensuram/view/modules/pharmacy/model/pharmacy_order_model.dart';
import 'package:get/get.dart';
import 'package:ensuram/core/services/http_service.dart';

import '../../../../core/constants/api_endpoints.dart';
import '../../../../core/widgets/widgets.dart';
import '../../laboratory/model/lab_book_model.dart';
import '../model/insurances_model.dart';

class BillsController extends GetxController {
  RxList orders = <InsuranceBill>[].obs;
  RxBool isOrdersLoading = false.obs;
  RxBool isOrdersMoreLoading = false.obs;
  RxInt totalOrdersCount = 0.obs;
  RxInt currentOrdersPage = 0.obs;
  RxInt selectedIndex = 0.obs;

  // Add these properties for date filtering
  RxString startDate = ''.obs;
  RxString endDate = ''.obs;

  void selectItem(int index) {
    selectedIndex.value = index;
  }

  // Generic method to pay any type of bill
  payInsuranceBill(String id) async {
    try {
      Get.back();
      Widgets.showLoader("Processing payment");
      var response = await ApiService.postData(
          Endpoints.payInsuranceBill, {
        "bill_id": id,

      });
      Widgets.hideLoader();
      if (response.status == true) {
        Widgets.showSnackBar("Success", "Payment successful");


            fetchInsuranceBills(page: 1);


      } else {
        Widgets.showSnackBar("Error", response.message ?? "Payment failed");
      }
    } catch (e) {
      print(e);
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", "An error occurred during payment");
    } finally {
      Widgets.hideLoader();
    }
  }
  payLabsBill(String id) async {
    try {
      Get.back();
      Widgets.showLoader("Processing payment");
      var response = await ApiService.postData(
          Endpoints.payLabBill, {
        "bill_id": id,

      });
      Widgets.hideLoader();
      if (response.status == true) {
        Widgets.showSnackBar("Success", "Payment successful");


            fetchLabBills(page: 1);


      } else {
        Widgets.showSnackBar("Error", response.message ?? "Payment failed");
      }
    } catch (e) {
      print(e);
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", "An error occurred during payment");
    } finally {
      Widgets.hideLoader();
    }
  }

  payHospitalBill(String id) async {
    try {
      Get.back();
      Widgets.showLoader("Processing payment");
      var response = await ApiService.postData(
          Endpoints.payHospitalBill, {
        "bill_id": id,

      });
      Widgets.hideLoader();
      if (response.status == true) {
        Widgets.showSnackBar("Success", "Payment successful");

        // Refresh the appropriate bill list based on type

            fetchHospitalBills(page: 1);


      } else {
        Widgets.showSnackBar("Error", response.message ?? "Payment failed");
      }
    } catch (e) {
      print(e);
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", "An error occurred during payment");
    } finally {
      Widgets.hideLoader();
    }
  }
  payPharmacyBill(String id) async {
    try {
      Get.back();
      Widgets.showLoader("Processing payment");
      var response = await ApiService.postData(
          Endpoints.payPharmacyBill, {
        "bill_id": id,

      });
      Widgets.hideLoader();
      if (response.status == true) {
        Widgets.showSnackBar("Success", "Payment successful");

        // Refresh the appropriate bill list based on type

        fetchPharmacyBills(page: 1);


      } else {
        Widgets.showSnackBar("Error", response.message ?? "Payment failed");
      }
    } catch (e) {
      print(e);
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", "An error occurred during payment");
    } finally {
      Widgets.hideLoader();
    }
  }

  // Fetch insurance bills
  fetchInsuranceBills({int page = 1}) async {
    try {
      if (isOrdersLoading.value) return;
      if (page == 1) {
        isOrdersLoading.value = true;
      } else {
        isOrdersMoreLoading.value = true;
      }

      // Build query with date filters if provided
      String endpoint = "${Endpoints.getInsuranceBills}?page=$page&limit=15";
      if (startDate.value.isNotEmpty) {
        endpoint += "&startDate=${startDate.value}";
      }
      if (endDate.value.isNotEmpty) {
        endpoint += "&endDate=${endDate.value}";
      }

      var response = await ApiService.getData(endpoint);
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          orders.clear();
          totalOrdersCount.value = 0;
          currentOrdersPage.value = 1;
        }
        orders.addAll(
          (response.data['data'] as List)
              .map((e) => InsuranceBill.fromJson(e))
              .toList(),
        );

        totalOrdersCount.value =
            int.parse(response.data['pagination']['total_records'].toString());
      }
    } catch (e) {
      print(e);
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    } finally {
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    }
  }

  // Fetch lab bills
  fetchLabBills({int page = 1}) async {
    try {
      if (isOrdersLoading.value) return;
      if (page == 1) {
        isOrdersLoading.value = true;
      } else {
        isOrdersMoreLoading.value = true;
      }

      // Build query with date filters if provided
      String endpoint = "${Endpoints.getLabBills}?page=$page&limit=15";
      if (startDate.value.isNotEmpty) {
        endpoint += "&startDate=${startDate.value}";
      }
      if (endDate.value.isNotEmpty) {
        endpoint += "&endDate=${endDate.value}";
      }

      var response = await ApiService.getData(endpoint);
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          orders.clear();
          totalOrdersCount.value = 0;
          currentOrdersPage.value = 1;
        }
        orders.addAll(
          (response.data['data'] as List)
              .map((e) => InsuranceBill.fromJson(e))
              .toList(),
        );

        totalOrdersCount.value =
            int.parse(response.data['pagination']['total_records'].toString());
      }
    } catch (e) {
      print(e);
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    } finally {
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    }
  }

  // Fetch hospital bills
  fetchHospitalBills({int page = 1}) async {
    try {
      if (isOrdersLoading.value) return;
      if (page == 1) {
        isOrdersLoading.value = true;
      } else {
        isOrdersMoreLoading.value = true;
      }

      // Build query with date filters if provided
      String endpoint = "${Endpoints.getHospitalBills}?page=$page&limit=15";
      if (startDate.value.isNotEmpty) {
        endpoint += "&startDate=${startDate.value}";
      }
      if (endDate.value.isNotEmpty) {
        endpoint += "&endDate=${endDate.value}";
      }

      var response = await ApiService.getData(endpoint);
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          orders.clear();
          totalOrdersCount.value = 0;
          currentOrdersPage.value = 1;
        }
        orders.addAll(
          (response.data['data'] as List)
              .map((e) => InsuranceBill.fromJson(e))
              .toList(),
        );

        totalOrdersCount.value =
            int.parse(response.data['pagination']['total_records'].toString());
      }
    } catch (e) {
      print(e);
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    } finally {
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    }
  }
  // Fetch hospital bills
  fetchPharmacyBills({int page = 1}) async {
    try {
      if (isOrdersLoading.value) return;
      if (page == 1) {
        isOrdersLoading.value = true;
      } else {
        isOrdersMoreLoading.value = true;
      }

      // Build query with date filters if provided
      String endpoint = "${Endpoints.getPharmacyBills}?page=$page&limit=15";
      if (startDate.value.isNotEmpty) {
        endpoint += "&startDate=${startDate.value}";
      }
      if (endDate.value.isNotEmpty) {
        endpoint += "&endDate=${endDate.value}";
      }

      var response = await ApiService.getData(endpoint);
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          orders.clear();
          totalOrdersCount.value = 0;
          currentOrdersPage.value = 1;
        }
        orders.addAll(
          (response.data['data'] as List)
              .map((e) => InsuranceBill.fromJson(e))
              .toList(),
        );

        totalOrdersCount.value =
            int.parse(response.data['pagination']['total_records'].toString());
      }
    } catch (e) {
      print(e);
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    } finally {
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    }
  }
}
