import 'package:ensuram/core/constants/color_constants.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../core/constants/constants_list.dart';
import '../../../../core/routes/app_routes.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/shimmer_effect.dart';
import '../controller/bills_controller.dart';
import '../model/insurances_model.dart';

class BillingView extends StatefulWidget {
  const BillingView({super.key});

  @override
  State<BillingView> createState() => _BillingViewState();
}

class _BillingViewState extends State<BillingView> {
  final BillsController controller = Get.put(BillsController());
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
controller.fetchInsuranceBills(page: 1);
    scrollController.addListener(scrollListener);
  }
  Widget widgetOrdersCard() {
    return Obx(() {
      return SingleChildScrollView(
        controller: scrollController,

        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [  Widgets.heightSpaceH2,
            SizedBox(
              height: 35,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: Data.billsTab.length,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      controller.selectItem(index);
                      // Reset pagination
                      controller.currentOrdersPage.value = 0;
                      controller.orders.clear();

                      // Fetch bills based on selected tab
                      switch (index) {
                        case 0: // Insurance
                          controller.fetchInsuranceBills(page: 1);
                          break;
                        case 1: // Lab
                          controller.fetchLabBills(page: 1);
                          break;
                        case 2: // Hospital
                          controller.fetchPharmacyBills(page: 1);
                          break;
                        case 3: // Pharmacy
                          controller.fetchHospitalBills(page: 1);
                          break;
                      }
                    },
                    child: Obx(() => Container(
                      padding: EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                      margin: const EdgeInsets.only(left: 15),
                      decoration: BoxDecoration(
                        color: controller.selectedIndex.value == index
                            ? ColorConstants.primaryColor
                            : Colors.white,
                        borderRadius: BorderRadius.circular(15),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        Data.billsTab[index],
                        style: TextStyle(
                          color: controller.selectedIndex.value == index
                              ? Colors.white
                              : Colors.black54,
                        ),
                      ),
                    )),
                  );
                },
              )
            ),
            Widgets.heightSpaceH3,

            controller.isOrdersLoading.value
                ? Padding(
              padding: EdgeInsets.symmetric(horizontal: 15),
                  child: const ShimmerListSkeleton(),
                )
                : controller.orders.isNotEmpty
                ? ListView.separated(
                    physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.symmetric(horizontal: 15),
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      InsuranceBill? transaction = controller.orders[index];print(controller.selectedIndex.value);
                      return Widgets.billsCard(
                        onTap: () {
                          payBill(
                            context,
                            controller.orders[index]
                          );
                        },
                      title: controller.selectedIndex.value==0?transaction?.insuranceName?? "":controller.selectedIndex.value==1?transaction?.labName?? "":controller.selectedIndex.value==2?transaction?.labName?? "":transaction?.hospitalName?? "",
                      date: transaction?.dateOfService?? "",
                      status: transaction?.status ?? "",
                      billingNo: transaction?.billingNumber ?? "",
                      amount: transaction?.costOfServices ?? "",
                      );
                    },
                    separatorBuilder: (context, index) {
                      return Widgets.heightSpaceH1;
                    },
                    itemCount: controller.orders.length ?? 0)
                : Widgets.noRecordsFound(title: "No bills Found"),

            if (controller.isOrdersMoreLoading.value)
              Center(child: CircularProgressIndicator()),

            Widgets.heightSpaceH2, Widgets.heightSpaceH2, Widgets.heightSpaceH2,
          ],
        ),
      );
    });
  }

  // Helper method to get bill type string from index
  String getBillTypeFromIndex(int index) {
    switch (index) {
      case 0: return "insurance";
      case 1: return "lab";
      case 2: return "pharmacy";
      case 3: return "hospital";
      default: return "insurance";
    }
  }

  // Helper method to get bill title from index
  String getBillTitleFromIndex(int index) {
    switch (index) {
      case 0: return "Insurance Bill";
      case 1: return "Lab Test Bill";

      case 2: return "Pharmacy Bill";    case 3: return "Hospital Bill";
      default: return "Bill";
    }
  }

  scrollListener() {
    if (scrollController.position.pixels == scrollController.position.maxScrollExtent &&
        controller.totalOrdersCount.value > controller.orders.length) {

      // Fetch more bills based on selected tab
      switch (controller.selectedIndex.value) {
        case 0: // Insurance
          controller.fetchInsuranceBills(page: controller.currentOrdersPage.value + 1);
          break;
        case 1: // Lab
          controller.fetchLabBills(page: controller.currentOrdersPage.value + 1);
          break;
        case 2: // Hospital
          controller.fetchHospitalBills(page: controller.currentOrdersPage.value + 1);
          break;
        case 3: // Pharmacy
          controller.fetchPharmacyBills(page: controller.currentOrdersPage.value + 1);
          break;
      }

      controller.currentOrdersPage.value++; // Increment the page counter
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Bills", size: 20, fontWeight: FontWeight.w700),
        actions: [],
      ),
      body: widgetOrdersCard(),
    );
  }
  void payBill(BuildContext context, InsuranceBill? transaction) {
    // Get the bill type based on the selected index
    String billType = getBillTypeFromIndex(controller.selectedIndex.value);

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.8,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Padding(
              padding: const EdgeInsets.all(15.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title based on bill type
                  Text(
                    'Pay ${getBillTitleFromIndex(controller.selectedIndex.value)}',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  Widgets.heightSpaceH1,
                  Widgets.divider(),
                  Widgets.heightSpaceH2,
                  RichText(
                    text: TextSpan(
                      text: 'Make sure to ',
                      style: TextStyle(color: Colors.black45, fontSize: 12),
                      children: [
                        TextSpan(
                          text: 'fill up the wallet ',
                          style: TextStyle(
                            color: Colors.blue,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              Get.toNamed(AppRoutes.userAddWallet);
                            },
                        ),
                        TextSpan(
                          text: ', before initiating this transaction.',
                          style: TextStyle(
                            color: Colors.black45,
                            fontSize: 12,
                          )
                        ),
                      ]
                    )
                  ),
                  Widgets.heightSpaceH2,
                  Widgets.columTexts(
                    title: 'Billing No.',
                    value: '${transaction?.billingNumber ?? ""}'
                  ),
                  Widgets.heightSpaceH2,
                  Widgets.columTexts(
                    title: 'Payment Amount',
                    value: '\$${transaction?.costOfServices ?? ""}'
                  ),
                  Widgets.heightSpaceH2,
                  Widgets.heightSpaceH3,
                  Widgets.divider(),
                  Widgets.heightSpaceH2,
                  CustomButton(
                    label: "PAY BILL",
                    color: ColorConstants.blackColor,
                    onTap: () {

                      switch(billType) {
                        case "insurance":
                          controller.payInsuranceBill(transaction?.id ?? "");
                          break;
                        case "lab":
                          controller.payLabsBill(transaction?.id ?? "");
                          break;
                        case "pharmacy":
                          controller.payPharmacyBill(transaction?.id ?? "");
                          break;
                        case "hospital":
                          controller.payHospitalBill(transaction?.id ?? "");
                          break;
                      }
                    },
                    textStyle: TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                      fontWeight: FontWeight.w600
                    ),
                  )
                ],
              ),
            );
          },
        );
      },
    );
  }
}