class InsuranceBill {
  String? id;
  String? insuranceName;
  String? billingNumber;
  String? descriptionOfServices;
  String? costOfServices;
  String? dateOfService;
  String? status;String? hospitalName;
String? labName;
  InsuranceBill(
      {this.id,
        this.insuranceName,this.labName,
        this.billingNumber,this.hospitalName,
        this.descriptionOfServices,
        this.costOfServices,
        this.dateOfService,
        this.status});

  InsuranceBill.fromJson(Map<String, dynamic> json) {
    id = json['id'].toString();
    insuranceName = json['insurance_name'];
    billingNumber = json['billing_number'];labName = json['labs_name'];
    descriptionOfServices = json['description_of_services'];
    costOfServices = json['cost_of_services'].toString();
    dateOfService = json['date_of_service'];
    status = json['status'];hospitalName = json['subaccount_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['insurance_name'] = this.insuranceName;data['labs_name'] = this.labName;
    data['billing_number'] = this.billingNumber;
    data['description_of_services'] = this.descriptionOfServices;
    data['cost_of_services'] = this.costOfServices;
    data['date_of_service'] = this.dateOfService;data['subaccount_name'] = this.hospitalName;
    data['status'] = this.status;
    return data;
  }
}
