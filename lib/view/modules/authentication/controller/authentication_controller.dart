import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../controller/user_controller.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../../../../core/services/http_service.dart';
import '../../../../core/widgets/widgets.dart';
import '../../../../model/user_model.dart';

import '../../../dashboard/view/dashboard_view.dart';

class AuthenticationController extends GetxController {
  late UserController userController;
  RxBool obscured = false.obs;

  void toggleObscured() {
    obscured.value = !obscured.value;
  }

  @override
  void onInit() {
    super.onInit();
    userController = Get.find();
  }

  void loginUser(String emailString, String password) async {
    try {
      Widgets.showLoader("Loading.. ");

      var response = await ApiService.postData(Endpoints.login,
          {"user_email": emailString, "user_password": password});
      Widgets.hideLoader();

      if (response.status == true) {
        userController.token = response.data["token"];
        fetchUserDetails();
      } else {
        Widgets.showSnackBar("Error", response.message ?? "");
      }
    } catch (e) {
      Widgets.showSnackBar("Error", e.toString());
    } finally {
      Widgets.hideLoader();
    }
  }

  void signupUser({var data}) async {
    try {
      Widgets.showLoader("Creating account..");

      var response = await ApiService.postData(Endpoints.register, data);

      Widgets.hideLoader();

      if (response.status == true) {
        Get.back();
        Widgets.showSnackBar("Success", response.message ?? "");
      } else {
        Widgets.showSnackBar("Error", response.message ?? "");
      }
    } catch (e) {
      Widgets.showSnackBar("Error", e.toString());
    } finally {
      Widgets.hideLoader();
    }
  }

  requestForgotPassword(String emailString) async {
    try {
      Widgets.showLoader("Loading.. ");

      var payload = {"user_email": emailString};

      var response =
          await ApiService.postData(Endpoints.forgotPassword, payload);

      Widgets.hideLoader();

      if (response.status == true) {
        Get.back();
        Widgets.showSnackBar("Success", response.message ?? "");
      } else {
        Widgets.showSnackBar("Error", response.message ?? "");
      }
    } catch (e) {
      Widgets.hideLoader();
    } finally {
      Widgets.hideLoader();
    }
  }

  fetchUserDetails() async {
    try {
      Widgets.showLoader("Loading ");

      var response = await ApiService.getData(Endpoints.userInfo);
      Widgets.hideLoader();
      if (response.status == true) {
        User userModel = User.fromJson(response.data['user']);

        await userController.saveUser(userModel, userController.token ?? "");
        await userController.fetchUser();
        Get.offAll(() => UserDashboardView());
      } else {}
    } catch (e) {
    } finally {
      Widgets.hideLoader();
    }
  }
}
