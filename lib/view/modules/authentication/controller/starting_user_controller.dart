import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/widgets/text_widgets.dart';

class StartingUserController extends GetxController {
  // Observable variables for checkboxes
  var isSpouse = false.obs;
  var isMother = false.obs;
  var isMotherInLaw = false.obs;
  var isGrandMother = false.obs;
  var isMyself = false.obs;
  var isFather = false.obs;
  var isFatherInLaw = false.obs;
  var isGrandFather = false.obs;
  var isOther = false.obs;
  RxInt? numberDaughter;
  RxInt? numberSon;
  void showDaughterNumbers(BuildContext context) {
    showModalBottomSheet(
      backgroundColor: Colors.white,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Texts.textBold("Select any one of them",),
              SizedBox(height: 10),
              // Dynamically generate a list of TextButtons for numbers 1 to 10
              ListView.separated(
                shrinkWrap: true,padding: EdgeInsets.zero,
                physics: NeverScrollableScrollPhysics(),
                itemCount: 10, // Numbers from 1 to 10
                separatorBuilder: (context, index) => Divider(color: Colors.black12),
                itemBuilder: (context, index) {
                  int number = index + 1; // Start from 1
                  return TextButton(
                    onPressed: () {
                      numberDaughter?.value=number;
                      Get.back();// Close the bottom sheet
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "$number Daughter(s)",
                          style: TextStyle(
                            color:Colors.black, // Replace with your ColorConstants.secondaryColor
                            fontSize: 15,
                          ),
                        ),
                        Icon(Icons.arrow_right_alt, color: Colors.black38),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }
  void showSonNumbers(BuildContext context) {
    showModalBottomSheet(
      backgroundColor: Colors.white,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Texts.textBold("Select any one of them",),
              SizedBox(height: 10),
              // Dynamically generate a list of TextButtons for numbers 1 to 10
              ListView.separated(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: 10, // Numbers from 1 to 10
                separatorBuilder: (context, index) => Divider(color: Colors.black12),
                itemBuilder: (context, index) {
                  int number = index + 1; // Start from 1
                  return TextButton(
                    onPressed: () {
                      numberSon?.value=number;
                      Get.back();// Close the bottom sheet
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "$number Son(s)",
                          style: TextStyle(
                            color: Colors.black, // Replace with your ColorConstants.secondaryColor
                            fontSize: 15,
                          ),
                        ),
                        Icon(Icons.arrow_right_alt, color: Colors.black38),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

}
