import 'package:ensuram/core/constants/padding_constants.dart';
import 'package:ensuram/core/routes/app_routes.dart';
import 'package:ensuram/core/utils/extensions.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:ensuram/core/widgets/widgets.dart';
import 'package:ensuram/view/modules/authentication/view/login_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../core/constants/color_constants.dart';
import '../../../../../core/widgets/round_body.dart';
import '../../../../../core/widgets/text_widgets.dart';
import '../controller/authentication_controller.dart';

class UserForgotPasswordView extends StatelessWidget {
   UserForgotPasswordView({super.key});
  final AuthenticationController authenticationController =
  Get.put(AuthenticationController());
  TextEditingController emailController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return GestureDetector(onTap: () => context.hideKeyboard(),
      child: Scaffold(
        body: SafeArea(
          child: RoundBody(isBack: true,
            widget: ListView(
              shrinkWrap: true,
              padding: PaddingConstants.screenPadding,
              children: [
                Widgets.heightSpaceH3,
                Texts.textBold("Forgotten Password",
                    color: Colors.white, textAlign: TextAlign.center, size: 28),
                Widgets.heightSpaceH1,
                Texts.textNormal("Instructions on how to reset your password will be sent through this email.", color: Colors.white),
                Widgets.heightSpaceH3,
                EntryField(controller: emailController,
                  hint: "Type your email address",
                  prefixIcon: Icons.email_outlined,
                ),


                Widgets.heightSpaceH1,
                CustomButton(label: "RESET PASSWORD", onTap: () {
                  if (!GetUtils.isEmail(emailController.text)) {
                    Widgets.showSnackBar(
                        "Incomplete Form", "Please enter valid email");
                  }else {
                    authenticationController.requestForgotPassword(emailController.text.toString());
                  }

                }), Widgets.heightSpaceH2,
                CustomButton(label: "CREATE ACCOUNT", onTap: () {

                  Get.toNamed(AppRoutes.userRegistration);
                },color: ColorConstants.whiteColor,textColor: Colors.black87,), Widgets.heightSpaceH2,

              ],
            ),
          ),
        ),
      ),
    );
  }
}
