import 'package:ensuram/core/constants/padding_constants.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:ensuram/core/widgets/widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import '../../../../../core/constants/color_constants.dart';
import '../../../../../core/widgets/round_body.dart';
import '../../../../../core/widgets/text_widgets.dart';

class UserAccountVerificationView extends StatelessWidget {

  TextEditingController otpController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: RoundBody(
          isBack: true,
          widget: ListView(
            shrinkWrap: true,
            padding: PaddingConstants.screenPadding,
            children: [
              Widgets.heightSpaceH4,
              Texts.textBold("Email Verification",
                  color: Colors.white, textAlign: TextAlign.center, size: 28),
              Widgets.heightSpaceH2,
              Text(


                  "Please check your email for a message with your code. Your code is 6 numbers long. Check also your SPAM.\nWe’ve send you the verification code on \<EMAIL>",
                  style: TextStyle(
                    color: Colors.white,
                  )),
              Widgets.heightSpaceH3,

              PinCodeTextField(
                controller: otpController,
                appContext: context,
                length: 6,
                autoDisposeControllers: true,
                animationType: AnimationType.fade,
                textStyle: const TextStyle(color: Colors.black),
                pinTheme: PinTheme(
                    fieldWidth: .12.sw,
                    shape: PinCodeFieldShape.box,
                    borderRadius: BorderRadius.circular(10),
                    borderWidth: .5,
                    selectedBorderWidth: .8,
                    activeBorderWidth: .5,
                    activeFillColor: ColorConstants.whiteColor,
                    inactiveFillColor: ColorConstants.whiteColor,
                    inactiveBorderWidth: .5,
                    selectedColor: ColorConstants.primaryColor,
                    activeColor: Colors.black12.withOpacity(.09),
                    selectedFillColor: ColorConstants.whiteColor,
                    inactiveColor: Colors.black12.withOpacity(.09)),
                cursorColor: ColorConstants.primaryColor,
                animationDuration: Duration(milliseconds: 300),
                enableActiveFill: true,
                keyboardType: TextInputType.number,
                onCompleted: (v) {
                  print("Completed");
                },
                onChanged: (value) {},
                beforeTextPaste: (text) {
                  print("Allowing to paste $text");
                  //if you return true then it will show the paste confirmation dialog. Otherwise if false, then nothing will happen.
                  //but you can show anything you want here, like your pop up saying wrong paste format or etc
                  return true;
                },
              ),
              Widgets.heightSpaceH3,
              Widgets.heightSpaceH1,
              CustomButton(label: "VALIDATE", onTap: () {}),
              Widgets.heightSpaceH2,

              Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                Text("Didn't get the code?   ",
                    style: TextStyle(
                      color: Colors.white,
                    )),
                InkWell(
                  onTap: () {},
                  child: Text(
                    'Resend Code',
                    style: TextStyle(
                      color: ColorConstants.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ]),
              Widgets.heightSpaceH2,
            ],
          ),
        ),
      ),
    );
  }
}
