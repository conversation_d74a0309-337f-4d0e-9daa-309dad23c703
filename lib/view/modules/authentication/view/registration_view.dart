import 'package:ensuram/core/constants/padding_constants.dart';
import 'package:ensuram/core/routes/app_routes.dart';
import 'package:ensuram/core/utils/extensions.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:ensuram/core/widgets/widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../core/constants/color_constants.dart';
import '../../../../../core/widgets/round_body.dart';
import '../../../../../core/widgets/text_widgets.dart';
import '../controller/authentication_controller.dart';

class UserRegistrationView extends StatelessWidget {
  UserRegistrationView({super.key});
  final AuthenticationController authenticationController =
      Get.put(AuthenticationController());
  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();

  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () => context.hideKeyboard(),
        child: Scaffold(
          body: SafeArea(
            child: RoundBody(
              isBack: true,
              widget: ListView(
                shrinkWrap: true,
                padding: PaddingConstants.screenPadding,
                children: [
                  Widgets.heightSpaceH3,
                  Texts.textBold("Create your Account",
                      color: Colors.white,
                      textAlign: TextAlign.center,
                      size: 28),
                  Widgets.heightSpaceH1,
                  Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                    Texts.textNormal("Already have an account?   ",
                        color: Colors.white70),
                    GestureDetector(
                        onTap: () {
                          Get.toNamed(AppRoutes.userLogin);
                        },
                        child: Texts.textBlock(
                            fontFamily: "LatoLight",
                            fontWeight: FontWeight.w800,
                            size: 15,
                            "Sign in here",
                            color: Colors.white))
                  ]),
                  Widgets.heightSpaceH3,
                  EntryField(
                    controller: firstNameController,
                    hint: "Type your first name",
                    prefixIcon: Icons.person_3_outlined,
                  ),
                  EntryField(
                    controller: lastNameController,
                    hint: "Type your last name",
                    prefixIcon: Icons.person_3_outlined,
                  ),
                  EntryField(
                    controller: emailController,
                    hint: "Type your email address",
                    prefixIcon: Icons.email_outlined,
                  ),
                  Obx(() => EntryField(
                      controller: passwordController,
                      hint: "Type your password",
                      prefixIcon: Icons.lock_outline,
                      obscureText: authenticationController.obscured.value,
                      suffixIcon:
                          authenticationController.obscured.value == false
                              ? CupertinoIcons.eye_slash
                              : Icons.remove_red_eye_outlined,
                      onTrailingTap: () {
                        authenticationController.toggleObscured();
                      })),
                  Widgets.heightSpaceH1,
                  CustomButton(
                      label: "CREATE ACCOUNT",
                      onTap: () {
                        context.hideKeyboard();
                        if (!GetUtils.isEmail(emailController.text)) {
                          Widgets.showSnackBar(
                              "Incomplete form", "Please enter valid email.");
                        } else if (passwordController.text.length <6) {
                          Widgets.showSnackBar("Incomplete form",
                              "Please enter password more than length 6 characters.");
                        } else if (firstNameController.text.isEmpty) {
                          Widgets.showSnackBar("Incomplete form",
                              "Please enter first name field");
                        } else if (lastNameController.text.isEmpty) {
                          Widgets.showSnackBar("Incomplete form",
                              "Please enter last name field");
                        } else {
                          authenticationController.signupUser(data: {
                            "user_firstname": firstNameController.text,
                            "user_lastname": lastNameController.text,
                            "user_email": emailController.text,
                            "user_password": passwordController.text,

                          });
                        }
                      }),
                  Widgets.heightSpaceH2,
                  Wrap(
                    crossAxisAlignment: WrapCrossAlignment.center,
                    alignment: WrapAlignment.center,
                    children: [
                      const Text(
                        'Your information and use of app is governed by our latest',
                        style: TextStyle(
                          color: Colors.white,
                        ),
                      ),
                      InkWell(
                        onTap: () {},
                        child: Text(
                          'Terms of Use',
                          style: TextStyle(
                            color: ColorConstants.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const Text(
                        ' and ',
                        style: TextStyle(
                          color: Colors.white,
                        ),
                      ),
                      InkWell(
                        onTap: () {},
                        child: Text(
                          'Privacy Policy',
                          style: TextStyle(
                            color: ColorConstants.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ));
  }
}
