import 'package:ensuram/core/constants/padding_constants.dart';
import 'package:ensuram/core/routes/app_routes.dart';
import 'package:ensuram/core/utils/extensions.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:ensuram/core/widgets/widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../core/constants/color_constants.dart';
import '../../../../../core/widgets/round_body.dart';
import '../../../../../core/widgets/text_widgets.dart';
import '../controller/authentication_controller.dart';

class UserLoginView extends StatelessWidget {
  UserLoginView({super.key});
  final AuthenticationController authenticationController =
      Get.put(AuthenticationController());
  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return GestureDetector(onTap: () => context.hideKeyboard(),
      child: Scaffold(
        body: SafeArea(
          child: RoundBody(
            isBack: true,
            widget: ListView(
              shrinkWrap: true,
              padding: PaddingConstants.screenPadding,
              children: [
                Widgets.heightSpaceH3,
                Texts.textBold("African Immigrant or Resident Login here",
                    color: Colors.white, textAlign: TextAlign.center, size: 28),
                Widgets.heightSpaceH1,
                Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                  Texts.textNormal("New to Ensuram?   ", color: Colors.white70),
                  GestureDetector(
                      onTap: () => Get.toNamed(AppRoutes.userRegistration),
                      child: Texts.textBlock(
                          fontFamily: "LatoLight",
                          fontWeight: FontWeight.w800,
                          size: 15,
                          "Create Account",
                          color: Colors.white))
                ]),
                Widgets.heightSpaceH3,
                EntryField(
                  controller: emailController,
                  hint: "Type your email",
                  prefixIcon: Icons.email_outlined,
                ),
      Obx(
      () => EntryField(
                    controller: passwordController,
                    hint: "Type your password",
                    prefixIcon: Icons.lock_outline,
                    obscureText: authenticationController.obscured.value,
                    suffixIcon: authenticationController.obscured.value == false
                        ? CupertinoIcons.eye_slash
                        : Icons.remove_red_eye_outlined,
                    onTrailingTap: () {
                      authenticationController.toggleObscured();
                    })),
                GestureDetector(
                  onTap: () => Get.toNamed(AppRoutes.userForgotPassword),
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: Align(
                      alignment: Alignment.topRight,
                      child: Texts.textBlock("Forgot Password?",
                          color: Colors.white,
                          size: 14,
                          fontFamily: "LatoLight",
                          fontWeight: FontWeight.w800),
                    ),
                  ),
                ),
                Widgets.heightSpaceH3,
                CustomButton(
                    label: "LOGIN",
                    onTap: () {context.hideKeyboard();
                      if (!GetUtils.isEmail(emailController.text)) {
                        Widgets.showSnackBar(
                            "Incomplete Form", "Please enter valid email");
                      } else if (passwordController.text.length < 6) {
                        Widgets.showSnackBar("Incomplete Form",
                            "Please enter password min length 6 characters");
                      }else {
                        authenticationController.loginUser(emailController.text.toString(), passwordController.text.toString());
                      }
                    }),
                Widgets.heightSpaceH2,
                Wrap(
                  crossAxisAlignment: WrapCrossAlignment.center,
                  alignment: WrapAlignment.center,
                  children: [
                    const Text(
                      'Your information and use of app is governed by our latest',
                      style: TextStyle(
                        color: Colors.white,
                      ),
                    ),
                    InkWell(
                      onTap: () {},
                      child: Text(
                        'Terms of Use',
                        style: TextStyle(
                          color: ColorConstants.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const Text(
                      ' and ',
                      style: TextStyle(
                        color: Colors.white,
                      ),
                    ),
                    InkWell(
                      onTap: () {},
                      child: Text(
                        'Privacy Policy',
                        style: TextStyle(
                          color: ColorConstants.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
