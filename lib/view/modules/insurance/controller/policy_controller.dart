import 'dart:developer';

import 'package:ensuram/view/modules/insurance/model/insurances.dart';
import 'package:ensuram/view/modules/insurance/policy_model.dart';
import 'package:ensuram/view/modules/insurance/view/insurances_view.dart';
import 'package:ensuram/view/modules/pharmacy/pharmacy_order_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ensuram/core/services/http_service.dart';

import '../../../../core/constants/api_endpoints.dart';
import '../../../../core/widgets/widgets.dart';
import '../../beneficiary/model/beneificery_model.dart';
import '../../laboratory/model/lab_book_model.dart';
import '../../laboratory/model/lab_detail_model.dart';

class PolicyController extends GetxController {
  RxList orders = <PolicyOrder>[].obs;
  RxBool isOrdersLoading = false.obs;
  RxBool isOrdersMoreLoading = false.obs;
  RxInt totalOrdersCount = 0.obs;
  RxInt currentOrdersPage = 0.obs;
  RxList benefieries = <Beneficary>[].obs;
  RxList providers = <Provider>[].obs;
  var selectedLabTest=LabTests().obs; var selectedLabDetail=LabDetail().obs;
  RxList insurances = <Insurances>[].obs;
  RxBool isInsurancesLoading = false.obs;
  RxBool isInsurancesMoreLoading = false.obs;
  RxInt totalInsurancesCount = 0.obs;
  RxInt currentInsurancesPage = 0.obs;
  var selectedProvider=Provider(name: "All Providers",id: "").obs;
  RxString selectedPlanType="All Plan Types".obs;

  RxString selectedCountry="All Countries".obs;

  var selectedInsurances = <Insurances>[].obs;

  void toggleInsuranceSelection(Insurances insurance) {
    if (selectedInsurances.contains(insurance)) {
      selectedInsurances.remove(insurance);
    } else {
      if (selectedInsurances.length < 2) {
        selectedInsurances.add(insurance);
      } else {
        Get.snackbar("Limit Reached", "You can only compare up to 2 plans.");
      }
    }
  }
  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    fetchBenefiecieriesBackground(page: 1);fetchProviders();
  }
  fetchOrders({int page = 1}) async {
    try {
      if (isOrdersLoading.value) return;
      if (page == 1) {
        isOrdersLoading.value = true;
      } else {
        isOrdersMoreLoading.value = true;
      }

      var response = await ApiService.getData(
          "${Endpoints.getInsuranceOrders}?page=$page&limit=15");
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          orders.clear();
          totalOrdersCount.value = 0;
          currentOrdersPage.value = 1;
        }
        orders.addAll(
          (response.data['data'] as List)
              .map((e) => PolicyOrder.fromJson(e))
              .toList(),
        );

        totalOrdersCount.value =
            int.parse(response.data['pagination']['total_records'].toString());
      }
    } catch (e) {
      print(e);
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    } finally {
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    }
  }

  var numberOfPeople = 1.obs;
  var peopleList = <PersonInfo>[].obs;

  void updatePeople(int number) {
    numberOfPeople.value = number;
    peopleList.value =
        List.generate(number, (index) => PersonInfo()); // initialize person info
  }

  void printAllData() {
    for (var i = 0; i < peopleList.length; i++) {
      log('Person ${i + 1}: ${peopleList[i]}');
    }
  }
  void validateAndSubmit() {
    for (var i = 0; i < peopleList.length; i++) {
      final person = peopleList[i];
      if (person.beneficiary == null ||
          person.smokes == null ||
          person.healthCondition == null ||
          person.pastSurgery == null) {
        Get.snackbar(
          'Incomplete Info',
          'Please fill all fields for Person ${i + 1}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.withOpacity(0.8),
          colorText: Colors.white,
        );
        return; // stop submission
      }
    }

    // If everything is valid, print all data
    printAllData();Get.to(()=>UserInsurancesView());
  }
  fetchInsuranceDetail() async {
    try {

      var response = await ApiService.postData(
          Endpoints.getInsuranceDetail,{
            "item_ids":selectedInsurances.map((e) => e.itemId).toList(),

          });

      if (response.status == true) {

        // benefieries.clear();
        //
        //
        // benefieries.addAll(
        //   (response.data['data'] as List)
        //       .map((e) => Beneficary.fromJson(e))
        //       .toList(),
        // );


      }
    } catch (e) {
      print(e);

    } finally {

    }
  }

  fetchBenefiecieriesBackground({int page = 1}) async {
    try {

      var response = await ApiService.getData(
          Endpoints.getUserBeneficiaries);

      if (response.status == true) {

          benefieries.clear();


        benefieries.addAll(
          (response.data['data'] as List)
              .map((e) => Beneficary.fromJson(e))
              .toList(),
        );


      }
    } catch (e) {
      print(e);

    } finally {

    }
  }
  fetchInsurances({int page = 1}) async {
    try {
      if (isInsurancesLoading.value) return;
      if (page == 1) {
        isInsurancesLoading.value = true;
      } else {
        isInsurancesMoreLoading.value = true;
      }


      var response = await ApiService.postData(
          "${Endpoints.searchInsurances}?page=$page&limit=15",{

            "country":selectedCountry.value=="All Countries"?"":selectedCountry.value,
            "plan_type":selectedPlanType.value=="All Plan Types"?"":selectedPlanType.value,
            "insurance_id":selectedProvider.value.id,
      });

      isInsurancesLoading.value = false;
      isInsurancesMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          insurances.clear();
          totalInsurancesCount.value = 0;
          currentInsurancesPage.value = 1;
        }if(response.data['data']!=null){
          insurances.addAll(
            (response.data['data'] as List)
                .map((e) => Insurances.fromJson(e))
                .toList(),
          );

          totalInsurancesCount.value =
              int.parse(response.data['data']['total_pages'].toString());}
      }
    } catch (e) {
      print(e);
      isInsurancesLoading.value = false;
      isInsurancesMoreLoading.value = false;
    } finally {
      isInsurancesLoading.value = false;
      isInsurancesMoreLoading.value = false;
    }
  }
  fetchProviders() async {
    try {

      var response = await ApiService.getData(
          Endpoints.getProviders);

      if (response.status == true) {

        providers.clear();


        providers.addAll(
          (response.data['data'] as List)
              .map((e) => Provider.fromJson(e))
              .toList(),
        );


      }
    } catch (e) {
      print(e);

    } finally {

    }
  }
  buyPolicy() async {
    try {
Widgets.showLoader("Loading");
      var response = await ApiService.postData(
          Endpoints.buyInsurances,{
        "item_id":"", "beneficiaries":"", "subscription_type":""
          });

      if (response.status == true) {




      }
    } catch (e) {
    Widgets.hideLoader();

    } finally {
      Widgets.hideLoader();
    }
  }
}
class PersonInfo {
  String? beneficiary;
  String? smokes;
  String? healthCondition;
  String? pastSurgery;

  PersonInfo({
    this.beneficiary,
    this.smokes,
    this.healthCondition,
    this.pastSurgery,
  });

  @override
  String toString() {
    return 'Beneficiary: $beneficiary, Smokes: $smokes, Health: $healthCondition, Past Surgery: $pastSurgery';
  }
}