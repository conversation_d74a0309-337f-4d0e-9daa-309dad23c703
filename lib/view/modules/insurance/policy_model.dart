class PolicyOrder {
  String? policyId;
  String? subscriptionType;
  String? totalCost;
  String? numPeople;
  String? policyStatus;
  String? policyCreated;
  String? insuranceNumber;
  String? planName;

  PolicyOrder(
      {this.policyId,
        this.subscriptionType,
        this.totalCost,
        this.numPeople,
        this.policyStatus,
        this.policyCreated,
        this.insuranceNumber,
        this.planName});

  PolicyOrder.fromJson(Map<String, dynamic> json) {
    policyId = json['policy_id'].toString();
    subscriptionType = json['subscription_type'];
    totalCost = json['total_cost'].toString();
    numPeople = json['num_people'].toString();
    policyStatus = json['policy_status'];
    policyCreated = json['policy_created'];
    insuranceNumber = json['insurance_number']!=null?json['insurance_number'].toString():"";
    planName = json['plan_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['policy_id'] = this.policyId;
    data['subscription_type'] = this.subscriptionType;
    data['total_cost'] = this.totalCost;
    data['num_people'] = this.numPeople;
    data['policy_status'] = this.policyStatus;
    data['policy_created'] = this.policyCreated;
    data['insurance_number'] = this.insuranceNumber;
    data['plan_name'] = this.planName;
    return data;
  }
}
