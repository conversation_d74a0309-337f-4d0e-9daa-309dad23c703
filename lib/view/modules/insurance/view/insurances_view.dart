import 'package:ensuram/core/constants/color_constants.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../core/constants/constants_list.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_dropdown.dart';
import '../../../../core/widgets/shimmer_effect.dart';
import '../controller/policy_controller.dart';
import '../model/insurances.dart';
import 'comparison_insurances_view.dart';

class UserInsurancesView extends StatefulWidget {
  const UserInsurancesView({super.key});

  @override
  State<UserInsurancesView> createState() => _UserInsurancesViewState();
}

class _UserInsurancesViewState extends State<UserInsurancesView> {
  final ScrollController scrollController = ScrollController();
  final PolicyController controller = Get.find();

  @override
  void initState() {
    super.initState();

    scrollController.addListener(scrollListener);
    controller.fetchInsurances(page: 1);
  }

  scrollListener() {
    if (scrollController.position.pixels ==
            scrollController.position.maxScrollExtent &&
        controller.totalInsurancesCount.value > controller.insurances.length) {
      controller.fetchInsurances() >
          (page: controller.currentInsurancesPage.value + 1);
      controller.currentInsurancesPage.value++; // Increment the page counter
    }
  }

  Widget widgetOrdersCard() {
    return Obx(() {
      return SingleChildScrollView(
        controller: scrollController,
        padding: EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomBorderButton(
              onTap: () {
                _showFilterBottomSheet(context);
              },
              label: "Filter Insurances",
              icon: Icon(Icons.filter_alt_outlined),
              textColor: Colors.black,
              padding: 9,
            ),
            Widgets.heightSpaceH2,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Texts.textMedium("See plans below might help you ",
                    size: 11, color: Colors.black38),
                InkWell(
                  onTap: () {
                    if (controller.selectedInsurances.isNotEmpty) {

                      controller.fetchInsuranceDetail();
                      Get.to(() => ComparisonInsurancesView(
                      ));
                    } else {
                      Widgets.showSnackBar(
                          "Select Plans", "Please select 1 or 2 plans to compare.");
                    }
                  },
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(17),
                    child: Container(
                      color: Colors.deepOrangeAccent,
                      padding:
                          EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                      child: Obx(() => Texts.textMedium(
                            "COMPARE ${controller.selectedInsurances.length} PLANS",
                            size: 11,
                            color: Colors.white,
                          )),
                    ),
                  ),
                ),
              ],
            ),
            Widgets.heightSpaceH2,
            controller.isInsurancesLoading.value
                ? const ShimmerListSkeleton()
                : controller.insurances.isNotEmpty
                    ? ListView.separated(
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          return Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Obx(() => Widgets.insuranceCard(
                                      controller.insurances[index],
                                      isSelected: controller.selectedInsurances
                                          .contains(
                                              controller.insurances[index]),
                                      onCheckboxChanged: () {
                                    controller.toggleInsuranceSelection(
                                        controller.insurances[index]);
                                  })));
                        },
                        separatorBuilder: (context, index) {
                          return Widgets.divider();
                        },
                        itemCount: controller.insurances.length ?? 0)
                    : Widgets.noRecordsFound(title: "No Insurances Plan Found"),
            if (controller.isInsurancesMoreLoading.value)
              Center(child: CircularProgressIndicator()),
            Widgets.heightSpaceH2,
            Widgets.heightSpaceH2,
            Widgets.heightSpaceH2,
          ],
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: ColorConstants.halfWhite,
        appBar: AppBar(
          leading: InkWell(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.arrow_back,
              color: ColorConstants.primaryBlackColor,
            ),
          ),
          backgroundColor: ColorConstants.backgroundColor,
          elevation: 0,
          centerTitle: true,
          title: Texts.textBlock("Insurances Plan",
              size: 20, fontWeight: FontWeight.w700),
          actions: [],
        ),
        body: widgetOrdersCard());
  }

  void _showFilterBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
            left: 16,
            right: 16,
            top: 24,
          ),
          child: StatefulBuilder(
            builder: (context, setState) {
              return Obx(
                ()=> Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text("Filter Policies",
                        style:
                            TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    SizedBox(height: 20),
                    CustomDropdown(
                      textColor: Colors.black,
                      color: ColorConstants.halfWhite,
                      value: controller.selectedProvider.value.name,
                      label: "Insurance Providers",
                      onTap: () {
                        showProvidersBottomSheet(context);
                      },
                      hint: 'Select here',
                    ),
                    Widgets.heightSpaceH1,
                    CustomDropdown(
                      textColor: Colors.black,
                      color: ColorConstants.halfWhite,
                      value: controller.selectedPlanType.value,
                      label: "Plan Types",
                      onTap: () {
                        showPlanTypes(context);
                      },
                      hint: 'Select here',
                    ),
                    Widgets.heightSpaceH1,
                    CustomDropdown(
                      textColor: Colors.black,
                      color: ColorConstants.halfWhite,
                      value: controller.selectedCountry.value,
                      label: "Countries",
                      onTap: () {
                        showCountries(context);
                      },
                      hint: 'Select here',
                    ),
                    SizedBox(height: 24),
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () {
                              controller.selectedProvider.value =
                                  Provider(name: "All Providers", id: "");
                              controller.selectedPlanType.value =
                                  "All Plan Types";
                              controller.selectedCountry.value = "All Countries";
                              Get.back(); // Close sheet
                              controller.fetchInsurances(page: 1); // Reset
                            },
                            child: Text("Reset"),
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(color: Colors.cyan),
                              foregroundColor: Colors.cyan,
                              shape: StadiumBorder(),
                            ),
                          ),
                        ),
                        SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              Get.back(); // Close sheet
                              controller.fetchInsurances(
                                page: 1,
                              );
                            },
                            child: Text("Apply"),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.cyan,
                              shape: StadiumBorder(),
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 20),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  void showProvidersBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.8,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Column(
              children: [
                // Title
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Providers',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
                Divider(height: 1, color: Colors.grey[300]),
                // List of Beneficiaries
                Expanded(
                  child: Obx(() {
                    if (controller.providers.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.access_time,
                                size: 48, color: Colors.grey),
                            SizedBox(height: 16),
                            Text(
                              'No providers found',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      );
                    } else {
                      return ListView.builder(
                        controller: scrollController,
                        itemCount: controller.providers.length,
                        itemBuilder: (context, index) {
                          Provider provider = controller.providers[index];
                          return ListTile(
                            title: Text(
                              provider.name ?? "",
                              style:
                                  TextStyle(fontSize: 16, color: Colors.black),
                            ),
                            trailing: Icon(
                              Icons.chevron_right,
                              color: Colors.grey,
                            ),
                            onTap: () {
                              controller.selectedProvider.value = provider;
                              Get.back();
                            },
                          );
                        },
                      );
                    }
                  }),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void showPlanTypes(BuildContext context) {
    final RxString selectedMethod = ''.obs;

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.6,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Texts.textBlock("Select one option",
                          size: 18, fontWeight: FontWeight.w600),
                      IconButton(
                        icon: Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  Divider(),
                  Expanded(
                    child: ListView.builder(
                        itemCount: Data.planTypes.length,
                        itemBuilder: (context, index) {
                          return ListTile(
                            title: Text(
                              Data.planTypes[index],
                              style:
                                  TextStyle(fontSize: 16, color: Colors.black),
                            ),
                            trailing: Icon(
                              Icons.chevron_right,
                              color: Colors.grey,
                            ),
                            onTap: () {
                              controller.selectedPlanType.value =
                                  Data.planTypes[index];
                              Get.back();
                            },
                          );
                        }),
                  ),
                ],
              ),
            );
          },
        );
      },
    ).then((value) {
      if (value != null) {
        // Handle the selected payment method
        print("Selected payment method: $value");
        // You can update your UI or state here
      }
    });
  }

  void showCountries(BuildContext context) {
    final RxString selectedMethod = ''.obs;

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.6,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Texts.textBlock("Select Country",
                          size: 18, fontWeight: FontWeight.w600),
                      IconButton(
                        icon: Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  Divider(),
                  Expanded(
                    child: ListView.builder(
                        itemCount: Data.countries.length,
                        itemBuilder: (context, index) {
                          return ListTile(
                            title: Text(
                              Data.countries[index],
                              style:
                                  TextStyle(fontSize: 16, color: Colors.black),
                            ),
                            trailing: Icon(
                              Icons.chevron_right,
                              color: Colors.grey,
                            ),
                            onTap: () {
                              controller.selectedCountry.value =
                                  Data.countries[index];
                              Get.back();
                            },
                          );
                        }),
                  ),
                ],
              ),
            );
          },
        );
      },
    ).then((value) {
      if (value != null) {
        // Handle the selected payment method
        print("Selected payment method: $value");
        // You can update your UI or state here
      }
    });
  }
}
