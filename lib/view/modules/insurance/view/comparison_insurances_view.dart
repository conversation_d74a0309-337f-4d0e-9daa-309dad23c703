import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../core/constants/color_constants.dart';
import '../../../../core/constants/padding_constants.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../../../../core/widgets/widgets.dart';

class ComparisonInsurancesView extends StatefulWidget {
  const ComparisonInsurancesView({super.key});

  @override
  State<ComparisonInsurancesView> createState() => _ComparisonInsurancesViewState();
}

class _ComparisonInsurancesViewState extends State<ComparisonInsurancesView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Compare Insurances"),
        actions: [],
      ),
      body: SingleChildScrollView(
        padding: PaddingConstants.screenPaddingHalf,
        child:
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Widgets.heightSpaceH2,
                  Container(

                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: ColorConstants.secondaryGreyColor,
                      border: Border.all(color: ColorConstants.secondaryColor),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: _buildProfile(
                                imageUrl: "", // replace with your image
                                name: "",
                                profession: "",
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(5.0),
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                child: Texts.textBold(
                                    'VS', color: ColorConstants.whiteColor,
                                    size: 12

                                ),
                              ),
                            ),
                            Expanded(
                              child: _buildProfile(
                                imageUrl:  "", // replace with your image
                                name:"",
                                profession: "",
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),


                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Top Profile Row

                      const SizedBox(height: 20),
                      // Comparison Info Cards

                      _buildComparisonRow('Plan Type',
                          "asdasd",
                          "asdada"),
                      _buildComparisonRow('Yearly Cost',
                          "asdasd",
                          "asdada"),
                      _buildComparisonRow('Coverage Value',
                          "asdasd",
                          "asdada"), _buildComparisonRow('Hospitals Covered',
                          "asdasd",
                          "asdada"), _buildComparisonRow('Out-Patient Limit',
                          "asdasd",
                          "asdada"),
                      _buildComparisonRow('In-Patient Limit',
                          "asdasd",
                          "asdada"),
                      _buildComparisonRow('Lab/Imaging Limit',
                          "asdasd",
                          "asdada"),

                      _buildComparisonRow('Lab/Imaging Limit',
                          "asdasd",
                          "asdada"),
                      _buildComparisonRow('Age Limit',
                          "asdasd",
                          "asdada"),

                      _buildComparisonRow('Waiting Period',
                          "asdasd",
                          "asdada"),
                      _buildComparisonRow('Covers Pre-Existing Conditions	',
                          "asdasd",
                          "asdada"),
                      _buildComparisonRow('Maternity Wait Time',
                          "asdasd",
                          "asdada"),
                    ],
                  ),
                ],
              ),

      ),
    );
  }

  Widget _buildProfile(
      {required String imageUrl, required String name, required String profession}) {
    return Column(
      children: [
        AdvancedAvatar(
          size: 50,
          child: Widgets.networkImage(imageUrl),
        ),
        Widgets.heightSpaceH1,
        Texts.textBold(
          name,


          size: 14,

        ),


      ],
    );
  }

  Widget _buildComparisonRow(String title, String value1, String value2) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[200]!)
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Texts.textBold(
              title,
              size: 12,
              color: ColorConstants.blackColor,
              textAlign: TextAlign.start,
            ),
          ),

          // Centered Divider
          Container(
            alignment: Alignment.center,
            width: 1,
            height: 20.h,
            color: ColorConstants.secondaryGreyColor,
          ),
          SizedBox(width: 10),
          Expanded(
            flex: 2,
            child: Texts.textNormal(
              value1,
              color: ColorConstants.secondaryGreyColor,

              size: 12,
            ),
          ),

          // Centered Divider
          Container(
            alignment: Alignment.center,
            width: 1,
            height: 20.h,

          ),
          SizedBox(width: 10),
          Expanded(
            flex: 2,
            child: Texts.textNormal(
              value2,

              size: 12,
            ),
          ),
        ],
      ),

    );
  }
}