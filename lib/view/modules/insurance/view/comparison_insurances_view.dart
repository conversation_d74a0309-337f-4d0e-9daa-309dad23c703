import 'package:ensuram/view/modules/insurance/view/insurance_summary_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../core/constants/color_constants.dart';
import '../../../../core/constants/padding_constants.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../../../../core/widgets/widgets.dart';
import '../controller/policy_controller.dart';
import '../model/insurances.dart';

class ComparisonInsurancesView extends StatefulWidget {
  const ComparisonInsurancesView({super.key});

  @override
  State<ComparisonInsurancesView> createState() => _ComparisonInsurancesViewState();
}

class _ComparisonInsurancesViewState extends State<ComparisonInsurancesView> {
  final controller = Get.find<PolicyController>();

  // Add a state variable to track selected insurance
  final RxInt selectedInsuranceIndex = 0.obs;

  @override
  void initState() {
    super.initState();
    // If only one insurance is selected, proceed directly to checkout
    // if (controller.selectedInsurances.length == 1) {
    //   WidgetsBinding.instance.addPostFrameCallback((_) {
    //     _proceedToCheckout(controller.selectedInsurances[0]);
    //   });
    // }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Compare Insurances"),
        actions: [],
      ),
      body: SingleChildScrollView(
        padding: PaddingConstants.screenPaddingHalf,
        child: Obx(()=>
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Widgets.heightSpaceH2,
              Container(
                padding: const EdgeInsets.all(16),
                decoration: Widgets.blockDecoration,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: _buildProfile(
                            imageUrl: controller.selectedInsurancesDetail.isNotEmpty ?
                                controller.selectedInsurancesDetail[0].insuranceLogo ?? "" : "",
                            name: controller.selectedInsurancesDetail.isNotEmpty ?
                                controller.selectedInsurancesDetail[0].insuranceName?? "" : "",
                            provider: controller.selectedInsurancesDetail.isNotEmpty ?
                                controller.selectedInsurancesDetail[0].planName ?? "" : "",
                          ),
                        ),
                        if (controller.selectedInsurancesDetail.length > 1)
                          Padding(
                            padding: const EdgeInsets.all(5.0),
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(5),
                              ),
                              child: Texts.textBold(
                                  'VS', color: ColorConstants.whiteColor,
                                  size: 12
                              ),
                            ),
                          ),
                        if (controller.selectedInsurancesDetail.length > 1)
                          Expanded(
                            child: _buildProfile(
                              imageUrl: controller.selectedInsurancesDetail[1].insuranceLogo?? "",
                              name: controller.selectedInsurancesDetail[1].insuranceName?? "",
                              provider: controller.selectedInsurancesDetail[1].insuranceName ?? "",
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),




              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 20),
                  // Comparison Info Cards
                  _buildComparisonRow('Plan Type',
                      controller.selectedInsurancesDetail.isNotEmpty ? controller.selectedInsurancesDetail[0].planName ?? "" : "",
                      controller.selectedInsurancesDetail.length > 1 ? controller.selectedInsurancesDetail[1].planName?? "" : ""),
                  _buildComparisonRow('Yearly Cost',
                      controller.selectedInsurancesDetail.isNotEmpty ? "${controller.selectedInsurancesDetail[0].yearCost??"0"} ${controller.selectedInsurancesDetail[0].insuranceCurrency??"\$"}" : "",
                      controller.selectedInsurancesDetail.length > 1 ? "${controller.selectedInsurancesDetail[1].yearCost??"0"} ${controller.selectedInsurancesDetail[1].insuranceCurrency??"\$"}" : ""),
                  _buildComparisonRow('Coverage Value',
                      controller.selectedInsurancesDetail.isNotEmpty ? "${controller.selectedInsurancesDetail[0].coverageValue??"0"} ${controller.selectedInsurancesDetail[0].insuranceCurrency??"\$"}" : "",
                      controller.selectedInsurancesDetail.length > 1 ? "${controller.selectedInsurancesDetail[1].coverageValue??"0"} ${controller.selectedInsurancesDetail[1].insuranceCurrency??"\$"}" : ""),
                  _buildComparisonRow('Hospitals Covered',
                      controller.selectedInsurancesDetail.isNotEmpty ? controller.selectedInsurancesDetail[0].hospitalCovered ?? "" : "",
                      controller.selectedInsurancesDetail.length > 1 ? controller.selectedInsurancesDetail[1].hospitalCovered ?? "" : ""),
                  _buildComparisonRow('Out-Patient Limit',
                      controller.selectedInsurancesDetail.isNotEmpty ? "${controller.selectedInsurancesDetail[0].outPatientLimit??"0"} ${controller.selectedInsurancesDetail[0].insuranceCurrency??"\$"}" : "",
                      controller.selectedInsurancesDetail.length > 1 ? "${controller.selectedInsurancesDetail[1].outPatientLimit??"0"} ${controller.selectedInsurancesDetail[1].insuranceCurrency??"\$"}" : ""),
                  _buildComparisonRow('In-Patient Limit',
                      controller.selectedInsurancesDetail.isNotEmpty ? "${controller.selectedInsurancesDetail[0].inPatientLimit??"0"} ${controller.selectedInsurancesDetail[0].insuranceCurrency??"\$"}" : "",
                      controller.selectedInsurancesDetail.length > 1 ? "${controller.selectedInsurancesDetail[1].inPatientLimit??"0"} ${controller.selectedInsurancesDetail[1].insuranceCurrency??"\$"}" : ""),
                  _buildComparisonRow('Lab/Imaging Limit',
                      controller.selectedInsurancesDetail.isNotEmpty ? "${controller.selectedInsurancesDetail[0].labImagingLimit??"0"} ${controller.selectedInsurancesDetail[0].insuranceCurrency??"\$"}" : "",
                      controller.selectedInsurancesDetail.length > 1 ? "${controller.selectedInsurancesDetail[1].labImagingLimit??"0"} ${controller.selectedInsurancesDetail[1].insuranceCurrency??"\$"}" : ""),
                  _buildComparisonRow('Age Limit',
                      controller.selectedInsurancesDetail.isNotEmpty ? controller.selectedInsurancesDetail[0].ageLimit ?? "" : "",
                      controller.selectedInsurancesDetail.length > 1 ? controller.selectedInsurancesDetail[1].ageLimit ?? "" : ""),
                  _buildComparisonRow('Waiting Period',
                      controller.selectedInsurancesDetail.isNotEmpty ? controller.selectedInsurancesDetail[0].waitingPeriod ?? "" : "",
                      controller.selectedInsurancesDetail.length > 1 ? controller.selectedInsurancesDetail[1].waitingPeriod ?? "" : ""),
                  _buildComparisonRow('Covers Pre-Existing Conditions',
                      controller.selectedInsurancesDetail.isNotEmpty ? controller.selectedInsurancesDetail[0].coversPreExistingConditions.toString() ?? "" : "",
                      controller.selectedInsurancesDetail.length > 1 ? controller.selectedInsurancesDetail[1].coversPreExistingConditions.toString() ?? "" : ""),
                  _buildComparisonRow('Maternity Wait Time',
                      controller.selectedInsurancesDetail.isNotEmpty ? controller.selectedInsurancesDetail[0].maternityServicesWaitTime ?? "" : "",
                      controller.selectedInsurancesDetail.length > 1 ? controller.selectedInsurancesDetail[1].maternityServicesWaitTime ?? "" : ""),
                ],
              ), // Replace the buttons section with this new implementation
              if (controller.selectedInsurancesDetail.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: Column(
                    children: [
                      if (controller.selectedInsurancesDetail.length > 1)
                        Column(
                          children: [
                            // First insurance option with checkbox
                            Container(
                              padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: selectedInsuranceIndex.value == 0
                                      ? ColorConstants.primaryColor
                                      : Colors.grey[300]!,
                                  width: 1.5,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Obx(() => Checkbox(
                                    value: selectedInsuranceIndex.value == 0,
                                    onChanged: (_) => selectedInsuranceIndex.value = 0,
                                    activeColor: ColorConstants.primaryColor,
                                  )),
                                  Expanded(
                                    child: Text(
                                      "Select ${controller.selectedInsurancesDetail[0].insuranceName}",
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: 8),
                            // Second insurance option with checkbox
                            Container(
                              padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: selectedInsuranceIndex.value == 1
                                      ? ColorConstants.primaryColor
                                      : Colors.grey[300]!,
                                  width: 1.5,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Obx(() => Checkbox(
                                    value: selectedInsuranceIndex.value == 1,
                                    onChanged: (_) => selectedInsuranceIndex.value = 1,
                                    activeColor: ColorConstants.primaryColor,
                                  )),
                                  Expanded(
                                    child: Text(
                                      "Select ${controller.selectedInsurancesDetail[1].insuranceName}",
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: 16),
                          ],
                        ),
                      // Single checkout button for both cases
                      ElevatedButton(
                        onPressed: () {
                          // Get the selected insurance based on index
                          controller.selectType.value = controller.selectedInsurancesDetail.length > 1
                              ? controller.selectedInsurancesDetail[selectedInsuranceIndex.value]
                              : controller.selectedInsurancesDetail[0];
                          Get.to(() => UserInsuranceSummaryView());
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ColorConstants.primaryColor,
                          minimumSize: Size(double.infinity, 50),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Texts.textBold(
                          "Proceed to Checkout",
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfile({
    required String imageUrl,
    required String name,
    required String provider
  }) {
    return Column(
      children: [
        AdvancedAvatar(
          size: 50,
          child: imageUrl.isNotEmpty ? Widgets.networkImage(imageUrl) : Icon(Icons.business, size: 30),
        ),
        Widgets.heightSpaceH1,
        Texts.textBold(
          name,
          size: 14,textAlign: TextAlign.center,
        ),

      ],
    );
  }

  Widget _buildComparisonRow(String title, String value1, String value2) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[200]!)
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Texts.textBold(
              title,
              size: 12,
              color: ColorConstants.blackColor,
              textAlign: TextAlign.start,
            ),
          ),

          // Centered Divider
          Container(
            alignment: Alignment.center,
            width: 1,
            height: 20.h,
            color: ColorConstants.secondaryGreyColor,
          ),
          SizedBox(width: 10),
          Expanded(
            flex: 2,
            child: Texts.textNormal(
              value1,
              color: ColorConstants.secondaryGreyColor,

              size: 12,
            ),
          ),

          if (value2.isNotEmpty) ...[
            // Centered Divider
            Container(
              alignment: Alignment.center,
              width: 1,
              height: 20.h,
              color: ColorConstants.secondaryGreyColor,
            ),
            SizedBox(width: 10),
            Expanded(
              flex: 2,
              child: Texts.textNormal(
                value2,
                size: 12,
              ),
            ),
          ],
        ],
      ),
    );
  }
}