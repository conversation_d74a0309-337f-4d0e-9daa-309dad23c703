import 'package:ensuram/view/modules/beneficiary/model/beneificery_model.dart';
import 'package:ensuram/view/modules/insurance/controller/policy_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/constants/color_constants.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_dropdown.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../../../../core/widgets/widgets.dart';

class InsuranceForm extends StatelessWidget {
  final PolicyController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(15.0),
        child: CustomButton(
          label: "Continue",
          onTap: () {
            controller.validateAndSubmit();
          },
        ),
      ),
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        leadingWidth: 30,
        centerTitle: true,
        title: Texts.textBlock("Buy policy",
            size: 20, fontWeight: FontWeight.w700),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Obx(
              () => CustomDropdown(
                color: ColorConstants.whiteColor,
                value: controller.numberOfPeople.value == 0
                    ? null
                    : controller.numberOfPeople.value.toString(),
                label: "Number of People to Insure",
                onTap: () {
                  showPersonCountBottomSheet(context);
                },
                textColor: controller.numberOfPeople.value == 0
                    ? Colors.black45
                    : Colors.black,
                hint: 'Select (1-10)',
              ),
            ),
            SizedBox(height: 16),
            Expanded(
              child: Obx(() => ListView.builder(
                    itemCount: controller.peopleList.length,
                    itemBuilder: (context, index) {
                      final person = controller.peopleList[index];
                      return Container(
                        decoration: Widgets.blockDecoration,
                        margin: EdgeInsets.symmetric(vertical: 8),
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomDropdown(
                                textColor: Colors.black,
                                color: ColorConstants.halfWhite,
                                value: person.beneficiary,
                                label: "Beneficiary ${index + 1}",
                                onTap: () {
                                  showBeneficiariesBottomSheet(context, person);
                                },
                                hint: 'Select here',
                              ),
                              DropdownButtonFormField(
                                decoration:
                                    InputDecoration(labelText: 'Smokes?'),
                                items: [
                                  DropdownMenuItem(
                                      value: 'Yes', child: Text('Yes')),
                                  DropdownMenuItem(
                                      value: 'No', child: Text('No')),
                                ],
                                onChanged: (value) {
                                  person.smokes = value.toString();
                                },
                              ),
                              DropdownButtonFormField(
                                decoration: InputDecoration(
                                    labelText: 'Health Condition'),
                                items: [
                                  DropdownMenuItem(
                                      value: 'Diabetes',
                                      child: Text('Diabetes')),
                                  DropdownMenuItem(
                                      value: 'Hypertension',
                                      child: Text('Hypertension')),
                                  DropdownMenuItem(
                                      value: 'HIV/AIDS',
                                      child: Text('HIV/AIDS')),
                                  DropdownMenuItem(
                                      value: 'Cancer', child: Text('Cancer')),
                                  DropdownMenuItem(
                                      value: 'COVID-19',
                                      child: Text('COVID-19')),
                                  DropdownMenuItem(
                                      value: 'Renal failure',
                                      child: Text('Renal failure')),
                                  DropdownMenuItem(
                                      value: 'Heart Ailments',
                                      child: Text('Heart Ailments')),
                                  DropdownMenuItem(
                                      value: 'Other health condition',
                                      child: Text('Other health condition')),
                                  DropdownMenuItem(
                                      value: 'No medical condition',
                                      child: Text('No medical condition')),
                                ],
                                onChanged: (value) {
                                  person.healthCondition = value.toString();
                                },
                              ),
                              DropdownButtonFormField(
                                decoration:
                                    InputDecoration(labelText: 'Past Surgery?'),
                                items: [
                                  DropdownMenuItem(
                                      value: 'Yes', child: Text('Yes')),
                                  DropdownMenuItem(
                                      value: 'No', child: Text('No')),
                                ],
                                onChanged: (value) {
                                  person.pastSurgery = value.toString();
                                },
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  )),
            ),
          ],
        ),
      ),
    );
  }

  void showPersonCountBottomSheet(BuildContext context) {
    final RxString selectedMethod = ''.obs;

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.6,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Texts.textBlock("Select one option",
                          size: 18, fontWeight: FontWeight.w600),
                      IconButton(
                        icon: Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  Divider(),
                  Expanded(
                    child: ListView(
                      controller: scrollController,
                      children: List.generate(8, (index) => index + 1)
                          .map((number) => Padding(
                                padding: const EdgeInsets.all(15.0),
                                child: GestureDetector(
                                  onTap: () {
                                    controller.updatePeople(number);
                                    Get.back();
                                  },
                                  child: Texts.textBlock("$number person(s)"),
                                ),
                              ))
                          .toList(),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    ).then((value) {
      if (value != null) {
        // Handle the selected payment method
        print("Selected payment method: $value");
        // You can update your UI or state here
      }
    });
  }

  void showBeneficiariesBottomSheet(BuildContext context, var person) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.8,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Column(
              children: [
                // Title
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Select Beneficiaries',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
                Divider(height: 1, color: Colors.grey[300]),
                // List of Beneficiaries
                Expanded(
                  child: Obx(() {
                    if (controller.benefieries.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.person_off,
                                size: 48, color: Colors.grey),
                            SizedBox(height: 16),
                            Text(
                              'No beneficiaries found',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      );
                    } else {
                      return ListView.builder(
                        controller: scrollController,
                        itemCount: controller.benefieries.length,
                        itemBuilder: (context, index) {
                          final beneficiary = controller.benefieries[index];
                          return ListTile(
                            leading: beneficiary.photo != null &&
                                    beneficiary.photo!.isNotEmpty
                                ? CircleAvatar(
                                    backgroundImage:
                                        NetworkImage(beneficiary.photo!),
                                    backgroundColor:
                                        ColorConstants.primaryColor,
                                  )
                                : CircleAvatar(
                                    backgroundColor:
                                        ColorConstants.primaryColor,
                                    child: Text(
                                      beneficiary.firstName?.substring(0, 1) ??
                                          "",
                                      style: TextStyle(color: Colors.white),
                                    ),
                                  ),
                            title: Text(
                              '${beneficiary.firstName ?? ""} ${beneficiary.lastName ?? ""}',
                              style:
                                  TextStyle(fontSize: 16, color: Colors.black),
                            ),
                            subtitle: Text(
                              beneficiary.beneficiaryId ?? 'No ID',
                              style:
                                  TextStyle(fontSize: 14, color: Colors.grey),
                            ),
                            trailing: Icon(
                              Icons.chevron_right,
                              color: Colors.grey,
                            ),
                            onTap: () {
                              person.beneficiary = beneficiary.firstName ?? "";
                              Get.back();
                              controller.update();
                            },
                          );
                        },
                      );
                    }
                  }),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
