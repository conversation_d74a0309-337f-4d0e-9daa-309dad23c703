import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/routes/app_routes.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';

class UserInsuranceSummaryView extends StatelessWidget {
  const UserInsuranceSummaryView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Insurance Plan",
            size: 20, fontWeight: FontWeight.w700),
        actions: [
          Image.asset(
            Assets.healthIcon,
            width: .20.sw,
          ),
          SizedBox(
            width: 10,
          )
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(children: [
              InkWell(
                onTap: () {},
                child: Container(color: Colors.grey, width: 50, height: 50),
              ),
              const SizedBox(
                width: 7.0,
              ),
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 5.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBlock("Insurance name",
                          fontWeight: FontWeight.w500,
                          maxline: 4,
                          color: Colors.black,
                          size: 18),
                      const SizedBox(
                        height: 4.0,
                      ),
                      Row(
                        children: [
                          Icon(
                            Icons.location_pin,
                            color: Colors.grey,
                            size: 14,
                          ),
                          Texts.textBlock(' Address',
                              fontWeight: FontWeight.w500,
                              color: Colors.black54,
                              size: 13),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ]),
            Widgets.heightSpaceH2,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                buildTextGroupColumn(key: "Start Date", value: "16.01.2025"),
                buildTextGroupColumn(
                    key: "Next Charge Date", value: "16.01.2025"),
              ],
            ),
            Widgets.heightSpaceH1,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                buildTextGroupColumn(key: "Yearly Payment", value: "272.2 USD"),
                buildTextGroupColumn(key: "Monthly Payment", value: "27 USD"),
              ],
            ),
            Widgets.heightSpaceH1,
            Divider(),
            Widgets.heightSpaceH1,
            Texts.textBlock("Order Summary",
                size: 18, fontWeight: FontWeight.w700),
            Widgets.heightSpaceH2,
            buildTextGroupRow(key: "Account email", value: "<EMAIL>"),
            buildTextGroupRow(key: "Insurance plan", value: "Silver"),
            buildTextGroupRow(key: "Beneficiary Country	", value: "Cameron"),
            buildTextGroupRow(key: "Account type", value: "Individual-family"),
            Widgets.heightSpaceH1,
            Divider(),

            Widgets.heightSpaceH1,
            Widgets.buildDropdown(
                textColor: Colors.black87,
                align: TextAlign.start,
                value: null,
                hint: "Billing Plan",
                onTap: () {},
                color: Colors.white),
            Widgets.heightSpaceH2,
            buildTextGroupRow(key: "Health Insurance", value: "\$25.00"),
            buildTextGroupRow(key: "Appointment Scheduler	", value: "\$25.00"),
            buildTextGroupRow(key: "Transaction fee", value: "\$25.00"),
            buildTextGroupRow(key: "Subtotal", value: "\$25.00"),
            buildTextGroupRow(key: "Tax", value: "\$25.00"),
            buildTextGroupRow(key: "Total", value: "\$125.00"),
            Widgets.heightSpaceH1,
            Divider(), Widgets.heightSpaceH1,
            Widgets.buildDropdown(
                textColor: Colors.black87,
                align: TextAlign.start,
                value: null,
                hint: "Choose Payment Method",
                onTap: () {},
                color: Colors.white),
            Widgets.heightSpaceH2,
            CustomButton(label: "Pay Now", onTap: () {Get.toNamed(AppRoutes.userBeneficiaries);}),

          ],
        ),
      ),
    );
  }

  buildTextGroupRow({String? key, String? value}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Texts.textBlock(key ?? "",
              fontWeight: FontWeight.w300,
              color: Colors.black54,
              maxline: 1,
              size: 15),
          SizedBox(
            width: 10,
          ),
          Flexible(
              child: Texts.textBlock(value ?? "",
                  fontWeight: FontWeight.w600,
                  maxline: 2,
                  color: Colors.black,
                  size: 16)),
        ],
      ),
    );
  }

  buildTextGroupColumn({String? key, String? value}) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Texts.textBlock(key ?? "",
              fontWeight: FontWeight.w300,
              color: Colors.black54,
              maxline: 1,
              size: 13),
          Texts.textBlock(value ?? "",
              fontWeight: FontWeight.w600,
              maxline: 1,
              color: Colors.black,
              size: 16),
        ],
      ),
    );
  }
}
