import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/routes/app_routes.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../controller/user_controller.dart';
import '../../../../core/constants/constants_list.dart';
import '../../../../core/widgets/custom_dropdown.dart';
import '../controller/policy_controller.dart';


class UserInsuranceSummaryView extends StatefulWidget {
  const UserInsuranceSummaryView({super.key});

  @override
  State<UserInsuranceSummaryView> createState() => _UserInsuranceSummaryViewState();
}

class _UserInsuranceSummaryViewState extends State<UserInsuranceSummaryView> {
  final controller = Get.find<PolicyController>();
@override
initState() {
    super.initState();  controller.selectedSubscription.value = "yearly";
    controller.setTotalAmount(isMonthly: false);

  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Checkout Summary",
            size: 20, fontWeight: FontWeight.w700),

      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15.0),
        child: Obx(()=>Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              InkWell(
                onTap: () {},
                child: Widgets.networkImage(controller.selectType.value.insuranceLogo??"",height: 100,width: 100),
              ),
              Texts.textBlock(controller.selectType.value.insuranceName??"",
                  fontWeight: FontWeight.w500,
                  maxline: 4,
                  color: Colors.black,
                  size: 18),


          Widgets.heightSpaceH2,
              buildTextGroupRow(key: "Plan Type", value: controller.selectType.value.planName??""),
              buildTextGroupRow(key: "Yearly Cost (Base)", value: "${controller.selectType.value.yearCost??"0"} ${controller.selectType.value.insuranceCurrency??"\$"}"),
              buildTextGroupRow(key: "Coverage Value", value: "${controller.selectType.value.coverageValue??"0"} ${controller.selectType.value.insuranceCurrency??"\$"}"),


              buildTextGroupRow(key: "Hospitals Covered", value: "${controller.selectType.value.hospitalCovered??"0"}"),
              buildTextGroupRow(key: "Number of People", value: "${controller.peopleList.length??"0"}"),
              CustomDropdown(
                textColor: Colors.black,
                color: ColorConstants.whiteColor,
                value: controller.selectedSubscription.value,
                label: "Subscription Type",
                onTap: () {
showTypes(context);
                },
                hint: 'Select here',
              ),     Widgets.heightSpaceH2,
              buildTextGroupRow(key: "Total", value: "${controller.totalAmount.value} ${controller.selectType.value.insuranceCurrency??"\$"}"),

              buildTextGroupRow(key: "Wallet balance", value: "${Get.find<UserController>().userModel?.accountBalnce ?? "0.00"} \$"),
              Widgets.heightSpaceH1,
              Divider(), Widgets.heightSpaceH1,

              Widgets.heightSpaceH2,
              CustomButton(label: "Pay Now", onTap: () {

                controller.buyPolicy();
              }),

            ],
          ),
        ),
      ),
    );
  }

  buildTextGroupRow({String? key, String? value}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Texts.textBlock(key ?? "",
              fontWeight: FontWeight.w300,
              color: Colors.black54,
              maxline: 1,
              size: 15),
          SizedBox(
            width: 10,
          ),
          Flexible(
              child: Texts.textBlock(value ?? "",
                  fontWeight: FontWeight.w600,
                  maxline: 2,
                  color: Colors.black,
                  size: 16)),
        ],
      ),
    );
  }
  void showTypes(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.6,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Texts.textBlock("Select Subscription Type",
                          size: 18, fontWeight: FontWeight.w600),
                      IconButton(
                        icon: Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  Divider(),
                  Expanded(
                    child: ListView.builder(
                      itemCount: Data.subscriptionTypes.length,
                      itemBuilder: (context, index) {
                        return ListTile(
                          title: Text(
                            Data.subscriptionTypes[index],
                            style: TextStyle(fontSize: 16, color: Colors.black),
                          ),
                          subtitle: Text(
                            index == 0
                                ? "(${((double.tryParse(controller.selectType.value.yearCost ?? "0") ?? 0) / 12).toStringAsFixed(2)} ${controller.selectType.value.insuranceCurrency ?? "\$"}/month)"
                                : "(${controller.selectType.value.yearCost ?? "0"} ${controller.selectType.value.insuranceCurrency ?? "\$"}/year)",
                            style: TextStyle(fontSize: 14, color: Colors.grey),
                          ),
                          trailing: Icon(
                            Icons.chevron_right,
                            color: Colors.grey,
                          ),
                          onTap: () {
                            controller.selectedSubscription.value = Data.subscriptionTypes[index];
                            // Calculate total amount based on subscription type
                            controller.setTotalAmount(isMonthly: index == 0);
                            Get.back();
                          },
                        );
                      }
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  buildTextGroupColumn({String? key, String? value}) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Texts.textBlock(key ?? "",
              fontWeight: FontWeight.w300,
              color: Colors.black54,
              maxline: 1,
              size: 13),
          Texts.textBlock(value ?? "",
              fontWeight: FontWeight.w600,
              maxline: 1,
              color: Colors.black,
              size: 16),
        ],
      ),
    );
  }
}
