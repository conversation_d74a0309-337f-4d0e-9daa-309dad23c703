class Insurances {
  String? itemId;
  String? planName;
  String? yearCost;
  String? hospitalCovered;
  String? coverageValue;
  String? insuranceId;
  String? insuranceName;
  String? insuranceLogo;
  String? currency;

  Insurances(
      {this.itemId,
        this.planName,
        this.yearCost,
        this.hospitalCovered,
        this.coverageValue,
        this.insuranceId,
        this.insuranceName,
        this.insuranceLogo,
        this.currency});

  Insurances.fromJson(Map<String, dynamic> json) {
    itemId = json['item_id'].toString();
    planName = json['plan_name'];
    yearCost = json['year_cost'].toString();
    hospitalCovered = json['hospital_covered'].toString();
    coverageValue = json['coverage_value'].toString();
    insuranceId = json['insurance_id'].toString();
    insuranceName = json['insurance_name'];
    insuranceLogo = json['insurance_logo'];
    currency = json['currency'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['item_id'] = this.itemId;
    data['plan_name'] = this.planName;
    data['year_cost'] = this.yearCost;
    data['hospital_covered'] = this.hospitalCovered;
    data['coverage_value'] = this.coverageValue;
    data['insurance_id'] = this.insuranceId;
    data['insurance_name'] = this.insuranceName;
    data['insurance_logo'] = this.insuranceLogo;
    data['currency'] = this.currency;
    return data;
  }
}
class Provider {
  String? id;
  String? name;


  Provider (
      {this.id,
        this.name});

  Provider .fromJson(Map<String, dynamic> json) {
    id = json['id'].toString();
    name = json['name'];
   
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
  
    return data;
  }
}
class PolicyDetail {
  String? itemId;
  String? planName;
  String? yearCost;
  String? hospitalCovered;
  String? coverageValue;
  String? outPatientLimit;
  String? inPatientLimit;
  String? labImagingLimit;
  String? ageLimit;
  String? waitingPeriod;
  String? coversPreExistingConditions;
  String? maternityServicesWaitTime;
  String? insuranceId;
  String? insuranceName;
  String? insuranceLogo;
  String? insuranceCurrency;

  PolicyDetail(
      {this.itemId,
        this.planName,
        this.yearCost,
        this.hospitalCovered,
        this.coverageValue,
        this.outPatientLimit,
        this.inPatientLimit,
        this.labImagingLimit,
        this.ageLimit,
        this.waitingPeriod,
        this.coversPreExistingConditions,
        this.maternityServicesWaitTime,
        this.insuranceId,
        this.insuranceName,
        this.insuranceLogo,
        this.insuranceCurrency});

  PolicyDetail.fromJson(Map<String, dynamic> json) {
    itemId = json['item_id'].toString();
    planName = json['plan_name'];
    yearCost = json['year_cost'].toString();
    hospitalCovered = json['hospital_covered'].toString();
    coverageValue = json['coverage_value'].toString();
    outPatientLimit = json['out_patient_limit'].toString();
    inPatientLimit = json['in_patient_limit'].toString();
    labImagingLimit = json['lab_imaging_limit'].toString();
    ageLimit = json['age_limit'].toString();
    waitingPeriod = json['waiting_period'].toString();
    coversPreExistingConditions = json['covers_pre_existing_conditions'].toString();
    maternityServicesWaitTime = json['maternity_services_wait_time'].toString();
    insuranceId = json['insurance_id'];
    insuranceName = json['insurance_name'];
    insuranceLogo = json['insurance_logo'];
    insuranceCurrency = json['insurance_currency'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['item_id'] = this.itemId;
    data['plan_name'] = this.planName;
    data['year_cost'] = this.yearCost;
    data['hospital_covered'] = this.hospitalCovered;
    data['coverage_value'] = this.coverageValue;
    data['out_patient_limit'] = this.outPatientLimit;
    data['in_patient_limit'] = this.inPatientLimit;
    data['lab_imaging_limit'] = this.labImagingLimit;
    data['age_limit'] = this.ageLimit;
    data['waiting_period'] = this.waitingPeriod;
    data['covers_pre_existing_conditions'] = this.coversPreExistingConditions;
    data['maternity_services_wait_time'] = this.maternityServicesWaitTime;
    data['insurance_id'] = this.insuranceId;
    data['insurance_name'] = this.insuranceName;
    data['insurance_logo'] = this.insuranceLogo;
    data['insurance_currency'] = this.insuranceCurrency;
    return data;
  }
}
