import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/routes/app_routes.dart';
import 'package:ensuram/view/modules/insurance/view/insurance_order_view.dart';
import 'package:ensuram/view/modules/laboratory/view/lab_order_view.dart';
import 'package:ensuram/view/modules/laboratory/view/lab_tests_view.dart';
import 'package:ensuram/view/modules/pharmacy/view/pharmacy_order_view.dart';
import 'package:ensuram/view/modules/pharmacy/view/pharmacy_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_rx/src/rx_typedefs/rx_typedefs.dart';

import '../../../../core/widgets/text_widgets.dart';
import '../../../../core/widgets/widgets.dart';

class ShopView extends StatefulWidget {
  const ShopView({super.key});

  @override
  State<ShopView> createState() => _ShopViewState();
}

class _ShopViewState extends State<ShopView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [
            Center(
                child: Texts.textBlock("Shop one of our services",
                    size: 17, align: TextAlign.center)),
            Widgets.heightSpaceH2,
            imageBlockCard(imageName: Assets.insuranceImage,title:"View/Book lab test",onTap: (){
              Get.to(()=>LabOrderView());
            }),
            Widgets.heightSpaceH2,
            imageBlockCard(imageName: Assets.medImage,title:"View/Buy medicines",onTap: (){

              Get.to(()=>PharmacyOrderView());
            }),
            Widgets.heightSpaceH2,
            imageBlockCard(imageName: Assets.insuranceImage,title:"View/Book Insurance",onTap: (){

              Get.to(()=>InsuranceOrderView());

            })
          ],
        ),
      ),
    );
  }

   imageBlockCard({String? imageName,String? title,Callback? onTap}) {
    return InkWell(onTap:onTap ,
      child: Stack(
              alignment: Alignment.center,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: SizedBox(
                    width: 1.sw,
                    height: 80,
                    child: Image.asset(
                     imageName??"",
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: Container(
                    width: 1.sw,
                    height: 80,
                    color: Colors.black38,
                  ),
                ),
                Texts.textBlock(title??"",
                    size: 17, align: TextAlign.center, color: Colors.white),
              ],
            ),
    );
  }
}
