class Appointment {
  String? hospital;
  String? specialty;
  String? doctor;
  String? beneficiary;
  String? date;
  String? time;
  String? type;
  String? reason;String? id;
  String? doneOn;
  String? country;
  bool? isSubscriptionValid;

  Appointment(
      {this.hospital,
        this.specialty,
        this.doctor,this.id,
        this.beneficiary,
        this.date,
        this.time,
        this.type,
        this.reason,
        this.doneOn,
        this.country,
        this.isSubscriptionValid});

  Appointment.fromJson(Map<String, dynamic> json) {
    hospital = json['hospital'];
    specialty = json['specialty'];
    doctor = json['doctor'];
    beneficiary = json['beneficiary'];id = json['appointment_id'].toString();
    date = json['date'];
    time = json['time'];
    type = json['type'];
    reason = json['reason'];
    doneOn = json['done_on'];
    country = json['country'];
    isSubscriptionValid = json['is_subscription_valid'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['hospital'] = this.hospital;
    data['specialty'] = this.specialty;
    data['doctor'] = this.doctor;
    data['beneficiary'] = this.beneficiary;data['appointment_id'] = this.id;
    data['date'] = this.date;
    data['time'] = this.time;
    data['type'] = this.type;
    data['reason'] = this.reason;
    data['done_on'] = this.doneOn;
    data['country'] = this.country;
    data['is_subscription_valid'] = this.isSubscriptionValid;
    return data;
  }
}
