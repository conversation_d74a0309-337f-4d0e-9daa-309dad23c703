import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../controller/book_appointment_controller.dart';

class BookAppointmentView extends StatelessWidget {
  const BookAppointmentView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(BookAppointmentController());

    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Book Appointment",
            size: 17, fontWeight: FontWeight.w700),
        actions: [],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [
            Widgets.heightSpaceH2,
            Obx(() => CustomDropdown(textColor: Colors.black,
              value: controller.selectedCountry.value.isEmpty ? null : controller.selectedCountry.value,
              label: "Country",
              onTap: () {
                showCountriesBottomSheet(context, controller);
              },
              hint: 'Select here',
            )),
            Widgets.heightSpaceH2,
            Obx(() => CustomDropdown(textColor: Colors.black,
              value: controller.selectedHospitalName.value.isEmpty ? null : controller.selectedHospitalName.value,
              label: "Hospital",
              onTap: () {
                if (controller.selectedCountry.value.isEmpty) {
                  Widgets.showSnackBar("Error", "Please select a country first");
                  return;
                }
                showHospitalsBottomSheet(context, controller);
              },
              hint: 'Select here',
            )),
            Widgets.heightSpaceH2,
            Obx(() => CustomDropdown(textColor: Colors.black,
              value: controller.selectedDoctorName.value.isEmpty ? null : controller.selectedDoctorName.value,
              label: "Doctor",
              onTap: () {
                if (controller.selectedHospitalId.value.isEmpty) {
                  Widgets.showSnackBar("Error", "Please select a hospital first");
                  return;
                }
                showDoctorsBottomSheet(context, controller);
              },
              hint: 'Select here',
            )),
            Widgets.heightSpaceH2,
            Obx(() => CustomDropdown(textColor: Colors.black,
              value: controller.selectedBeneficiaryName.value.isEmpty ? null : controller.selectedBeneficiaryName.value,
              label: "Beneficiary",
              onTap: () {
                showBeneficiariesBottomSheet(context, controller);
              },
              hint: 'Select here',
            )),
            Widgets.heightSpaceH2,
            Obx(() => CustomDropdown(textColor: Colors.black,
              value: controller.selectedDate.value == null ? null : DateFormat('MM/dd/yyyy').format(controller.selectedDate.value!),
              label: "Appointment Date",
              onTap: () async {
                final DateTime? picked = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now().add(Duration(days: 1)),
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(Duration(days: 90)),
                );
                if (picked != null) {
                  controller.selectedDate.value = picked;
                }
              },
              hint: 'mm/dd/yyyy',
            )),
            Widgets.heightSpaceH2,
            Obx(() => CustomDropdown(textColor: Colors.black,
              value: controller.selectedTime.value.isEmpty ? null : controller.selectedTime.value,
              label: "Appointment Time",
              onTap: () {
                if (controller.selectedDoctorId.value.isEmpty) {
                  Widgets.showSnackBar("Error", "Please select a doctor first");
                  return;
                }
                if (controller.selectedDate.value == null) {
                  Widgets.showSnackBar("Error", "Please select a date first");
                  return;
                }
                showTimeSlotBottomSheet(context, controller);
              },
              hint: 'Select time slot',
            )),
            Widgets.heightSpaceH2,
            Obx(() => CustomDropdown(textColor: Colors.black,
              value: controller.selectedAppointmentType.value.isEmpty ? null : controller.selectedAppointmentType.value,
              label: "Appointment Type",
              onTap: () {
                showAppointmentTypesBottomSheet(context, controller);
              },
              hint: 'Select here',
            )),
            Widgets.heightSpaceH2,
            EntryField(
              controller: controller.reasonController,
              label: "Appointment Reason",
              hint: "Write here",
              borderRadius: 10,

            ),
            Widgets.heightSpaceH2,
            CustomButton(
              onTap: () => controller.bookAppointment(),
              label: "BOOK APPOINTMENT",
            )
          ],
        ),
      ),
    );
  }

  void showCountriesBottomSheet(BuildContext context, BookAppointmentController controller) {
    final countries = ["Cameroon", "Cote D'Ivoire", "Ghana", "Nigeria"];

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Select Country',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ),
            Divider(height: 1, color: Colors.grey[300]),
            Expanded(
              child: ListView.builder(
                itemCount: countries.length,
                itemBuilder: (context, index) {
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: ColorConstants.primaryColor,
                      child: Text(
                        countries[index][0],
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                    title: Text(
                      countries[index],
                      style: TextStyle(fontSize: 16, color: Colors.black),
                    ),
                    trailing: Icon(
                      Icons.chevron_right,
                      color: Colors.grey,
                    ),
                    onTap: () {
                      controller.selectCountry(countries[index]);
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  void showHospitalsBottomSheet(BuildContext context, BookAppointmentController controller) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.8,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Select Hospital',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
                Divider(height: 1, color: Colors.grey[300]),
                Expanded(
                  child: Obx(() {
                    if (controller.isLoadingHospitals.value) {
                      return Center(child: CircularProgressIndicator());
                    } else if (controller.hospitals.isEmpty) {
                      return Center(
                        child: Text('No hospitals found for this country'),
                      );
                    } else {
                      return ListView.builder(
                        controller: scrollController,
                        itemCount: controller.hospitals.length,
                        itemBuilder: (context, index) {
                          final hospital = controller.hospitals[index];
                          return ListTile(
                            leading: CircleAvatar(
                              backgroundColor: ColorConstants.primaryColor,
                              child: Text(
                                hospital.id ?? "",
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                            title: Text(
                              hospital.name ?? 'Unknown Hospital',
                              style: TextStyle(fontSize: 16, color: Colors.black),
                            ),

                            trailing: Icon(
                              Icons.chevron_right,
                              color: Colors.grey,
                            ),
                            onTap: () {
                              controller.selectHospital(hospital);
                              Navigator.pop(context);
                            },
                          );
                        },
                      );
                    }
                  }),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void showDoctorsBottomSheet(BuildContext context, BookAppointmentController controller) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.8,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Select Doctor',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
                Divider(height: 1, color: Colors.grey[300]),
                Expanded(
                  child: Obx(() {
                    if (controller.isLoadingDoctors.value) {
                      return Center(child: CircularProgressIndicator());
                    } else if (controller.doctors.isEmpty) {
                      return Center(
                        child: Text('No doctors found for this hospital'),
                      );
                    } else {
                      return ListView.builder(
                        controller: scrollController,
                        itemCount: controller.doctors.length,
                        itemBuilder: (context, index) {
                          final doctor = controller.doctors[index];
                          return ListTile(
                            leading: CircleAvatar(
                              backgroundColor: ColorConstants.primaryColor,
                              child: Text(
                                doctor.name?[0] ?? 'D',
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                            title: Text(
                              doctor.name ?? 'Unknown Doctor',
                              style: TextStyle(fontSize: 16, color: Colors.black),
                            ),
                            subtitle: Text(
                              doctor.specialty ?? 'No specialty',
                              style: TextStyle(fontSize: 14, color: Colors.grey),
                            ),
                            trailing: Icon(
                              Icons.chevron_right,
                              color: Colors.grey,
                            ),
                            onTap: () {
                              controller.selectDoctor(doctor);
                              Navigator.pop(context);
                            },
                          );
                        },
                      );
                    }
                  }),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void showBeneficiariesBottomSheet(BuildContext context, BookAppointmentController controller) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.8,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Select Beneficiary',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
                Divider(height: 1, color: Colors.grey[300]),
                Expanded(
                  child: Obx(() {
                    if (controller.isLoadingBeneficiaries.value) {
                      return Center(child: CircularProgressIndicator());
                    } else if (controller.beneficiaries.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.person_off, size: 48, color: Colors.grey),
                            SizedBox(height: 16),
                            Text(
                              'No beneficiaries found',
                              style: TextStyle(color: Colors.grey),
                            ),

                          ],
                        ),
                      );
                    } else {
                      return ListView.builder(
                        controller: scrollController,
                        itemCount: controller.beneficiaries.length,
                        itemBuilder: (context, index) {
                          final beneficiary = controller.beneficiaries[index];
                          return ListTile(
                            leading: beneficiary.photo != null && beneficiary.photo!.isNotEmpty
                                ? CircleAvatar(
                                    backgroundImage: NetworkImage(beneficiary.photo!),
                                    backgroundColor: ColorConstants.primaryColor,
                                  )
                                : CircleAvatar(
                                    backgroundColor: ColorConstants.primaryColor,
                                    child: Text(
                                      '${beneficiary.name?.substring(0, 1) ?? ""}',
                                      style: TextStyle(color: Colors.white),
                                    ),
                                  ),
                            title: Text(
                              '${beneficiary.name ?? ""}',
                              style: TextStyle(fontSize: 16, color: Colors.black),
                            ),

                            trailing: Icon(
                              Icons.chevron_right,
                              color: Colors.grey,
                            ),
                            onTap: () {
                              controller.selectBeneficiary(beneficiary);
                              Navigator.pop(context);
                            },
                          );
                        },
                      );
                    }
                  }),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void showAppointmentTypesBottomSheet(BuildContext context, BookAppointmentController controller) {
    final appointmentTypes = ["New Patient Appointment", "Follow-up Appointment","Others"];

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Select Appointment Type',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ),
            Divider(height: 1, color: Colors.grey[300]),
            Expanded(
              child: ListView.builder(
                itemCount: appointmentTypes.length,
                itemBuilder: (context, index) {
                  return ListTile(

                    title: Text(
                      appointmentTypes[index],
                      style: TextStyle(fontSize: 16, color: Colors.black),
                    ),
                    trailing: Icon(
                      Icons.chevron_right,
                      color: Colors.grey,
                    ),
                    onTap: () {
                      controller.selectedAppointmentType.value = appointmentTypes[index];
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  void showTimeSlotBottomSheet(BuildContext context, BookAppointmentController controller) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.8,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Select Available Time Slot',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
                Divider(height: 1, color: Colors.grey[300]),
                Expanded(
                  child: Obx(() {
                    if (controller.isLoadingTimeSlots.value) {
                      return Center(child: CircularProgressIndicator());
                    } else if (controller.timeSlots.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.access_time, size: 48, color: Colors.grey),
                            SizedBox(height: 16),
                            Text(
                              'No available time slots for this date',
                              style: TextStyle(color: Colors.grey),
                            ),
                            TextButton(
                              onPressed: () {
                                Get.back();
                                // Let user select a different date
                              },
                              child: Text('Select a different date'),
                            )
                          ],
                        ),
                      );
                    } else {
                      return GridView.builder(
                        controller: scrollController,
                        padding: EdgeInsets.all(16),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          childAspectRatio: 2.0,
                          crossAxisSpacing: 10,
                          mainAxisSpacing: 10,
                        ),
                        itemCount: controller.timeSlots.length,
                        itemBuilder: (context, index) {
                          final timeSlot = controller.timeSlots[index];
                          return InkWell(
                            onTap: () {
                              controller.selectedTime.value = timeSlot;
                              Navigator.pop(context);
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: ColorConstants.primaryColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: ColorConstants.primaryColor,
                                  width: 1,
                                ),
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                timeSlot,
                                style: TextStyle(
                                  color: ColorConstants.primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          );
                        },
                      );
                    }
                  }),
                ),
              ],
            );
          },
        );
      },
    );
  }

}
