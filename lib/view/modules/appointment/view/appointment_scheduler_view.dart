import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/routes/app_routes.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:ensuram/view/modules/beneficiary/view/add_benefieries_view.dart';
import 'package:ensuram/view/modules/beneficiary/view/benefieries_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../dashboard/controller/dashboard_controller.dart';
import '../controller/appointmnet_controller.dart';

class AppointmentSchedularView extends StatelessWidget {
  AppointmentController controller = Get.find();

  @override
  Widget build(BuildContext context) {controller.fetchBeneficiaries();
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Add Appointment Scheduler",
            size: 17, fontWeight: FontWeight.w700),
        actions: [],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Widgets.heightSpaceH2,
            Obx(() => CustomDropdown(textColor: Colors.black,
              value: controller.selectedBeneficiaryName.value.isEmpty ? null : "${controller.selectedBeneficiaryName.value}",
              label: "Beneficiaries",
              onTap: () {
                showBeneficiariesBottomSheet(context);
              },
              hint: 'Select here',
            )),
            Widgets.heightSpaceH2,
            RichText(
                text: TextSpan(
                    text: 'Add a beneficiary ',
                    style: TextStyle(color: Colors.black45, fontSize: 10),
                    children: [
                  TextSpan(
                    text: 'here',
                    style: TextStyle(
                      color: Colors.blue,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Get.to(() => AddBeneficiaryView())?.then((_) {
                          // Refresh beneficiaries list when returning from add screen
                          controller.fetchBeneficiaries();
                        });
                      },
                  ),
                  TextSpan(
                      text: ', then refresh this page to see changes',
                      style: TextStyle(
                        color: Colors.black45,
                        fontSize: 10,
                      )),
                ])),
            Widgets.heightSpaceH2,
            Obx(() => CustomDropdown(textColor: Colors.black,
              value: controller.selectedSubscriptionType.value.isEmpty ? null : controller.selectedSubscriptionType.value,
              label: "Subscription Type",
              onTap: () async {
                showSubscriptionsBottomSheet(context).then((value) {
                  if (value != null) {
                    controller.selectedSubscriptionType.value = value;
                  }
                });
              },
              hint: 'Select here',
            )),
            Widgets.heightSpaceH2,
            CustomButton(
              onTap: controller.submitAppointmentSchedular,
              label: "SUBMIT",
            )
          ],
        ),
      ),
    );
  }

  void showBeneficiariesBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.8,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Column(
              children: [
                // Title
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Select Beneficiaries',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
                Divider(height: 1, color: Colors.grey[300]),
                // List of Beneficiaries
                Expanded(
                  child: Obx(() {
                    if (controller.isLoading.value) {
                      return Center(child: CircularProgressIndicator());
                    } else if (controller.beneficiaries.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.person_off, size: 48, color: Colors.grey),
                            SizedBox(height: 16),
                            Text(
                              'No beneficiaries found',
                              style: TextStyle(color: Colors.grey),
                            ),

                          ],
                        ),
                      );
                    } else {
                      return ListView.builder(
                        controller: scrollController,
                        itemCount: controller.beneficiaries.length,
                        itemBuilder: (context, index) {
                          final beneficiary = controller.beneficiaries[index];
                          return ListTile(
                            leading: beneficiary.photo != null && beneficiary.photo!.isNotEmpty
                                ? CircleAvatar(
                                    backgroundImage: NetworkImage(beneficiary.photo!),
                                    backgroundColor: ColorConstants.primaryColor,
                                  )
                                : CircleAvatar(
                                    backgroundColor: ColorConstants.primaryColor,
                                    child: Text(
                                      beneficiary.firstName?.substring(0, 1) ?? "",
                                      style: TextStyle(color: Colors.white),
                                    ),
                                  ),
                            title: Text(
                              '${beneficiary.firstName ?? ""} ${beneficiary.lastName ?? ""}',
                              style: TextStyle(fontSize: 16, color: Colors.black),
                            ),
                            subtitle: Text(
                              beneficiary.beneficiaryId?? 'No ID',
                              style: TextStyle(fontSize: 14, color: Colors.grey),
                            ),
                            trailing: Icon(
                              Icons.chevron_right,
                              color: Colors.grey,
                            ),
                            onTap: () {
                              controller.selectBeneficiary(beneficiary);
                              Get.back();
                            },
                          );
                        },
                      );
                    }
                  }),
                ),
              ],
            );
          },
        );
      },
    );
  }

  showSubscriptionsBottomSheet(BuildContext context) {
    // Existing code remains the same
    return showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.8,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Column(
              children: [
                // Title
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Select Subscriptions Type',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
                Divider(height: 1, color: Colors.grey[300]),
                // List of Subscription Types
                ListTile(
                  leading: CircleAvatar(
                    backgroundColor: ColorConstants.primaryColor,
                    child: Text(
                      '1',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                  title: Text(
                    'Monthly',
                    style: TextStyle(fontSize: 16, color: Colors.black),
                  ),
                  subtitle: Text(
                    '(\$2.99 /person)',
                    style: TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                  trailing: Icon(
                    Icons.chevron_right,
                    color: Colors.grey,
                  ),
                  onTap: () {
                   Get.back(result: "Monthly(\$2.99 /person)");
                  },
                ),
                ListTile(
                  leading: CircleAvatar(
                    backgroundColor: ColorConstants.primaryColor,
                    child: Text(
                      '2',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                  title: Text(
                    'Yearly',
                    style: TextStyle(fontSize: 16, color: Colors.black),
                  ),
                  subtitle: Text(
                    '(\$35.88 /person)',
                    style: TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                  trailing: Icon(
                    Icons.chevron_right,
                    color: Colors.grey,
                  ),
                  onTap: () {
                    Get.back(result: "Yearly(\$35.88 /person)");
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }
}