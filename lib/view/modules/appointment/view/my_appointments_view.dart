import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/routes/app_routes.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:intl/intl.dart';

import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_dialog.dart';
import '../../../../core/widgets/entry_field.dart';
import '../../../../core/widgets/shimmer_effect.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../../../../core/widgets/widgets.dart';
import '../../beneficiary/model/beneificery_model.dart';
import '../../beneficiary/view/update_benefieries_view.dart';
import '../controller/appointmnet_controller.dart';
import '../model/appointment_model.dart';

class MyAppointmentsView extends StatefulWidget {
  const MyAppointmentsView({super.key});

  @override
  State<MyAppointmentsView> createState() => _MyAppointmentsViewState();
}

class _MyAppointmentsViewState extends State<MyAppointmentsView> {
  late AppointmentController controller;

  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    controller = Get.put(AppointmentController());

    controller.fetchAppointments(page: 1);
    scrollController.addListener(scrollListener);
  }

  scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent &&
        controller.totalCount.value > controller.appointments.length) {
      controller.fetchAppointments(page: controller.currentPage.value + 1);
      controller.currentPage.value++; // Increment the page counter
    }
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      floatingActionButton: FloatingActionButton.extended(
        backgroundColor: ColorConstants.secondaryColor,
        icon: Icon(Icons.add),
        onPressed: () {
          showDialog(
              context: context,
              builder: (context) => CustomAlertDialog(
                    onDone: () {
                      Get.back();
                      Get.toNamed(AppRoutes.appointmentScheduler);
                    },
                    title: "Appointment",
                    subtitle:
                        "To book an appointment, please subscribe a beneficiary to the Appointment Scheduler. If the beneficiary is already subscribed with an active plan, you may proceed to book.",
                    onCancel: () { Get.back();
                      Get.toNamed(AppRoutes.bookAppointment);
                    },
                    cancelButtonTitle: "Book Appointment",
                    doneButtonTitle: "Add Service",
                  ));
        },
        label: Text("Book appointment"),
        foregroundColor: Colors.white,
      ),
      body:widgetListView(),
    );
  }


  Widget widgetListView() {
    return Obx(() {
      return SingleChildScrollView(
        controller: scrollController,
        padding: EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(10)),
              child: TextField(
                textAlignVertical: TextAlignVertical.center,
                // focusNode: _searchFocusNode,
                // controller: spotController.searchController,
                onChanged: (value) {},
                decoration: InputDecoration(
                    prefixIcon: IconButton(
                      padding: EdgeInsets.zero,
                      icon: const Icon(CupertinoIcons.search,
                          color: Colors.black38, size: 23),
                      onPressed: () {
                        // Perform the search here
                      },
                    ),
                    hintStyle: const TextStyle(
                        color: Colors.black38,
                        fontSize: 15,
                        decoration: TextDecoration.none),
                    border: InputBorder.none,
                    hintText: "Search Appointments"),
                style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 15,
                    decoration: TextDecoration.none),
              ),
            ),
            Widgets.heightSpaceH2,
            controller.isLoading.value
                ? const ShimmerListSkeleton()
                : controller.appointments.isNotEmpty
                ? ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  Appointment appointment =
                  controller.appointments[index];
                  return Widgets.appointmentCard(appointment,() {
                    controller.cancelAppointment(appointment.id ?? "");
                  },() {
                    _showEditAppointmentDialog(context, appointment);
                  });
                },
                separatorBuilder: (context, index) {
                  return Widgets.heightSpaceH1;
                },
                itemCount: controller.appointments.length ?? 0)
                : Widgets.noRecordsFound(
                title: "You have no appointments"),
            if (controller.isLoading.value)
              Center(child: CircularProgressIndicator()),
            Widgets.heightSpaceH2,
            Widgets.heightSpaceH2,
            Widgets.heightSpaceH2,
          ],
        ),
      );
    });
  }

  void _showEditAppointmentDialog(BuildContext context, Appointment appointment) {
    // Create temporary variables to hold edited values
    DateTime? selectedDate = appointment.date != null ?
        DateFormat('yyyy-MM-dd').parse(appointment.date!) : null;
    String selectedTime = appointment.time ?? '';
    final reasonController = TextEditingController(text: appointment.reason ?? '');

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return Dialog(backgroundColor: ColorConstants.halfWhite,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: Container(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Texts.textBlock("Edit Appointment",
                        size: 18,
                        fontWeight: FontWeight.bold
                      ),
                      IconButton(
                        icon: Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                        padding: EdgeInsets.zero,
                        constraints: BoxConstraints(),
                      )
                    ],
                  ),

                  Widgets.heightSpaceH2,

                  // Date Picker
                  InkWell(
                    onTap: () async {
                      final DateTime? picked = await showDatePicker(
                        context: context,
                        initialDate: selectedDate ?? DateTime.now().add(Duration(days: 1)),
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(Duration(days: 90)),
                        builder: (context, child) {
                          return Theme(
                            data: Theme.of(context).copyWith(
                              colorScheme: ColorScheme.light(
                                primary: ColorConstants.primaryColor,
                              ),
                            ),
                            child: child!,
                          );
                        },
                      );
                      if (picked != null) {
                        setState(() {
                          selectedDate = picked;
                        });
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 15),
                      decoration: BoxDecoration(color: Colors.white,
                        border: Border.all(color: Colors.white),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.calendar_today, size: 18, color: ColorConstants.primaryColor),
                          SizedBox(width: 10),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text("Date", style: TextStyle(fontSize: 12, color: Colors.grey)),
                                SizedBox(height: 4),
                                Text(
                                  selectedDate != null
                                      ? DateFormat('MM/dd/yyyy').format(selectedDate!)
                                      : "Select date",
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: selectedDate != null ? Colors.black : Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  Widgets.heightSpaceH2,

                  // Time Picker
                  InkWell(
                    onTap: () async {

                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 15),
                      decoration: BoxDecoration(color: Colors.white,
                        border: Border.all(color: Colors.white),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.access_time, size: 18, color: ColorConstants.primaryColor),
                          SizedBox(width: 10),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text("Time", style: TextStyle(fontSize: 12, color: Colors.grey)),
                                SizedBox(height: 4),
                                Text(
                                  selectedTime.isNotEmpty ? selectedTime : "Select time",
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: selectedTime.isNotEmpty ? Colors.black : Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  Widgets.heightSpaceH2,

                  // Reason Text Field
                  EntryField(
                    controller: reasonController,

                 label: "Appointment Reason",
                    hint: "Write here",
                    borderRadius: 10,
                  ),

                  Widgets.heightSpaceH3,

                  // Save Button
                  CustomButton(
                    onTap: () {
                      if (selectedDate == null) {
                        Widgets.showSnackBar("Error", "Please select a date");
                        return;
                      }


                      // Format date for API
                      final formattedDate = DateFormat('yyyy-MM-dd').format(selectedDate!);

                      // Prepare update data
                      final updateData = {
                        "appointment_id": appointment.id,
                        "appointment_date": formattedDate,
                        "appointment_time": appointment.time?.substring(0,appointment.time!.length-3),
                        "appointment_reason": reasonController.text
                      };

                      // Call update method
                      controller.updateAppointment(updateData);

                      // Close dialog
                      Navigator.pop(context);
                    },
                    label: "SAVE CHANGES",
                  )
                ],
              ),
            ),
          );
        }
      ),
    );
  }
}
