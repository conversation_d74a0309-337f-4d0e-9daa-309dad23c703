import 'package:ensuram/core/constants/api_endpoints.dart';
import 'package:ensuram/core/services/http_service.dart';
import 'package:ensuram/core/widgets/widgets.dart';
import 'package:ensuram/view/modules/beneficiary/model/beneificery_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import 'appointmnet_controller.dart';

class Hospital {
  final String? id;
  final String? name;


  Hospital({this.id, this.name});

  factory Hospital.fromJson(Map<String, dynamic> json) {
    return Hospital(
      id: json['hsa_id'].toString(),
      name: json['subaccount_name'],

    );
  }
}

class Doctor {
  final String? id;
  final String? name;
  final String? specialty;


  Doctor({this.id, this.name, this.specialty});

  factory Doctor.fromJson(Map<String, dynamic> json) {
    return Doctor(
      id: json['id'].toString(),
      name: json['name'],
      specialty: json['specialty'],

    );
  }
}

class BookAppointmentController extends GetxController {
  // Form fields
  final RxString selectedCountry = "".obs;
  final RxString selectedHospitalId = "".obs;
  final RxString selectedHospitalName = "".obs;
  final RxString selectedDoctorId = "".obs;
  final RxString selectedDoctorName = "".obs;
  final RxString selectedBeneficiaryId = "".obs;
  final RxString selectedBeneficiaryName = "".obs;
  final Rx<DateTime?> selectedDate = Rx<DateTime?>(null);
  final RxString selectedTime = "".obs;
  final RxString selectedAppointmentType = "".obs;
  final TextEditingController reasonController = TextEditingController();
  
  // Data lists
  RxList<Hospital> hospitals = <Hospital>[].obs;
  RxList<Doctor> doctors = <Doctor>[].obs;
  RxList<Beneficary> beneficiaries = <Beneficary>[].obs;
  RxList<String> timeSlots = <String>[].obs;

  // Loading states
  RxBool isLoadingHospitals = false.obs;
  RxBool isLoadingDoctors = false.obs;
  RxBool isLoadingBeneficiaries = false.obs;
  RxBool isLoadingTimeSlots = false.obs;

  @override
  void onInit() {
    super.onInit();
    fetchBeneficiaries();

    // Observe changes to date and doctor to fetch time slots
    ever(selectedDate, (_) {
      if (selectedDoctorId.value.isNotEmpty) {
        fetchDoctorTimeSlots();
      }
    });

    ever(selectedDoctorId, (_) {
      if (selectedDate.value != null) {
        fetchDoctorTimeSlots();
      }
    });
  }
  
  @override
  void onClose() {
    reasonController.dispose();
    super.onClose();
  }
  
  // Select country and fetch hospitals
  void selectCountry(String country) {
    selectedCountry.value = country;
    // Reset dependent fields
    selectedHospitalId.value = "";
    selectedHospitalName.value = "";
    selectedDoctorId.value = "";
    selectedDoctorName.value = "";
    
    // Fetch hospitals for this country
    fetchHospitals(country);
  }
  
  // Fetch hospitals by country
  Future<void> fetchHospitals(String country) async {
    try {
      isLoadingHospitals.value = true;
      hospitals.clear();
      
      var response = await ApiService.getData("${Endpoints.getHospitalsByCountry}?country=$country");
      
      isLoadingHospitals.value = false;
      
      if (response.status == true) {
        hospitals.addAll(
          (response.data as List)
              .map((e) => Hospital.fromJson(e))
              .toList(),
        );
      } else {
        // Widgets.showSnackBar("Error", response.message ?? "Failed to load hospitals");
      }
    } catch (e) {
      print("Error fetching hospitals: $e");
      isLoadingHospitals.value = false;
      Widgets.showSnackBar("Error", "Failed to load hospitals");
    }
  }
  
  // Select hospital and fetch doctors
  void selectHospital(Hospital hospital) {
    selectedHospitalId.value = hospital.id ?? "";
    selectedHospitalName.value = hospital.name ?? "";
    // Reset dependent fields
    selectedDoctorId.value = "";
    selectedDoctorName.value = "";
    
    // Fetch doctors for this hospital
    fetchDoctors(hospital.id ?? "");
  }
  
  // Fetch doctors by hospital
  Future<void> fetchDoctors(String hospitalId) async {
    try {
      isLoadingDoctors.value = true;
      doctors.clear();
      
      var response = await ApiService.getData("${Endpoints.getDoctorsByHospital}?hospital_id=$hospitalId");
      
      isLoadingDoctors.value = false;
      
      if (response.status == true) {
        doctors.addAll(
          (response.data['doctors'] as List)
              .map((e) => Doctor.fromJson(e))
              .toList(),
        );
      } else {
        // Widgets.showSnackBar("Error", response.message ?? "Failed to load doctors");
      }
    } catch (e) {
      print("Error fetching doctors: $e");
      isLoadingDoctors.value = false;
      Widgets.showSnackBar("Error", "Failed to load doctors");
    }
  }
  
  // Select doctor
  void selectDoctor(Doctor doctor) {
    selectedDoctorId.value = doctor.id ?? "";
    selectedDoctorName.value = doctor.name ?? "";
    selectedTime.value = ""; // Reset time selection when doctor changes
  }
  
  // Fetch beneficiaries
  Future<void> fetchBeneficiaries() async {
    try {
      isLoadingBeneficiaries.value = true;
      
      var response = await ApiService.getData("${Endpoints.getUserBeneficiariesEligible}");
      
      isLoadingBeneficiaries.value = false;
      
      if (response.status == true) {
        beneficiaries.clear();
        beneficiaries.addAll(
          (response.data['data'] as List)
              .map((e) => Beneficary.fromJson(e))
              .toList(),
        );
      } else {
        Widgets.showSnackBar("Error", response.message ?? "Failed to load beneficiaries");
      }
    } catch (e) {
      print("Error fetching beneficiaries: $e");
      isLoadingBeneficiaries.value = false;
      Widgets.showSnackBar("Error", "Failed to load beneficiaries");
    }
  }
  
  // Select beneficiary
  void selectBeneficiary(Beneficary beneficiary) {
    selectedBeneficiaryId.value = beneficiary.id ?? "";
    selectedBeneficiaryName.value = "${beneficiary.name?? ""}";
  }
  
  // Book appointment
  Future<void> bookAppointment() async {
    // Validate all required fields
    if (selectedCountry.value.isEmpty ||
        selectedHospitalId.value.isEmpty ||
        selectedDoctorId.value.isEmpty ||
        selectedBeneficiaryId.value.isEmpty ||
        selectedDate.value == null ||
        selectedTime.value.isEmpty ||
        selectedAppointmentType.value.isEmpty) {
      Widgets.showSnackBar("Error", "Please fill all required fields");
      return;
    }
    
    try {
      Widgets.showLoader("Booking appointment...");
      
      // Format date for API
      final formattedDate = DateFormat('yyyy-MM-dd').format(selectedDate.value!);
      
      // Prepare request data
      final requestData = {
        "country": selectedCountry.value,
        "hospital_id": selectedHospitalId.value,
        "doctor_id": selectedDoctorId.value,
        "beneficiary_id": selectedBeneficiaryId.value,
        "appointment_date": formattedDate,
        "appointment_time": selectedTime.value.substring(0,selectedTime.value.length-3),
        "appointment_type": selectedAppointmentType.value,
        "appointment_reason": reasonController.text
      };
      
      var response = await ApiService.postData(Endpoints.bookAppointment, requestData);
      
      Widgets.hideLoader();
      
      if (response.status == true) { Get.back(); //
        Widgets.showSnackBar("Success", response.message ?? "Appointment booked successfully");
        Get.find<AppointmentController>().fetchAppointments(page: 1);

      } else {
        Widgets.showSnackBar("Error", response.message ?? "Failed to book appointment");
      }
    } catch (e) {
      print("Error booking appointment: $e");
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", "Failed to book appointment");
    }
  }

  // Convert time from 12-hour format (3:30 PM) to 24-hour format (15:30)

  // Fetch doctor's available time slots
  Future<void> fetchDoctorTimeSlots() async {
    if (selectedDoctorId.value.isEmpty || selectedDate.value == null) {
      return;
    }

    try {
      isLoadingTimeSlots.value = true;
      timeSlots.clear();

      var response = await ApiService.getData(
        "${Endpoints.getDoctorOpeningHours}?doctor_id=${selectedDoctorId.value}"
      );

      isLoadingTimeSlots.value = false;

      if (response.status == true) {

        final slots = (response.data['opening_hours'] as List).cast<String>();
        timeSlots.addAll(slots);
      } else {
        Widgets.showSnackBar("Error", response.message ?? "Failed to load available time slots");
      }
    } catch (e) {
      print("Error fetching time slots: $e");
      isLoadingTimeSlots.value = false;
      Widgets.showSnackBar("Error", "Failed to load available time slots");
    }
  }
}
