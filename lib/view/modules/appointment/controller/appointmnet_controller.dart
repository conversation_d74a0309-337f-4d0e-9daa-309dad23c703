import 'dart:convert';
import 'dart:io';
import 'package:ensuram/core/constants/api_endpoints.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/services/http_service.dart';
import 'package:ensuram/core/widgets/text_widgets.dart';
import 'package:ensuram/core/widgets/widgets.dart';
import 'package:ensuram/view/modules/beneficiary/model/beneificery_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:http/http.dart' as http;

import '../../../../controller/user_controller.dart';
import '../../contributions/model/organization_model.dart';
import '../model/appointment_model.dart';

class AppointmentController extends GetxController {
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final idNumberController = TextEditingController();
  final phoneNumberController = TextEditingController();
  RxList appointments = <Appointment>[].obs;
  RxBool isLoading = false.obs;
  RxBool isMoreLoading = false.obs;
  RxInt totalCount = 0.obs;
  RxInt currentPage = 0.obs;
  final RxString selectedSubscriptionType = "".obs;
  final RxString selectedBeneficiaryId = "".obs;
  final RxString selectedBeneficiaryName = "".obs;

  // For beneficiaries list
  RxList<Beneficary> beneficiaries = <Beneficary>[].obs;
  RxBool isLoadingBeneficiaries = false.obs;

  final Rx<DateTime?> selectedDate = Rx<DateTime?>(null);
  final Rx<String?> selectedGender = Rx<String?>(null);
  final Rx<String?> selectedRelationship = Rx<String?>(null);
  final Rx<File?> selectedImage = Rx<File?>(null);
  @override
  void onInit() {
    super.onInit();
    fetchBeneficiaries();
  }

  // Fetch beneficiaries from API
  Future<void> fetchBeneficiaries() async {
    try {
      isLoadingBeneficiaries.value = true;
      var response = await ApiService.getData(Endpoints.getUserBeneficiaries);
      isLoadingBeneficiaries.value = false;

      if (response.status == true) {
        beneficiaries.clear();
        beneficiaries.addAll(
          (response.data['data'] as List)
              .map((e) => Beneficary.fromJson(e))
              .toList(),
        );
      }
    } catch (e) {
      print("Error fetching beneficiaries: $e");
      isLoadingBeneficiaries.value = false;
      Widgets.showSnackBar("Error", "Failed to load beneficiaries");
    }
  }

  // Select a beneficiary
  void selectBeneficiary(Beneficary beneficiary) {
    selectedBeneficiaryId.value = beneficiary.beneficiaryId ?? "";
    selectedBeneficiaryName.value =
        "${beneficiary.firstName ?? ""} ${beneficiary.lastName ?? ""}";
  }

  // Submit appointment
  Future<void> submitAppointmentSchedular() async {
    try {
      if (selectedBeneficiaryId.value.isEmpty) {
        Widgets.showSnackBar("Error", "Please select a beneficiary");
        return;
      }

      if (selectedSubscriptionType.value.isEmpty) {
        Widgets.showSnackBar("Error", "Please select a subscription type");
        return;
      }
Widgets.showLoader("Loading");
      var response =
          await ApiService.postData(Endpoints.subscribeToAppointment, {
        "beneficiary_id": selectedBeneficiaryId.value,
        "subscription_type":
            selectedSubscriptionType.value == "Monthly(\$2.99 /person)"
                ? "M"
                : "Y"
      });
      Widgets.hideLoader();

      if (response.status == true) {
        Get.back();
        Widgets.showSnackBar("Success", "Appointment scheduled successfully");
      } else {
        Widgets.showSnackBar(
            "Error", response.message ?? "Failed to schedule appointment");
      }
    } catch (e) {
      print(e);
      Widgets.hideLoader();
      Widgets.showSnackBar(
          "Error", "An error occurred while scheduling appointment try");
    }
  }


  void selectGender(String gender) {
    selectedGender.value = gender;
  }





  fetchAppointments({int page = 1}) async {
    try {
      if (isLoading.value) return;
      if (page == 1) {
        isLoading.value = true;
      } else {
        isMoreLoading.value = true;
      }

      var response = await ApiService.getData(
          "${Endpoints.getUserAppointments}?page=$page&limit=15");
      isLoading.value = false;
      isMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          appointments.clear();
          totalCount.value = 0;
          currentPage.value = 1;
        }
        appointments.addAll(
          (response.data['data'] as List)
              .map((e) => Appointment.fromJson(e))
              .toList(),
        );

        totalCount.value =
            int.parse(response.data['pagination']['total_appointments'].toString());
      }
    } catch (e) {
      print(e);
      isLoading.value = false;
      isMoreLoading.value = false;
    } finally {
      isLoading.value = false;
      isMoreLoading.value = false;
    }
  }

  fetchAppointmentsBackground({int page = 1}) async {
    try {
      var response = await ApiService.getData(
          "${Endpoints.getUserAppointments}?page=$page&limit=15");

      if (response.status == true) {
        if (page == 1) {
          appointments.clear();
          totalCount.value = 0;
          currentPage.value = 1;
        }
        appointments.addAll(
          (response.data['data'] as List)
              .map((e) => Appointment.fromJson(e))
              .toList(),
        );

        totalCount.value =
            int.parse(response.data['pagination']['total_appointments'].toString());
      }
    } catch (e) {
      print(e);
      isLoading.value = false;
      isMoreLoading.value = false;
    } finally {
      isLoading.value = false;
      isMoreLoading.value = false;
    }
  }

  @override
  void onClose() {
    firstNameController.dispose();
    lastNameController.dispose();
    idNumberController.dispose();
    phoneNumberController.dispose();
    super.onClose();
  }


  cancelAppointment(String id) async {
    try {
      Widgets.showLoader("Cancelling appointment");
      var response = await ApiService.postData(
          Endpoints.cancelAppointment, {"appointment_id": id});
      Widgets.hideLoader();

      if (response.status == true) {
        fetchAppointmentsBackground(page: 1);
      } else {
        Widgets.showSnackBar(
            "Error", response.message ?? "Failed to cancel appointment");
      }
    } catch (e) {
      Widgets.hideLoader();}
  }

  // Update appointment
  Future<void> updateAppointment(Map<String, dynamic> updateData) async {
    try {
      Widgets.showLoader("Updating appointment");

      var response = await ApiService.postData(Endpoints.editAppointment, updateData);

      Widgets.hideLoader();

      if (response.status == true) {
        Widgets.showSnackBar("Success", response.message ?? "Appointment updated successfully");
        // Refresh appointments list
        fetchAppointmentsBackground(page: 1);
      } else {
        Widgets.showSnackBar("Error", response.message ?? "Failed to update appointment");
      }
    } catch (e) {
      print("Error updating appointment: $e");
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", "Failed to update appointment");
    }
  }

  // Helper method to convert time from 12-hour to 24-hour format
  String _convertTo24HourFormat(String time12h) {
    try {
      // Parse the time string (e.g., "3:30 PM")
      final timeParts = time12h.split(' ');
      final hourMinute = timeParts[0].split(':');
      final period = timeParts[1];

      int hour = int.parse(hourMinute[0]);
      final minute = hourMinute[1];

      // Convert to 24-hour format
      if (period == 'PM' && hour < 12) {
        hour += 12;
      } else if (period == 'AM' && hour == 12) {
        hour = 0;
      }

      // Format as HH:MM
      return '${hour.toString().padLeft(2, '0')}:$minute';
    } catch (e) {
      print("Error converting time format: $e");
      return time12h; // Return original if conversion fails
    }
  }
}
