import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/widgets.dart';
import 'package:ensuram/view/modules/laboratory/model/lab_book_model.dart';
import 'package:ensuram/view/modules/pharmacy/view/add_to_cart_view.dart';
import 'package:ensuram/view/modules/pharmacy/view/medication_detail_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_rx/src/rx_typedefs/rx_typedefs.dart';

import '../../../../core/constants/color_constants.dart';
import '../../../../core/widgets/shimmer_effect.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../controller/lab_controller.dart';
import 'lab_detail_view.dart';
// TODO: add flutter_svg to pubspec.yaml

class LabsTestView extends StatefulWidget {
  const LabsTestView({super.key});

  @override
  State<LabsTestView> createState() => _LabsTestViewState();
}

class _LabsTestViewState extends State<LabsTestView> {
  LabController controller = Get.find();
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    scrollController.addListener(scrollListener);
    controller.fetchTests(page: 1);
  }

  scrollListener() {
    if (scrollController.position.pixels ==
            scrollController.position.maxScrollExtent &&
        controller.totalLabsCount.value > controller.labTests.length) {
      controller.fetchTests() > (page: controller.currentLabPage.value + 1);
      controller.currentLabPage.value++; // Increment the page counter
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        leadingWidth: 30,
        centerTitle: true,
        title: Texts.textBlock("Book Lab Tests",
            size: 20, fontWeight: FontWeight.w700),
        actions: [

        ],
      ),
      body: widgetOrdersCard(),
    );
  }

  Widget productCard({
    required LabTests product,
    required Callback onPress,
  }) {
    return GestureDetector(
      onTap: onPress,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: .20.sh,
            width: 1.sw,
            decoration: BoxDecoration(
              color: const Color(0xFF979797).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: Widgets.networkImage(product.labsLogo ?? "")),
          ),
          const SizedBox(height: 8),
          Texts.textBlock(product.testName ?? "",
              color: ColorConstants.primaryColor),
          const SizedBox(height: 3),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "\$${product.price}",
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w800,
                  color: Colors.black,
                ),
              ),
              GestureDetector(
                onTap: () {
                  controller.selectedLabTest.value = product;
                  controller.fetchTestsDetail();
                  Get.to(() => LabDetailView());
                },
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: Container(
                    color: ColorConstants.primaryColor,
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    child: Texts.textMedium("Learn more",
                        size: 11, color: Colors.white),
                  ),
                ),
              )
            ],
          ),
          const SizedBox(height: 5),
        ],
      ),
    );
  }

  Widget cartIcon(Callback press) {
    return InkWell(
      borderRadius: BorderRadius.circular(100),
      onTap: press,
      child: Stack(
        children: [
          Icon(Icons.shopping_cart, color: ColorConstants.secondaryGreyColor),
          Positioned(
              right: 2,
              child: CircleAvatar(
                backgroundColor: Colors.red,
                radius: 4,
              ))
        ],
      ),
    );
  }

  Widget widgetOrdersCard() {
    return Obx(() {
      return SingleChildScrollView(
        controller: scrollController,
        padding: EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

            CustomBorderButton(onTap: (){
              _showFilterBottomSheet(context);
            },
              label: "Filter Lab Tests",
              icon: Icon(Icons.filter_alt_outlined),
              textColor: Colors.black,padding: 9,
            ),
            Widgets.heightSpaceH2,
            controller.isLabTestsLoading.value
                ? const ShimmerListSkeleton()
                : controller.labTests.isNotEmpty
                    ? ListView.separated(
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: productCard(
                                product: controller.labTests[index],
                                onPress: () {}),
                          );
                        },
                        separatorBuilder: (context, index) {
                          return Widgets.divider();
                        },
                        itemCount: controller.labTests.length ?? 0)
                    : Widgets.noRecordsFound(title: "No Tests Found"),
            if (controller.isLabMoreLoading.value)
              Center(child: CircularProgressIndicator()),
            Widgets.heightSpaceH2,
            Widgets.heightSpaceH2,
            Widgets.heightSpaceH2,
          ],
        ),
      );
    });
  }

  void _showFilterBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
            left: 16,
            right: 16,
            top: 24,
          ),
          child: StatefulBuilder(
            builder: (context, setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text("Filter Lab Tests",
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  SizedBox(height: 20),
                  _buildFilterField("Search Lab Test", "Enter test name",
                      controller.testController),
                  SizedBox(height: 16),
                  _buildFilterField("Search by Location", "Enter location",
                      controller.locationController),
                  SizedBox(height: 16),
                  _buildFilterField(
                      "Min Price", "0", controller.minPriceController,
                      isNumeric: true),
                  SizedBox(height: 16),
                  _buildFilterField(
                      "Max Price", "1000", controller.maxPriceController,
                      isNumeric: true),
                  SizedBox(height: 24),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            controller.testController.clear();
                            controller.locationController.clear();
                            controller.minPriceController.text = "0";
                            controller.maxPriceController.text = "1000";
                            Navigator.pop(context); // Close sheet
                            controller.fetchTests(page: 1); // Reset
                          },
                          child: Text("Reset"),
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(color: Colors.cyan),
                            foregroundColor: Colors.cyan,
                            shape: StadiumBorder(),
                          ),
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context); // Close sheet
                            controller.fetchTests(
                              page: 1,
                            );
                          },
                          child: Text("Search"),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.cyan,
                            shape: StadiumBorder(),
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                ],
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildFilterField(
      String label, String hint, TextEditingController controller,
      {bool isNumeric = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: TextStyle(fontWeight: FontWeight.w600)),
        SizedBox(height: 6),
        TextField(
          controller: controller,
          keyboardType: isNumeric ? TextInputType.number : TextInputType.text,
          decoration: InputDecoration(
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            hintText: hint,
            filled: true,
            fillColor: Colors.grey.shade200,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(30),
              borderSide: BorderSide.none,
            ),
          ),
        ),
      ],
    );
  }
}
