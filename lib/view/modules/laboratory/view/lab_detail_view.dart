import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/widgets.dart';
import 'package:ensuram/view/modules/laboratory/controller/lab_controller.dart';
import 'package:ensuram/view/modules/pharmacy/view/pharmacy_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:intl/intl.dart';

import '../../../../core/constants/color_constants.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/text_widgets.dart';

class LabDetailView extends StatefulWidget {
  const LabDetailView({super.key});

  @override
  State<LabDetailView> createState() => _LabDetailViewState();
}

class _LabDetailViewState extends State<LabDetailView>
{

  void showBookingDialog(BuildContext context) {
    DateTime? selectedDate;
    TimeOfDay? selectedTime;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return Dialog(
            backgroundColor: ColorConstants.halfWhite,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: Container(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title & Close
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Texts.textBlock("Confirm Lab Test Booking", size: 18, fontWeight: FontWeight.bold),
                      IconButton(
                        icon: Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                        padding: EdgeInsets.zero,
                        constraints: BoxConstraints(),
                      )
                    ],
                  ),

                  Widgets.heightSpaceH2,

                  // Date Picker
                  InkWell(
                    onTap: () async {
                      final DateTime? picked = await showDatePicker(
                        context: context,
                        initialDate: DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(Duration(days: 90)),
                        builder: (context, child) {
                          return Theme(
                            data: Theme.of(context).copyWith(
                              colorScheme: ColorScheme.light(
                                primary: ColorConstants.primaryColor,
                              ),
                            ),
                            child: child!,
                          );
                        },
                      );
                      if (picked != null) {
                        setState(() {
                          selectedDate = picked;
                        });
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 15),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(color: Colors.white),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.calendar_today, size: 18, color: ColorConstants.primaryColor),
                          SizedBox(width: 10),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text("Select Appointment Date", style: TextStyle(fontSize: 12, color: Colors.grey)),
                                SizedBox(height: 4),
                                Text(
                                  selectedDate != null
                                      ? DateFormat('dd/MM//yyyy').format(selectedDate!)
                                      : "Select date",
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: selectedDate != null ? Colors.black : Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  Widgets.heightSpaceH2,

                  // Time Picker
                  InkWell(
                    onTap: () async {
                      final TimeOfDay? picked = await showTimePicker(
                        context: context,
                        initialTime: TimeOfDay.now(),
                      );
                      if (picked != null) {
                        setState(() {
                          selectedTime = picked;
                        });
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 15),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(color: Colors.white),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.access_time, size: 18, color: ColorConstants.primaryColor),
                          SizedBox(width: 10),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text("Select Appointment Time", style: TextStyle(fontSize: 12, color: Colors.grey)),
                                SizedBox(height: 4),
                                Text(
                                  selectedTime != null
                                      ? selectedTime!.format(context)
                                      : "Select time",
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: selectedTime != null ? Colors.black : Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  Widgets.heightSpaceH3,

                  // Save Button
                  CustomButton(
                    onTap: () {
                      if (selectedDate == null) {
                        Widgets.showSnackBar("Error", "Please select a date");
                        return;
                      }

                      if (selectedTime == null) {
                        Widgets.showSnackBar("Error", "Please select a time");
                        return;
                      }

                      // Working hours: 09:00 to 17:00
                      final startHour = 9;
                      final endHour = 17;

                      if (selectedTime!.hour < startHour || selectedTime!.hour >= endHour) {
                        Widgets.showSnackBar("Error", "Please select a time between 09:00 and 17:00");
                        return;
                      }

                      final formattedDate = DateFormat('yyyy.MM.dd').format(selectedDate!);
                      final formattedTime = "${selectedTime!.hour.toString().padLeft(2, '0')}:${selectedTime!.minute.toString().padLeft(2, '0')}";

                      final data = {

                        "appointment_date": formattedDate,
                        "appointment_time": formattedTime,
                        "lab_id": labController.selectedLabTest.value.labId??"",
                        "test_id": labController.selectedLabTest.value.id??""
                      };

                      labController.bookLabTest(data);
                    },
                    label: "SAVE CHANGES",
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }


  late LabController labController;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    labController=Get.find();
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Lab Test Detail",
            size: 17, fontWeight: FontWeight.w700),
        actions: [],
      ),
      body: Obx(
          ()=> Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [

              Container(
                height: .30.sh,
                width: 1.sw,
                decoration: BoxDecoration(
                  color: const Color(0xFF979797).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(15),
                    child: Widgets.networkImage(
                      labController.selectedLabTest.value.labsLogo??"",

                    )),
              ),
              Widgets.heightSpaceH2,
              Texts.textBlock( labController.selectedLabTest.value.testName??"",
                  color: ColorConstants.primaryColor),

              const SizedBox(height: 3),


              Texts.textBlock(labController.selectedLabTest.value.labsName??"",
                  size: 15, fontWeight: FontWeight.w600),
              Widgets.heightSpaceH1,
              buildTextGroupColumn(key: "Sample Type", value: labController.selectedLabDetail.value.labSampleType??""),

              buildTextGroupColumn(key: "Price", value: "\$${ labController.selectedLabTest.value.price??""}"),

              buildTextGroupColumn(key: "Processing Time", value: "${labController.selectedLabDetail.value.processingTime??"1"} day(s)"),
              buildTextGroupColumn(key: "Availability ", value: labController.selectedLabDetail.value.availability??""),

            ],),
        ),
      ),
      bottomNavigationBar:
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: CustomButton(color: ColorConstants.secondaryColor,
          onTap: () {   showBookingDialog(context); },
          label: "Book Now",
        ),
      ),
    );
  }
  buildTextGroupColumn({String? key, String? value}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 3),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Texts.textBlock(key ?? "",
              fontWeight: FontWeight.w300,
              color: Colors.black,
              maxline: 1,
              size: 14),
          Texts.textBlock(value ?? "",
              fontWeight: FontWeight.w600,
              maxline: 1,
              color: Colors.black,
              size: 16),
        ],
      ),
    );
  }

}






