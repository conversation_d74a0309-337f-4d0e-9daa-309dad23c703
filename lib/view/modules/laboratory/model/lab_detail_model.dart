class LabDetail {
  String? testName;
  String? description;
  String? labSampleType;
  double? price;
  String? processingTime;
  String? availability;
  int? labsId;
  String? labsLogo;
  String? labsAddress;
  String? labsName;

  LabDetail(
      {this.testName,
        this.description,
        this.labSampleType,
        this.price,
        this.processingTime,
        this.availability,
        this.labsId,
        this.labsLogo,
        this.labsAddress,
        this.labsName});

  LabDetail.fromJson(Map<String, dynamic> json) {
    testName = json['test_name'];
    description = json['description'];
    labSampleType = json['lab_sample_type'];
    price = json['price'];
    processingTime = json['processing_time'];
    availability = json['availability'];
    labsId = json['labs_id'];
    labsLogo = json['labs_logo'];
    labsAddress = json['labs_address'];
    labsName = json['labs_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['test_name'] = this.testName;
    data['description'] = this.description;
    data['lab_sample_type'] = this.labSampleType;
    data['price'] = this.price;
    data['processing_time'] = this.processingTime;
    data['availability'] = this.availability;
    data['labs_id'] = this.labsId;
    data['labs_logo'] = this.labsLogo;
    data['labs_address'] = this.labsAddress;
    data['labs_name'] = this.labsName;
    return data;
  }
}
