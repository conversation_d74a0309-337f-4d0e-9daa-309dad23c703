class LabOrder {
  String? testId;
  String? testName;
  String? labName;
  String? appointmentDate;
  String? appointmentTime;
  String? status;

  LabOrder(
      {this.testId,
        this.testName,
        this.labName,
        this.appointmentDate,
        this.appointmentTime,
        this.status});

  LabOrder.fromJson(Map<String, dynamic> json) {
    testId = json['test_id'].toString();
    testName = json['test_name'];
    labName = json['lab_name'];
    appointmentDate = json['appointment_date'];
    appointmentTime = json['appointment_time'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['test_id'] = this.testId;
    data['test_name'] = this.testName;
    data['lab_name'] = this.labName;
    data['appointment_date'] = this.appointmentDate;
    data['appointment_time'] = this.appointmentTime;
    data['status'] = this.status;
    return data;
  }
}
class LabTests {
  String? id;
  String? testName;
  String? price;
  String? labId;
  String? labsLogo;
  String? labsName;
  String? labsState;
  String? labsCity;
  String? labsAddress;

  LabTests(
      {this.id,
        this.testName,
        this.price,
        this.labId,
        this.labsLogo,
        this.labsName,
        this.labsState,
        this.labsCity,
        this.labsAddress});

  LabTests.fromJson(Map<String, dynamic> json) {
    id = json['id'].toString();
    testName = json['test_name'];
    price = json['price'].toString();
    labId = json['lab_id'].toString();
    labsLogo = json['labs_logo'];
    labsName = json['labs_name'];
    labsState = json['labs_state'];
    labsCity = json['labs_city'];
    labsAddress = json['labs_address'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['test_name'] = this.testName;
    data['price'] = this.price;
    data['lab_id'] = this.labId;
    data['labs_logo'] = this.labsLogo;
    data['labs_name'] = this.labsName;
    data['labs_state'] = this.labsState;
    data['labs_city'] = this.labsCity;
    data['labs_address'] = this.labsAddress;
    return data;
  }
}
