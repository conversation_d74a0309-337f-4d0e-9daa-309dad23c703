import 'package:ensuram/view/modules/laboratory/model/lab_detail_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:ensuram/core/services/http_service.dart';

import '../../../../core/constants/api_endpoints.dart';
import '../../../../core/widgets/widgets.dart';
import '../model/lab_book_model.dart';

class LabController extends GetxController {
  RxList orders = <LabOrder>[].obs;
  RxBool isOrdersLoading = false.obs;
  RxBool isOrdersMoreLoading = false.obs;
  RxInt totalOrdersCount = 0.obs;
  RxInt currentOrdersPage = 0.obs;
  var selectedLabTest=LabTests().obs; var selectedLabDetail=LabDetail().obs;
  RxList labTests = <LabTests>[].obs;
  RxBool isLabTestsLoading = false.obs;
  RxBool isLabMoreLoading = false.obs;
  RxInt totalLabsCount = 0.obs;
  RxInt currentLabPage = 0.obs;
  final TextEditingController testController = TextEditingController();
  final TextEditingController locationController = TextEditingController();
  final TextEditingController minPriceController = TextEditingController(text: "0");
  final TextEditingController maxPriceController = TextEditingController(text: "1000");

  fetchOrders({int page = 1}) async {
    try {
      if (isOrdersLoading.value) return;
      if (page == 1) {
        isOrdersLoading.value = true;
      } else {
        isOrdersMoreLoading.value = true;
      }

      var response = await ApiService.getData(
          "${Endpoints.getLabTestOrders}?page=$page&limit=15");
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          orders.clear();
          totalOrdersCount.value = 0;
          currentOrdersPage.value = 1;
        }
        orders.addAll(
          (response.data['data'] as List)
              .map((e) => LabOrder.fromJson(e))
              .toList(),
        );

        totalOrdersCount.value =
            int.parse(response.data['pagination']['total_records'].toString());
      }
    } catch (e) {
      print(e);
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    } finally {
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    }
  }
  fetchTests({int page = 1}) async {
    try {
      if (isLabTestsLoading.value) return;
      if (page == 1) {
        isLabTestsLoading.value = true;
      } else {
        isLabMoreLoading.value = true;
      }


      var response = await ApiService.getData(
          "${Endpoints.getLabTests}?page=$page&limit=15&search=${testController.text}&location=${locationController.text}&min_price=${minPriceController.text}&max_price=${maxPriceController.text}");

      isLabTestsLoading.value = false;
      isLabMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          labTests.clear();
          totalLabsCount.value = 0;
          currentLabPage.value = 1;
        }if(response.data['data']!=null){
        labTests.addAll(
          (response.data['data']['lab_tests'] as List)
              .map((e) => LabTests.fromJson(e))
              .toList(),
        );

        totalLabsCount.value =
            int.parse(response.data['data']['total_pages'].toString());}
      }
    } catch (e) {
      print(e);
      isLabTestsLoading.value = false;
      isLabMoreLoading.value = false;
    } finally {
      isLabTestsLoading.value = false;
      isLabMoreLoading.value = false;
    }
  }
  fetchTestsDetail() async {
    try {


      var response = await ApiService.getData(
          "${Endpoints.getLabTestsDetails}?id=${selectedLabTest.value.id}");

      if (response.status == true) {


         selectedLabDetail.value= LabDetail.fromJson(response.data['data']);


    }} catch (e) {
      print(e);
      isLabTestsLoading.value = false;
      isLabMoreLoading.value = false;
    } finally {
      isLabTestsLoading.value = false;
      isLabMoreLoading.value = false;
    }
  }

   bookLabTest(var payload) async {
    try {
Get.back();
    Widgets.showLoader("Booking...");

    var response = await ApiService.postData(Endpoints.bookLabTest, payload);

    Widgets.hideLoader();

    if (response.status == true) {
Get.back();Get.back();
    Widgets.showSnackBar(
      "Success",
      response.message ?? "Failed to add funds to wallet",
    );
fetchTests(page: 1);
    } else {
      Widgets.showSnackBar(
        "Error",
        response.message ?? "Failed to add funds to wallet",
      );
    }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar(
        "Error",
        "An error occurred while adding funds",
      );
      print("Error adding funds to wallet: $e");
    }
  }

}
