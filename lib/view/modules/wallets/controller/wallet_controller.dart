import 'dart:convert';
import 'dart:developer';

import 'package:ensuram/view/modules/wallets/model/saving_transactions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_paypal/flutter_paypal.dart';
import 'package:flutterwave_standard/core/flutterwave.dart';
import 'package:flutterwave_standard/models/requests/customer.dart';
import 'package:flutterwave_standard/models/requests/customizations.dart';
import 'package:flutterwave_standard/models/responses/charge_response.dart';
import 'package:get/get.dart';
import 'package:ensuram/core/services/http_service.dart';
import 'package:ensuram/core/widgets/widgets.dart';

import '../../../../controller/user_controller.dart';
import '../../../../core/constants/api_endpoints.dart';

class WalletController extends GetxController {
  RxString balance = "1,100.00".obs;
  RxString selectedPaymentMethod = "".obs;

  final TextEditingController amountController = TextEditingController();
  final TextEditingController amountSendController = TextEditingController();
  final TextEditingController recipientController = TextEditingController();
  final TextEditingController reasonController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  RxList transactions = <SavingTransactions>[].obs;
  RxBool isTransactionsLoading = false.obs;
  RxBool isTransactionsMoreLoading = false.obs;
  RxInt totalTransactionsCount = 0.obs;
  RxInt currentPage = 0.obs;

  RxList sentTransactions = <SentTransaction>[].obs;
  RxBool isSentTransactionsLoading = false.obs;
  RxBool isSentTransactionsMoreLoading = false.obs;
  RxInt totalSentTransactionsCount = 0.obs;
  RxInt currentSentTransactionsPage = 0.obs;

  RxList receivedTransactions = <SavingTransactions>[].obs;
  RxBool isReceivedTransactionsLoading = false.obs;
  RxBool isReceivedTransactionsMoreLoading = false.obs;
  RxInt totalReceivedTransactionsCount = 0.obs;
  RxInt currentReceivedTransactionsPage = 0.obs;

  RxString transactionViewType = 'sent'.obs;
  final TextEditingController searchTransactionController =
      TextEditingController();

  // Add these new properties for transaction filtering
  RxString transactionFilter = 'all'.obs;
  RxList filteredSentTransactions = <SentTransaction>[].obs;
  // timeFilter
  // 'all', 'today', 'past'
  @override
  void onInit() {
    super.onInit();
    amountController.text = "100";


  }

  @override
  void onClose() {
    super.onClose();
  }

  fetchTransactions({int page = 1}) async {
    try {
      if (isTransactionsLoading.value) return;
      if (page == 1) {
        isTransactionsLoading.value = true;
      } else {
        isTransactionsMoreLoading.value = true;
      }

      var response = await ApiService.getData(
          "${Endpoints.getTransactions}?page=$page&limit=15");
      isTransactionsLoading.value = false;
      isTransactionsMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          transactions.clear();
          totalTransactionsCount.value = 0;
          currentPage.value = 1;
        }
        transactions.addAll(
          (response.data['transactions'] as List)
              .map((e) => SavingTransactions.fromJson(e))
              .toList(),
        );

        totalTransactionsCount.value =
            response.data['pagination']['total_records'] ?? 0;
      }
    } catch (e) {
      print(e);
      isTransactionsLoading.value = false;
      isTransactionsMoreLoading.value = false;
    } finally {
      isTransactionsLoading.value = false;
      isTransactionsMoreLoading.value = false;
    }
  }

  fetchSentTransactions({int page = 1}) async {
    try {
      if (isSentTransactionsLoading.value) return;
      if (page == 1) {
        isSentTransactionsLoading.value = true;
      } else {
        isSentTransactionsMoreLoading.value = true;
      }

      var response = await ApiService.getData(
          "${Endpoints.sentTransactions}?page=$page&limit=15&type=${transactionViewType.value}&timeFilter=${transactionFilter.value}");
      isSentTransactionsLoading.value = false;
      isSentTransactionsMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          sentTransactions.clear();
          totalSentTransactionsCount.value = 0;
          currentSentTransactionsPage.value = 1;
        }
        sentTransactions.addAll(
          (response.data['transactions'] as List)
              .map((e) => SentTransaction.fromJson(e))
              .toList(),
        );

        totalSentTransactionsCount.value =
            int.parse(response.data['pagination']['total_records'].toString());


      }
    } catch (e) {
      print(e);
      isSentTransactionsLoading.value = false;
      isSentTransactionsMoreLoading.value = false;
    } finally {
      isSentTransactionsLoading.value = false;
      isSentTransactionsMoreLoading.value = false;
    }
  }

  Future<void> fetchWalletBalance() async {
    try {
      // final response = await ApiService.getData(Endpoints.walletBalance);
      //
      // if (response.status) {
      //   // Format balance with commas
      //   final double rawBalance = double.parse(response.data['balance'].toString());
      //   balance.value = rawBalance.toStringAsFixed(2).replaceAllMapped(
      //     RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      //     (Match m) => '${m[1]},'
      //   );
      // } else {
      //   print("Failed to fetch wallet balance: ${response.message}");
      // }
    } catch (e) {
      print("Error fetching wallet balance: $e");
    }
  }

  void paymentMethodPaypal(BuildContext context) {
    processPayment(
      context: context,
      amount: Get.find<WalletController>().amountController.text,
      onSuccess: (data) {
        addFundsToWallet(data);
      },
      onCancel: () {
        print("Payment cancelled");
      },
      onError: (error) {
        print("Payment error: $error");
      },
    );
  }

  // Add funds to wallet
  Future<void> addFundsToWallet(var data) async {
    try {
      PaypalModel paypalModel = PaypalModel.fromJson(data);
      var payload = {
        "paymentData": paypalModel.data?.toJson(),
        "client_id": Get.find<UserController>().userModel?.userId,
        "transaction": paypalModel.data?.transactions?.toList(),
        "payer": paypalModel.data?.payer?.toJson(),
        "payerInfo": paypalModel.data?.payer?.payerInfo?.toJson(),
        "relatedSale": paypalModel
            .data?.transactions?.first.relatedResources?.first.sale
            ?.toJson(),
        "payer_name":
            "${paypalModel.data?.payer?.payerInfo?.firstName} ${paypalModel.data?.payer?.payerInfo?.lastName}",
        "payer_email": paypalModel.data?.payer?.payerInfo?.email,
        "payment_method": paypalModel.data?.payer?.paymentMethod,
        "payment_status": paypalModel.data?.payer?.status,
        "paid_amount": paypalModel.data?.transactions?.first.amount?.total,
        "currency": paypalModel.data?.transactions?.first.amount?.currency,
        "txn_id ": paypalModel
            .data?.transactions?.first.relatedResources?.first.sale?.id,
        "reason": paypalModel.data?.transactions?.first.description,
      };
      Widgets.showLoader("Processing...");

      var response =
          await ApiService.postData(Endpoints.addPaypalPayment, payload);

      Widgets.hideLoader();

      if (response.status == true) {
        Widgets.showSnackBar(
          "Success",
          response.message ?? "Failed to add funds to wallet",
        );
        fetchTransactions();
        Get.find<UserController>().fetchUserDetailsBackground();
      } else {
        Widgets.showSnackBar(
          "Error",
          response.message ?? "Failed to add funds to wallet",
        );
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar(
        "Error",
        "An error occurred while adding funds",
      );
      print("Error adding funds to wallet: $e");
    }
  }

  Future<void> sentWallet() async {
    try {Get.back();
      var payload = {
        "amount": amountSendController.text,
        "recipientId": recipientController.text,
        "reason": reasonController.text,
        "password": passwordController.text,
      };
      Widgets.showLoader("Processing...");

      var response = await ApiService.postData(Endpoints.sendPayment, payload);

      Widgets.hideLoader();

      if (response.status == true) {amountSendController.clear();
        recipientController.clear();
        reasonController.clear();
        passwordController.clear();
        Widgets.showSnackBar(
          "Success",
          response.message ?? "Failed to add funds to wallet",
        );
        fetchSentTransactions();
        Get.find<UserController>().fetchUserDetailsBackground();
      } else {
        Widgets.showSnackBar(
          "Error",
          response.message ?? "Failed to add funds to wallet",
        );
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar(
        "Error",
        "An error occurred while adding funds",
      );
      print("Error adding funds to wallet: $e");
    }
  }

  Future<void> saveStatusInVerificationTable(String amount) async {
    try {
      Widgets.showLoader("Processing...");

      final response = await ApiService.postData(
        Endpoints.userVerification,
        {
          'amount': amount,
          'payment_method': selectedPaymentMethod.value,
        },
      );

      // Widgets.hideLoader();
      //
      // if (response.status) {
      //   // Update balance
      //   await fetchWalletBalance();
      //
      //   // Show success message
      //   Widgets.showSnackBar(
      //     "Success",
      //     "Successfully added \$$amount to your wallet",
      //   );
      // } else {
      //   Widgets.showSnackBar(
      //     "Error",
      //     response.message ?? "Failed to add funds to wallet",
      //   );
      // }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar(
        "Error",
        "An error occurred while adding funds",
      );
      print("Error adding funds to wallet: $e");
    }
  }

  handlePaymentInitialization(BuildContext context) async {
    final Customer customer = Customer(
        name: "Flutterwave Developer",
        phoneNumber: "*************",
        email: "<EMAIL>");
    final Flutterwave flutterwave = Flutterwave(
        publicKey: "FLWPUBK_TEST-89f6de3ceb84f2df6697e01998037047-X",
        currency: "currency-here",
        redirectUrl: "add-your-redirect-url-here",
        txRef: "add-your-unique-reference-here",
        amount: "3000",
        customer: customer,
        paymentOptions: "ussd, card, bank transfer",
        customization: Customization(title: "My Payment"),
        isTestMode: true);

    final ChargeResponse response = await flutterwave.charge(context);

    // Handle the response
    if (response.success == true) {
      // Payment was successful
    } else {
      // Payment failed or was cancelled
    }
  }

  Future<void> processPayment({
    required BuildContext context,
    required String amount,
    required Function(String) onSuccess,
    required Function() onCancel,
    required Function(String) onError,
  }) async {
    try {
      // Validate amount
      final double parsedAmount =
          double.tryParse(amount.replaceAll(',', '')) ?? 0.0;
      if (parsedAmount <= 0) {
        onError('Please enter a valid amount');
        return;
      }

      // Format amount for PayPal (2 decimal places)
      final String formattedAmount = parsedAmount.toStringAsFixed(2);

      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (BuildContext context) => UsePaypal(
            sandboxMode: true,
            clientId:
                "ATnXCyWEydSAn-LAltfSSFta9Ql_ioMfCD9kwgBzjSoC2qrhEupLfPHfZsj9uRfRzI9kv-XYcnlHN9ra",
            secretKey:
                "EJtfQi7hQdDifhSHkLT57MmA91DmsCcZvpOvDpKQtHhEXIZp9275LlCNtanV3jYnmBegE3bHQKiqCgCD",
            returnURL: "https://samplesite.com/return",
            cancelURL: "https://samplesite.com/cancel",
            transactions: [
              {
                "amount": {
                  "total": formattedAmount,
                  "currency": "USD",
                  "details": {
                    "subtotal": formattedAmount,
                    "shipping": '0.00',
                    "shipping_discount": 0
                  }
                },
                "description": "Add funds to Ensuram E-Wallet",
                "item_list": {
                  "items": [
                    {
                      "name": "Wallet Top-up",
                      "quantity": 1,
                      "price": formattedAmount,
                      "currency": "USD"
                    }
                  ],
                }
              }
            ],
            note: "Add funds to your Ensuram E-Wallet",
            onSuccess: (Map params) async {
              log("PayPal payment successful: $params");

              addFundsToWallet(params);
            },
            onError: (error) {
              print("PayPal error: $error");
              onError(error.toString());
            },
            onCancel: (params) {
              print("PayPal payment cancelled: $params");
              onCancel();
            },
          ),
        ),
      );
    } catch (e) {
      print("Error processing PayPal payment: $e");
      onError('An error occurred while processing your payment');
    }
  }

}

class PaypalModel {
  String? payerID;
  String? paymentId;
  String? token;
  String? status;
  Data? data;

  PaypalModel(
      {this.payerID, this.paymentId, this.token, this.status, this.data});

  PaypalModel.fromJson(Map<dynamic, dynamic> json) {
    payerID = json['payerID'];
    paymentId = json['paymentId'];
    token = json['token'];
    status = json['status'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<dynamic, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['payerID'] = this.payerID;
    data['paymentId'] = this.paymentId;
    data['token'] = this.token;
    data['status'] = this.status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  String? id;
  String? intent;
  String? state;
  String? cart;
  Payer? payer;
  List<Transactions>? transactions;
  String? createTime;
  String? updateTime;
  List<Links>? links;

  Data(
      {this.id,
      this.intent,
      this.state,
      this.cart,
      this.payer,
      this.transactions,
      this.createTime,
      this.updateTime,
      this.links});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    intent = json['intent'];
    state = json['state'];
    cart = json['cart'];
    payer = json['payer'] != null ? new Payer.fromJson(json['payer']) : null;
    if (json['transactions'] != null) {
      transactions = <Transactions>[];
      json['transactions'].forEach((v) {
        transactions!.add(new Transactions.fromJson(v));
      });
    }

    createTime = json['create_time'];
    updateTime = json['update_time'];
    if (json['links'] != null) {
      links = <Links>[];
      json['links'].forEach((v) {
        links!.add(new Links.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['intent'] = this.intent;
    data['state'] = this.state;
    data['cart'] = this.cart;
    if (this.payer != null) {
      data['payer'] = this.payer!.toJson();
    }
    if (this.transactions != null) {
      data['transactions'] = this.transactions!.map((v) => v.toJson()).toList();
    }

    data['create_time'] = this.createTime;
    data['update_time'] = this.updateTime;
    if (this.links != null) {
      data['links'] = this.links!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Payer {
  String? paymentMethod;
  String? status;
  PayerInfo? payerInfo;

  Payer({this.paymentMethod, this.status, this.payerInfo});

  Payer.fromJson(Map<String, dynamic> json) {
    paymentMethod = json['payment_method'];
    status = json['status'];
    payerInfo = json['payer_info'] != null
        ? new PayerInfo.fromJson(json['payer_info'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['payment_method'] = this.paymentMethod;
    data['status'] = this.status;
    if (this.payerInfo != null) {
      data['payer_info'] = this.payerInfo!.toJson();
    }
    return data;
  }
}

class PayerInfo {
  String? email;
  String? firstName;
  String? lastName;
  String? payerId;
  ShippingAddress? shippingAddress;
  String? countryCode;

  PayerInfo(
      {this.email,
      this.firstName,
      this.lastName,
      this.payerId,
      this.shippingAddress,
      this.countryCode});

  PayerInfo.fromJson(Map<String, dynamic> json) {
    email = json['email'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    payerId = json['payer_id'];
    shippingAddress = json['shipping_address'] != null
        ? new ShippingAddress.fromJson(json['shipping_address'])
        : null;
    countryCode = json['country_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['email'] = this.email;
    data['first_name'] = this.firstName;
    data['last_name'] = this.lastName;
    data['payer_id'] = this.payerId;
    if (this.shippingAddress != null) {
      data['shipping_address'] = this.shippingAddress!.toJson();
    }
    data['country_code'] = this.countryCode;
    return data;
  }
}

class ShippingAddress {
  String? recipientName;
  String? line1;
  String? city;
  String? state;
  String? postalCode;
  String? countryCode;

  ShippingAddress(
      {this.recipientName,
      this.line1,
      this.city,
      this.state,
      this.postalCode,
      this.countryCode});

  ShippingAddress.fromJson(Map<String, dynamic> json) {
    recipientName = json['recipient_name'];
    line1 = json['line1'];
    city = json['city'];
    state = json['state'];
    postalCode = json['postal_code'];
    countryCode = json['country_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['recipient_name'] = this.recipientName;
    data['line1'] = this.line1;
    data['city'] = this.city;
    data['state'] = this.state;
    data['postal_code'] = this.postalCode;
    data['country_code'] = this.countryCode;
    return data;
  }
}

class Transactions {
  Amount? amount;
  Payee? payee;
  String? description;
  ItemList? itemList;
  List<RelatedResources>? relatedResources;

  Transactions(
      {this.amount,
      this.payee,
      this.description,
      this.itemList,
      this.relatedResources});

  Transactions.fromJson(Map<String, dynamic> json) {
    amount =
        json['amount'] != null ? new Amount.fromJson(json['amount']) : null;
    payee = json['payee'] != null ? new Payee.fromJson(json['payee']) : null;
    description = json['description'];
    itemList = json['item_list'] != null
        ? new ItemList.fromJson(json['item_list'])
        : null;
    if (json['related_resources'] != null) {
      relatedResources = <RelatedResources>[];
      json['related_resources'].forEach((v) {
        relatedResources!.add(new RelatedResources.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.amount != null) {
      data['amount'] = this.amount!.toJson();
    }
    if (this.payee != null) {
      data['payee'] = this.payee!.toJson();
    }
    data['description'] = this.description;
    if (this.itemList != null) {
      data['item_list'] = this.itemList!.toJson();
    }
    if (this.relatedResources != null) {
      data['related_resources'] =
          this.relatedResources!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Amount {
  String? total;
  String? currency;
  Details? details;

  Amount({this.total, this.currency, this.details});

  Amount.fromJson(Map<String, dynamic> json) {
    total = json['total'];
    currency = json['currency'];
    details =
        json['details'] != null ? new Details.fromJson(json['details']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['total'] = this.total;
    data['currency'] = this.currency;
    if (this.details != null) {
      data['details'] = this.details!.toJson();
    }
    return data;
  }
}

class Details {
  String? subtotal;
  String? shipping;
  String? insurance;
  String? handlingFee;
  String? shippingDiscount;
  String? discount;

  Details(
      {this.subtotal,
      this.shipping,
      this.insurance,
      this.handlingFee,
      this.shippingDiscount,
      this.discount});

  Details.fromJson(Map<String, dynamic> json) {
    subtotal = json['subtotal'];
    shipping = json['shipping'];
    insurance = json['insurance'];
    handlingFee = json['handling_fee'];
    shippingDiscount = json['shipping_discount'];
    discount = json['discount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['subtotal'] = this.subtotal;
    data['shipping'] = this.shipping;
    data['insurance'] = this.insurance;
    data['handling_fee'] = this.handlingFee;
    data['shipping_discount'] = this.shippingDiscount;
    data['discount'] = this.discount;
    return data;
  }
}

class Payee {
  String? merchantId;
  String? email;

  Payee({this.merchantId, this.email});

  Payee.fromJson(Map<String, dynamic> json) {
    merchantId = json['merchant_id'];
    email = json['email'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['merchant_id'] = this.merchantId;
    data['email'] = this.email;
    return data;
  }
}

class ItemList {
  List<Items>? items;
  ShippingAddress? shippingAddress;

  ItemList({this.items, this.shippingAddress});

  ItemList.fromJson(Map<String, dynamic> json) {
    if (json['items'] != null) {
      items = <Items>[];
      json['items'].forEach((v) {
        items!.add(new Items.fromJson(v));
      });
    }
    shippingAddress = json['shipping_address'] != null
        ? new ShippingAddress.fromJson(json['shipping_address'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    if (this.shippingAddress != null) {
      data['shipping_address'] = this.shippingAddress!.toJson();
    }
    return data;
  }
}

class Items {
  String? name;
  String? price;
  String? currency;
  String? tax;
  int? quantity;
  String? imageUrl;

  Items(
      {this.name,
      this.price,
      this.currency,
      this.tax,
      this.quantity,
      this.imageUrl});

  Items.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    price = json['price'];
    currency = json['currency'];
    tax = json['tax'];
    quantity = json['quantity'];
    imageUrl = json['image_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['price'] = this.price;
    data['currency'] = this.currency;
    data['tax'] = this.tax;
    data['quantity'] = this.quantity;
    data['image_url'] = this.imageUrl;
    return data;
  }
}

class RelatedResources {
  Sale? sale;

  RelatedResources({this.sale});

  RelatedResources.fromJson(Map<String, dynamic> json) {
    sale = json['sale'] != null ? new Sale.fromJson(json['sale']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.sale != null) {
      data['sale'] = this.sale!.toJson();
    }
    return data;
  }
}

class Sale {
  String? id;
  String? state;
  Amount? amount;
  String? paymentMode;
  String? protectionEligibility;
  String? protectionEligibilityType;
  TransactionFee? transactionFee;
  String? parentPayment;
  String? createTime;
  String? updateTime;
  List<Links>? links;

  Sale(
      {this.id,
      this.state,
      this.amount,
      this.paymentMode,
      this.protectionEligibility,
      this.protectionEligibilityType,
      this.transactionFee,
      this.parentPayment,
      this.createTime,
      this.updateTime,
      this.links});

  Sale.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    state = json['state'];
    amount =
        json['amount'] != null ? new Amount.fromJson(json['amount']) : null;
    paymentMode = json['payment_mode'];
    protectionEligibility = json['protection_eligibility'];
    protectionEligibilityType = json['protection_eligibility_type'];
    transactionFee = json['transaction_fee'] != null
        ? new TransactionFee.fromJson(json['transaction_fee'])
        : null;
    parentPayment = json['parent_payment'];
    createTime = json['create_time'];
    updateTime = json['update_time'];
    if (json['links'] != null) {
      links = <Links>[];
      json['links'].forEach((v) {
        links!.add(new Links.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['state'] = this.state;
    if (this.amount != null) {
      data['amount'] = this.amount!.toJson();
    }
    data['payment_mode'] = this.paymentMode;
    data['protection_eligibility'] = this.protectionEligibility;
    data['protection_eligibility_type'] = this.protectionEligibilityType;
    if (this.transactionFee != null) {
      data['transaction_fee'] = this.transactionFee!.toJson();
    }
    data['parent_payment'] = this.parentPayment;
    data['create_time'] = this.createTime;
    data['update_time'] = this.updateTime;
    if (this.links != null) {
      data['links'] = this.links!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TransactionFee {
  String? value;
  String? currency;

  TransactionFee({this.value, this.currency});

  TransactionFee.fromJson(Map<String, dynamic> json) {
    value = json['value'];
    currency = json['currency'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['value'] = this.value;
    data['currency'] = this.currency;
    return data;
  }
}

class Links {
  String? href;
  String? rel;
  String? method;

  Links({this.href, this.rel, this.method});

  Links.fromJson(Map<String, dynamic> json) {
    href = json['href'];
    rel = json['rel'];
    method = json['method'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['href'] = this.href;
    data['rel'] = this.rel;
    data['method'] = this.method;
    return data;
  }
}
