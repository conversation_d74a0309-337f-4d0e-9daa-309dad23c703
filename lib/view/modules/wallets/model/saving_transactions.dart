class SavingTransactions {
  String? id;
  String? clientId;
  String? reason;
  String? payerName;
  String? payerEmail;
  String? paymentMethod;
  String? paymentStatus;
  String? paidAmount;
  String? currency;
  String? txnId;
  String? sIp;
  String? sBrowser;
  String? sLocation;
  String? createAt;

  SavingTransactions(
      {this.id,
        this.clientId,
        this.reason,
        this.payerName,
        this.payerEmail,
        this.paymentMethod,
        this.paymentStatus,
        this.paidAmount,
        this.currency,
        this.txnId,
        this.sIp,
        this.sBrowser,
        this.sLocation,
        this.createAt});

  SavingTransactions.fromJson(Map<String, dynamic> json) {
    id = json['id'].toString();
    clientId = json['client_id'].toString();
    reason = json['reason'];
    payerName = json['payer_name'];
    payerEmail = json['payer_email'];
    paymentMethod = json['payment_method'];
    paymentStatus = json['payment_status'];
    paidAmount = json['paid_amount']!=null?json['paid_amount'].toString():"0.00";
    currency = json['currency'];
    txnId = json['txn_id'];
    sIp = json['s_ip'];
    sBrowser = json['s_browser'];
    sLocation = json['s_location'];
    createAt = json['create_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['client_id'] = this.clientId;
    data['reason'] = this.reason;
    data['payer_name'] = this.payerName;
    data['payer_email'] = this.payerEmail;
    data['payment_method'] = this.paymentMethod;
    data['payment_status'] = this.paymentStatus;
    data['paid_amount'] = this.paidAmount;
    data['currency'] = this.currency;
    data['txn_id'] = this.txnId;
    data['s_ip'] = this.sIp;
    data['s_browser'] = this.sBrowser;
    data['s_location'] = this.sLocation;
    data['create_at'] = this.createAt;
    return data;
  }
}
class SentTransaction {
  String? amount;
  String? reason;
  String? status;
  String? createdAt;
  String? recipientName;
  String? recipientFrom;
  SentTransaction(
      {this.amount,
        this.reason,
        this.status,
        this.createdAt,
        this.recipientName});

  SentTransaction.fromJson(Map<String, dynamic> json) {
    amount = json['amount']!=null?json['amount'].toString():"0.00";
    reason = json['reason']!=null?json['reason'].toString():"";
    status = json['status']!=null?json['status'].toString():"";
    createdAt = json['created_at']!=null?json['created_at'].toString():"";
    recipientName = json['recipient_name']!=null?json['recipient_name'].toString():"";
    recipientFrom = json['sender_name']!=null?json['sender_name'].toString():"";

  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['amount'] = this.amount;
    data['reason'] = this.reason;data['sender_name'] = this.recipientFrom;
    data['status'] = this.status;
    data['created_at'] = this.createdAt;
    data['recipient_name'] = this.recipientName;
    return data;
  }
}
