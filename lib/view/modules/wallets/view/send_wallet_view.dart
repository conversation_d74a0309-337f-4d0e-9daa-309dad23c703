import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../controller/user_controller.dart';
import '../../../../core/routes/app_routes.dart';
import '../../../../core/widgets/shimmer_effect.dart';
import '../controller/wallet_controller.dart';


class SendWalletView extends StatefulWidget {
  const SendWalletView({super.key});

  @override
  State<SendWalletView> createState() => _SendWalletViewState();
}

class _SendWalletViewState extends State<SendWalletView> {
  WalletController controller = Get.find();
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    scrollController.addListener(scrollListener);
    controller.transactionViewType.value = 'sent';
    controller.fetchSentTransactions(page: 1);
  }

  scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent &&
        controller.totalSentTransactionsCount.value >
            controller.sentTransactions.length) {
      controller.fetchSentTransactions(
          page: controller.currentSentTransactionsPage.value + 1);
      controller
          .currentSentTransactionsPage.value++; // Increment the page counter
    }
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Transfer Money",
            size: 17, fontWeight: FontWeight.w700),
        actions: [],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [
            widgetAddAmount(context),Widgets.heightSpaceH2,widgetTransactionsCard()
          ],
        ),
      ),
    );
  }

  widgetAddAmount(BuildContext context) {
    return Container(
      width: 1.sw,
      padding: EdgeInsets.all(15),
      decoration: Widgets.blockDecoration,
      child: Column(crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Widgets.heightSpaceH1,
          Center(
              child: Texts.textMedium("Account Balance",
                  size: 11, color: Colors.black)),
          Widgets.heightSpaceH05,
          GetBuilder<UserController>(
            builder: (walletController) {
              return  Center(child: Texts.textBlock("\$ ${walletController.userModel?.accountBalnce ?? "0.00"}", size: 30));
            },
          ),
          Widgets.heightSpaceH3,
          EntryField(color: ColorConstants.halfWhite,
            label: "Amount (USD)",
            hint: "How much?",controller: controller.amountSendController,
            textInputType: TextInputType.numberWithOptions(decimal: true),
            borderRadius: 10,
          ),

          EntryField(color: ColorConstants.halfWhite,
            label: "Recipient's Ensuram ID (e.g., ENS-1)",
            hint: "Write here",controller: controller.recipientController,
            borderRadius: 10,
          ),
          EntryField(color: ColorConstants.halfWhite,
            label: "Reason",controller: controller.reasonController,
            hint: "Write here",
            borderRadius: 10,
          ),

          Widgets.heightSpaceH1,
          CustomButton(
            onTap: () {

              if(controller.amountSendController.text.isEmpty){
                Widgets.showSnackBar("Error", "Please enter an amount");
                return;
              }

                if(controller.recipientController.text.isEmpty){
                Widgets.showSnackBar("Error", "Please enter a recipient");
                return;
              }
                if(controller.reasonController.text.isEmpty){
                Widgets.showSnackBar("Error", "Please enter a reason");
                return;
              }else{
                transferPayment(context);
              }



              },
            label: "SEND",
          ),
        ],
      ),
    );
  }
  Widget widgetTransactionsCard() {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
         Row(
            children: [
              Texts.textBlock("Transaction History", size: 15),
              Spacer(),
              PopupMenuButton<String>(color: Colors.white,
                icon: Icon(Icons.filter_list, color: Colors.black87, size: 20),
                onSelected: (String filter) {
                  // Handle filter selection
                  controller.transactionFilter.value = filter;
controller.fetchSentTransactions(page: 1);
                },
                itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
                  PopupMenuItem<String>(
                    value: 'all',
                    child: Row(
                      children: [
                        Icon(
                          Icons.all_inclusive,
                          color: controller.transactionFilter.value == 'all'
                              ? ColorConstants.secondaryColor
                              : Colors.grey,
                          size: 18,
                        ),
                        SizedBox(width: 8),
                        Text(
                          'All Transactions',
                          style: TextStyle(
                            color: controller.transactionFilter.value == 'all'
                                ? ColorConstants.secondaryColor
                                : Colors.black,
                            fontWeight: controller.transactionFilter.value == 'all'
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem<String>(
                    value: 'today',
                    child: Row(
                      children: [
                        Icon(
                          Icons.today,
                          color: controller.transactionFilter.value == 'today'
                              ? ColorConstants.secondaryColor
                              : Colors.grey,
                          size: 18,
                        ),
                        SizedBox(width: 8),
                        Text(
                          'Today',
                          style: TextStyle(
                            color: controller.transactionFilter.value == 'today'
                                ? ColorConstants.secondaryColor
                                : Colors.black,
                            fontWeight: controller.transactionFilter.value == 'today'
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem<String>(
                    value: 'past',
                    child: Row(
                      children: [
                        Icon(
                          Icons.history,
                          color: controller.transactionFilter.value == 'past'
                              ? ColorConstants.secondaryColor
                              : Colors.grey,
                          size: 18,
                        ),
                        SizedBox(width: 8),
                        Text(
                          'Previous Transactions',
                          style: TextStyle(
                            color: controller.transactionFilter.value == 'past'
                                ? ColorConstants.secondaryColor
                                : Colors.black,
                            fontWeight: controller.transactionFilter.value == 'past'
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),


          // Transaction type toggle
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                // Sent toggle button
                Expanded(
                  child: InkWell(
                    onTap: () {
                      controller.transactionViewType.value = 'sent';
                      controller.fetchSentTransactions(page: 1);
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: controller.transactionViewType.value == 'sent'
                            ? ColorConstants.secondaryColor
                            : Colors.white,
                        borderRadius: BorderRadius.horizontal(left: Radius.circular(10)),
                      ),
                      child: Center(
                        child: Text(
                          "Sent",
                          style: TextStyle(
                            color: controller.transactionViewType.value == 'sent'
                                ? Colors.white
                                : Colors.black,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                // Received toggle button
                Expanded(
                  child: InkWell(
                    onTap: () {
                      controller.transactionViewType.value = 'received';


                      controller.fetchSentTransactions(page: 1);
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: controller.transactionViewType.value == 'received'
                            ? ColorConstants.secondaryColor
                            : Colors.white,
                        borderRadius: BorderRadius.horizontal(right: Radius.circular(10)),
                      ),
                      child: Center(
                        child: Text(
                          "Received",
                          style: TextStyle(
                            color: controller.transactionViewType.value == 'received'
                                ? Colors.white
                                : Colors.black,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          Widgets.heightSpaceH2,

          // Search bar
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10)
            ),
            child: TextField(
              textAlignVertical: TextAlignVertical.center,
              controller: controller.searchTransactionController,
              onChanged: (value) {

              },
              decoration: InputDecoration(
                prefixIcon: IconButton(
                  padding: EdgeInsets.zero,
                  icon: const Icon(CupertinoIcons.search,
                    color: Colors.black38, size: 23),
                  onPressed: () {
                    // Perform the search here
                  },
                ),
                hintStyle: const TextStyle(
                  color: Colors.black38,
                  fontSize: 15,
                  decoration: TextDecoration.none
                ),
                border: InputBorder.none,
                hintText: "Search transactions"
              ),
              style: const TextStyle(
                color: Colors.grey,
                fontSize: 15,
                decoration: TextDecoration.none
              ),
            ),
          ),




          Widgets.heightSpaceH2,
          controller.isSentTransactionsLoading.value
              ? const ShimmerListSkeleton()
              : controller.sentTransactions.isNotEmpty
              ? ListView.separated(
              physics: const NeverScrollableScrollPhysics(),
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return    controller.transactionViewType.value == 'sent'?Widgets.sendTransactionCard( controller.sentTransactions[index]):Widgets.receivedTransactionCard(controller.sentTransactions[index]);
              },
              separatorBuilder: (context, index) {
                return Widgets.heightSpaceH1;
              },
              itemCount:
              controller.sentTransactions.length ?? 0)
              : Widgets.noRecordsFound(title: "No Transactions Found"),
          if (controller.isSentTransactionsLoading.value)
            Center(child: CircularProgressIndicator()),

          Widgets.heightSpaceH2, Widgets.heightSpaceH2, Widgets.heightSpaceH2,
        ],
      );
    });
  }

  // Helper method to build sent transactions list
  Widget _buildSentTransactionsList() {
    return controller.isSentTransactionsLoading.value && controller.sentTransactions.isEmpty
        ? const ShimmerListSkeleton()
        : controller.sentTransactions.isNotEmpty
            ? ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  return Widgets.sendTransactionCard(controller.sentTransactions[index]);
                },
                separatorBuilder: (context, index) {
                  return Widgets.heightSpaceH1;
                },
                itemCount: controller.sentTransactions.length)
            : Widgets.noRecordsFound(title: "No Sent Transactions Found");
  }

  // Helper method to build received transactions list
  Widget _buildReceivedTransactionsList() {
    return controller.isReceivedTransactionsLoading.value && controller.receivedTransactions.isEmpty
        ? const ShimmerListSkeleton()
        : controller.receivedTransactions.isNotEmpty
            ? ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  return Widgets.sendTransactionCard(controller.receivedTransactions[index]);
                },
                separatorBuilder: (context, index) {
                  return Widgets.heightSpaceH1;
                },
                itemCount: controller.receivedTransactions.length)
            : Widgets.noRecordsFound(title: "No Received Transactions Found");
  }
  void transferPayment(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true, // This is important
      builder: (context) {
        return Padding(
          // Add padding to account for keyboard
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom
          ),
          child: DraggableScrollableSheet(
            expand: false,
            maxChildSize: 1, // Allow more space for scrolling
            minChildSize: 0.3,
            initialChildSize: 0.37, // Start a bit larger
            builder: (context, scrollController) {
              return SingleChildScrollView(
                controller: scrollController, // Connect the scroll controller
                padding: const EdgeInsets.all(15.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min, // Important for proper sizing
                  children: [
                    Text(
                      'Confirmation',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    Widgets.heightSpaceH1,
                    Widgets.divider(),
                    Widgets.heightSpaceH2,
                    Text(
                      "Write your account password to continue",
                      style: TextStyle(
                        color: Colors.black87,
                        fontSize: 14
                      )
                    ),
                    Widgets.heightSpaceH3,
                    EntryField(
                      color: ColorConstants.halfWhite,
                      label: "Password",obscureText: true,
                      hint: "Write here",
                      controller: controller.passwordController,
                      borderRadius: 10,

                    ),
                    Widgets.heightSpaceH3,
                    Row(
                      children: [
                        Expanded(
                          child: CustomButton(
                            label: "CANCEL",
                            color: Colors.red,
                            onTap: () {
                              Get.back();
                            },
                            textStyle: TextStyle(
                              fontSize: 12,
                              color: Colors.white,
                              fontWeight: FontWeight.w600
                            ),
                          ),
                        ),
                        Widgets.widthSpaceW2,
                        Expanded(
                          child: CustomButton(
                            label: "CONFIRM",
                            color: ColorConstants.secondaryColor,
                            onTap: () {
                              if(controller.passwordController.text.isEmpty){
                                Widgets.showSnackBar("Error", "Please enter your password");
                                return;
                              } else {
                                controller.sentWallet();
                              }
                            },
                            textStyle: TextStyle(
                              fontSize: 12,
                              color: Colors.white,
                              fontWeight: FontWeight.w600
                            ),
                          ),
                        )
                      ],
                    ),
                    // Add extra space at bottom to ensure everything is visible
                    SizedBox(height: 20),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
}
