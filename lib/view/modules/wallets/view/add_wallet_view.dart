import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import 'package:ensuram/view/modules/wallets/controller/wallet_controller.dart';

import '../../../../controller/user_controller.dart';
import '../../../../core/widgets/shimmer_effect.dart';


class AddWalletView extends StatefulWidget {
  const AddWalletView({super.key});

  @override
  State<AddWalletView> createState() => _AddWalletViewState();
}

class _AddWalletViewState extends State<AddWalletView> {

  final ScrollController scrollController = ScrollController();

  WalletController controller = Get.find();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Your Wallet",
            size: 17, fontWeight: FontWeight.w700),
        actions: [],
      ),
      body: SingleChildScrollView(controller: scrollController,
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [
            widgetAddAmount(context),Widgets.heightSpaceH2,widgetTransactionsCard()
          ],
        ),
      ),
    );
  }

  widgetAddAmount(BuildContext context) {
    return Container(
      width: 1.sw,
      padding: EdgeInsets.all(15),
      decoration: Widgets.blockDecoration,
      child: Column(
        children: [
          Widgets.heightSpaceH1,
          Center(
              child: Texts.textMedium("Account Balance",
                  size: 11, color: Colors.black)),
          Widgets.heightSpaceH05,
          GetBuilder<UserController>(
            builder: (walletController) {
              return  Center(child: Texts.textBlock("\$ ${walletController.userModel?.accountBalnce ?? "0.00"}", size: 30));
            },
          ),
          Widgets.heightSpaceH3,
          EntryField(color: ColorConstants.halfWhite,
            label: "Enter the amount you want to save",
            hint: "How much?",controller: controller.amountController,
            textInputType: TextInputType.numberWithOptions(decimal: true),
            borderRadius: 10,
          ),
          Obx(() =>
              CustomDropdown(color: ColorConstants.halfWhite,
                value: controller.selectedPaymentMethod.value == ""
                    ? null
                    : controller.selectedPaymentMethod.value,
                label: "Payment Method",
                onTap: () {
                  showPaymentMethodsBottomSheet(context);
                },textColor: controller.selectedPaymentMethod.value == ""
                    ? Colors.black45
                    : Colors.black,
                hint: 'Select here',
              ),
          ),
          Widgets.heightSpaceH2,
          CustomButton(
            onTap: () {

              if(controller.amountController.text.isEmpty){
                Widgets.showSnackBar("Error", "Please enter an amount");
                return;
              }else{

                if(controller.selectedPaymentMethod.value == "PayPal"){
                  controller.paymentMethodPaypal(context);
                }else if(controller.selectedPaymentMethod.value == "Mobile Money"){

                  controller.handlePaymentInitialization(context);
                }else if(controller.selectedPaymentMethod.value == "Credit/Debit Card"){
                }else{
                  Widgets.showSnackBar("Error", "Please select a payment method");
                }
              }
            },
            label: "PROCEED",
          ),
        ],
      ),
    );
  }

  Widget widgetTransactionsCard() {
    return Obx(
          () {
        return ListView(
          shrinkWrap: true,
          physics:
          const NeverScrollableScrollPhysics(), // Since parent is scrollable
          children: [ Texts.textBlock("Transaction History", size: 15),


            Widgets.heightSpaceH2,
            Container(
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(10)),
              child: TextField(
                textAlignVertical: TextAlignVertical.center,
                // focusNode: _searchFocusNode,
                // controller: spotController.searchController,
                onChanged: (value) {},
                decoration: InputDecoration(
                    prefixIcon: IconButton(
                      padding: EdgeInsets.zero,
                      icon: const Icon(CupertinoIcons.search,
                          color: Colors.black38, size: 23),
                      onPressed: () {
                        // Perform the search here
                      },
                    ),
                    hintStyle: const TextStyle(
                        color: Colors.black38,
                        fontSize: 15,
                        decoration: TextDecoration.none),
                    border: InputBorder.none,
                    hintText: "Search transactions"),
                style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 15,
                    decoration: TextDecoration.none),
              ),
            ),
            Widgets.heightSpaceH2,
            controller.isTransactionsLoading.value
                ? const ShimmerListSkeleton()
                : controller.transactions.isNotEmpty
                ? ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  return Widgets.transactionCard(transaction: controller.transactions[index]);
                },
                separatorBuilder: (context, index) {
                  return Widgets.heightSpaceH1;
                },
                itemCount:
                controller.transactions.length ?? 0)
                : Widgets.noRecordsFound(title: "No Transactions Found"),
            if (controller.isTransactionsMoreLoading.value)
              CircularProgressIndicator()
          ],
        );
      },
    );
  }

  void showPaymentMethodsBottomSheet(BuildContext context) {
    final RxString selectedMethod = ''.obs;

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.6,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Texts.textBlock("Select Payment Method",
                          size: 18, fontWeight: FontWeight.w600),
                      IconButton(
                        icon: Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  Divider(),
                  Expanded(
                    child: ListView(
                      controller: scrollController,
                      children: [
                        _buildPaymentMethodTile(
                          context,
                          title: "PayPal",
                          icon: Icons.paypal,
                          iconColor: Colors.blue,
                          selectedMethod: selectedMethod,
                        ),
                        Divider(),
                        _buildPaymentMethodTile(
                          context,
                          title: "Mobile Money",
                          icon: Icons.phone_android,
                          iconColor: Colors.green,
                          selectedMethod: selectedMethod,
                        ),
                        Divider(),
                        _buildPaymentMethodTile(
                          context,
                          title: "Credit/Debit Card",
                          icon: Icons.credit_card,
                          iconColor: Colors.orange,
                          selectedMethod: selectedMethod,
                        ),
                      ],
                    ),
                  ),
                  CustomButton(
                    onTap: () {
                      if (selectedMethod.value.isNotEmpty) {
                        Navigator.pop(context, selectedMethod.value);
                      } else {
                        Widgets.showSnackBar("Error", "Please select a payment method");
                      }
                    },
                    label: "CONFIRM",
                    color: ColorConstants.secondaryColor,
                  ),
                ],
              ),
            );
          },
        );
      },
    ).then((value) {
      if (value != null) {
        // Handle the selected payment method
        print("Selected payment method: $value");
        // You can update your UI or state here
      }
    });
  }

  Widget _buildPaymentMethodTile(
      BuildContext context, {
        required String title,
        required IconData icon,
        required Color iconColor,
        required RxString selectedMethod,
      }) {
    return Obx(() => ListTile(
      leading: Icon(icon, color: iconColor, size: 30),
      title: Text(title, style: TextStyle(fontWeight: FontWeight.w500)),
      trailing: Radio<String>(
        value: title,
        groupValue: selectedMethod.value,
        activeColor: ColorConstants.primaryColor,
        onChanged: (value) {
          selectedMethod.value = value!;controller.selectedPaymentMethod.value = title;
        },
      ),
      onTap: () {controller.selectedPaymentMethod.value = title;
      selectedMethod.value = title;
      },
    ));
  }
  @override
  void initState() {
    super.initState();

    scrollController.addListener(scrollListener);
    controller.fetchTransactions(page: 1);
  }

  scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent &&
        controller.totalTransactionsCount.value >
            controller.transactions.length) {
      controller.fetchTransactions(
          page: controller.currentPage.value + 1);
      controller
          .currentPage.value++; // Increment the page counter
    }
  }
 
}
