import 'dart:developer';

import 'package:easy_stepper/easy_stepper.dart';
import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/routes/app_routes.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/widgets.dart';
import 'package:ensuram/view/dashboard/controller/dashboard_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:smile_id/smile_id.dart';
import 'package:smile_id/smile_id_document_verification.dart';
import 'package:smile_id/smileid_messages.g.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../controller/user_controller.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../controller/wallet_controller.dart';

class WalletView extends StatefulWidget {
  const WalletView({super.key});

  @override
  State<WalletView> createState() => _WalletViewState();
}

class _WalletViewState extends State<WalletView> {
  Future<void> initPlatformState() async {
    if (!mounted) return;

    try {
      String partnerId = '7221';
      String authToken =
          "Mq+D6qftDjJj8dqsj+87bo1WBjaEo8OLozHCxZ2TihwwKByQYoSLO6oYEQQ+FvBBn+GEbIhleO+8hWVozHPQ+/ZirzwJwYIwvC7K/s7hS1eY+DYhpjZQ4YI5Zth+l+xkh9qlglVj/q5Y57UK4Ti2TT5ZkulsidAV4TvX85ahYYw=";
      String prodBaseUrl = 'https://api.smileidentity.com/v1/';
      String sandboxBaseUrl = 'https://testapi.smileidentity.com/v1/';
      SmileID.initializeWithConfig(
          useSandbox: true,
          config: FlutterConfig(
              partnerId: partnerId,
              authToken: authToken,
              prodBaseUrl: prodBaseUrl,
              sandboxBaseUrl: sandboxBaseUrl),
          enableCrashReporting: false);

      SmileID.setCallbackUrl(callbackUrl: Uri.parse("https://ensuram.com/callback/smileid.php"));

    } catch (e) {
      print('Error initializing SmileID: $e');
    }

  }
WalletController walletController = Get.put(WalletController());
  @override
  void initState() {
    Get.find<UserController>().fetchUserDetailsBackground();
    initPlatformState();
    super.initState();
  }

  int activeStep = 1;
  int activeStep2 = 0;
  // final SmileID _smileID = SmileID();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            widgetProfileCard(),
            Widgets.heightSpaceH2,
            GetBuilder<UserController>(
              builder: (userController) {
                return userController.userModel?.verificationStatus == "approved"
                    ? Container()
                    : widgetVerificationCard(context);},
            ),
            Widgets.heightSpaceH2,
            widgetEWalletCard(),
          ],
        ),
      ),
    );
  }

  widgetEWalletCard() {
    return Container(
      width: 1.sw,
      padding: EdgeInsets.all(15),
      decoration: Widgets.blockDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Texts.textBlock("E-Health Wallet", size: 15),
          Widgets.heightSpaceH1,
          Texts.textMedium(
              "Take control of your families health expenses with smart and effortless savings with ENSURAM E-Health wallet. No amount is too small, start saving today",
              size: 10,
              color: Colors.black38),
          Widgets.heightSpaceH2,
         GetBuilder<UserController>(
            builder: (walletController) {
              return  Center(child: Texts.textBlock("\$ ${walletController.userModel?.accountBalnce ?? "0.00"}", size: 26));
            },
          ),

          Widgets.heightSpaceH05,
          Center(
              child: Texts.textMedium("Account Balance",
                  size: 11, color: Colors.black)),
          Widgets.heightSpaceH2,
          CustomButton(
            label: "ADD MONEY TO YOUR ACCOUNT ",
            color: Colors.black,
            onTap: () {
              Get.toNamed(AppRoutes.userAddWallet);
            },
            textStyle: TextStyle(
                fontSize: 10, color: Colors.white, fontWeight: FontWeight.w600),
          ),
          Widgets.heightSpaceH1,
          CustomButton(
            label: "TRANSFER MONEY",
            color: ColorConstants.secondaryColor,
            onTap: () {
              Get.toNamed(AppRoutes.userSendWallet);
            },
            textStyle: TextStyle(
                fontSize: 10, color: Colors.white, fontWeight: FontWeight.w600),
          )
        ],
      ),
    );
  }

  widgetExpensesCard() {
    return Container(
      width: 1.sw,
      padding: EdgeInsets.all(15),
      decoration: Widgets.blockDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Texts.textBlock("Expenses", size: 15),
          Widgets.heightSpaceH2,
          Row(
            children: [
              Expanded(
                  child: Widgets.buildDropdown(
                      textColor: Colors.black54,
                      align: TextAlign.start,
                      value: null,
                      hint: "Start Date",
                      textSize: 12,
                      onTap: () {},
                      icon: Icons.calendar_month,
                      iconSize: 15,
                      color: ColorConstants.halfWhite)),
              SizedBox(
                width: 10,
              ),
              Expanded(
                  child: Widgets.buildDropdown(
                      textColor: Colors.black54,
                      align: TextAlign.start,
                      icon: Icons.calendar_month,
                      value: null,
                      hint: "End Date",
                      textSize: 12,
                      iconSize: 15,
                      onTap: () {},
                      color: ColorConstants.halfWhite)),
            ],
          ),
          Widgets.heightSpaceH2,
          CustomButton(
            label: "SEARCH",
            color: ColorConstants.primaryColor,
            onTap: () {},
            textStyle: TextStyle(
                fontSize: 12, color: Colors.white, fontWeight: FontWeight.w600),
          )
        ],
      ),
    );
  }

  widgetVerificationCard(BuildContext context) {
    return Container(
      width: 1.sw,
      padding: EdgeInsets.all(15),
      decoration: Widgets.blockDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Texts.textBlock("Complete your registration process",
              size: 15, maxline: 2),
          Widgets.heightSpaceH2,
          Row(
            children: [
              Icon(
                Icons.verified_user_outlined,
                color: ColorConstants.primaryColor,
                size: 20,
              ),
              SizedBox(width: 8),
              Texts.textBlock("Identity Verification",
                  size: 13, maxline: 2, color: Colors.black54),
            ],
          ),
          Widgets.heightSpaceH1,
          Texts.textMedium(
              "We'll verify you to ensure that you get the most out of Ensuram.",
              size: 10,
              color: Colors.black38),
          Widgets.heightSpaceH2,
          CustomButton(
            label: "VERIFY NOW",
            color: Colors.red,
            onTap: () {
              Navigator.of(context).push(
                //Requires Navigator.of(context).push in order to load
                MaterialPageRoute<void>(
                  builder: (BuildContext context) => Scaffold(
                    appBar: AppBar(title: const Text("Document Verification")),
                    body: SmileIDDocumentVerification(
                        userId:Get.find<UserController>().userModel?.userId ?? "",
                        countryCode: "GH",
                        useStrictMode:
                            true,
                        onSuccess: (String? result) {
                        },
                        onError: (String errorMessage) {

                        }),
                  ),
                ),
              );
            },
            textStyle: TextStyle(
                fontSize: 12, color: Colors.white, fontWeight: FontWeight.w600),
          )
        ],
      ),
    );
  }

  widgetProfileCard() {
    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Column(
            children: [
              Container(
                width: 1.sw,
                height: 60,
                decoration: BoxDecoration(color: ColorConstants.primaryColor),
              ),
              Container(
                width: 1.sw,
                height: 210,
                decoration: BoxDecoration(color: Colors.white),
              )
            ],
          ),
        ),
        Positioned(
            top: 20,
            left: 10,
            right: 10,
            child: Column(
              children: [
                GetBuilder<UserController>(
                  builder: (profileController) {
                    return ClipRRect(borderRadius: BorderRadius.circular(10),
                      child: Widgets.networkImage(
                        "${Endpoints.domain}/customer/${profileController.userModel?.userId}/profile/${profileController.userModel?.userPhoto}",
                        width: 90,
                        height: 90,
                      ),
                    );
                  },
                ),
                Widgets.heightSpaceH1,
                GetBuilder<UserController>(
                  builder: (profileController) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Texts.textBlock(
                            "${profileController.userModel?.userFirstname ?? ""} ${profileController.userModel?.userLastname ?? ""} ",
                            size: 14),profileController.userModel?.verificationStatus == "approved" ? Icon(Icons.verified_rounded,color: Colors.blue,size: 15,):SizedBox.shrink(),
                      ],
                    );
                  },
                ),
                Widgets.heightSpaceH3,
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Texts.textBlock("Your Loyal Level",
                          size: 12, fontFamily: "LatoLight"),
                      Texts.textBlock("How to become a gold member?",
                          size: 10,
                          color: ColorConstants.primaryColor,
                          fontFamily: "LatoLight"),
                    ],
                  ),
                ),
                SizedBox(
                  height: 100,
                  child: EasyStepper(
                      padding: EdgeInsets.zero,
                      activeStep: activeStep,
                      activeStepTextColor: ColorConstants.primaryColor,
                      finishedStepTextColor: ColorConstants.primaryColor,
                      internalPadding: 0,
                      unreachedStepTextColor: Colors.black38,
                      activeStepBackgroundColor: Colors.white,
                      activeStepBorderColor: Colors.white,
                      finishedStepBorderColor: Colors.white,
                      showLoadingAnimation: false,
                      finishedStepBackgroundColor: Colors.white,
                      stepRadius: 10,
                      lineStyle: LineStyle(
                        lineType: LineType.normal,
                        activeLineColor: ColorConstants.primaryColor,
                        lineLength: 60,
                        defaultLineColor: CupertinoColors.systemGrey5,
                        finishedLineColor: ColorConstants.primaryColor,
                        lineSpace: 0,
                      ),
                      showStepBorder: false,
                      steps: [
                        EasyStep(
                            customStep: CircleAvatar(
                              radius: 7,
                              backgroundColor: activeStep >= 0
                                  ? ColorConstants.primaryColor
                                  : Colors.black38,
                            ),
                            title: 'Silver',
                            topTitle: true),
                        EasyStep(
                          customStep: CircleAvatar(
                            radius: 7,
                            backgroundColor: activeStep >= 1
                                ? ColorConstants.primaryColor
                                : Colors.black38,
                          ),
                          title: 'Gold',
                          topTitle: true,
                        ),
                        EasyStep(
                          customStep: CircleAvatar(
                            radius: 7,
                            backgroundColor: activeStep >= 2
                                ? ColorConstants.primaryColor
                                : Colors.black38,
                          ),
                          title: 'Platinum',
                          topTitle: true,
                        ),
                        EasyStep(
                          customStep: CircleAvatar(
                            radius: 7,
                            backgroundColor: activeStep >= 3
                                ? ColorConstants.primaryColor
                                : Colors.black38,
                          ),
                          topTitle: true,
                          title: 'Ambassador',
                        ),
                      ],
                      onStepReached: (index) => activeStep = index),
                ),
              ],
            )),
        Positioned(
            top: 10,
            right: 10,
            child: InkWell(onTap: () {
              Get.find<DashboardController>().changeTabIndex(3);
            },
              child: Image.asset(
                Assets.editeIcon,
                width: 20,
                height: 20,
              ),
            )),
      ],
    );
  }
}
