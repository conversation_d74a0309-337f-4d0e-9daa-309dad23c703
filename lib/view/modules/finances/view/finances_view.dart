import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/view/modules/finances/controller/finances_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../core/constants/constants_list.dart';
import '../../../../core/routes/app_routes.dart';
import '../../../../core/widgets/custom_button.dart';

class FinancesView extends StatelessWidget {
  FinancesView({super.key});
  final FinanceController controller = Get.put(FinanceController());
  widgetAmount() {
    return Container(
      width: 1.sw,
      padding: EdgeInsets.all(15),
      decoration: Widgets.blockDecoration,
      child: Column(
        children: [
          Widgets.heightSpaceH1,
          Center(
              child: Texts.textMedium("Current Plan",
                  size: 13, color: Colors.black)),
          Widgets.heightSpaceH05,
          Center(child: Texts.textBlock("\$32.00 / Yearly", size: 30)),
          Widgets.heightSpaceH2,
          Center(
              child: Texts.textMedium("Next payment due",
                  size: 11, color: ColorConstants.secondaryColor)),
          Widgets.heightSpaceH05,
          Center(child: Texts.textBlock("0.00 / Yearly ", size: 20,color: ColorConstants.secondaryGreyColor)),
        ],
      ),
    );
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Billing & Payments", size: 20, fontWeight: FontWeight.w700),
        actions: [],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [


            Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10)),
                    child: TextField(
                      textAlignVertical: TextAlignVertical.center,
                      // focusNode: _searchFocusNode,
                      // controller: spotController.searchController,
                      onChanged: (value) {},
                      decoration: InputDecoration(
                          prefixIcon: IconButton(
                            padding: EdgeInsets.zero,
                            icon: const Icon(CupertinoIcons.search,
                                color: Colors.black38, size: 23),
                            onPressed: () {
                              // Perform the search here
                            },
                          ),
                          hintStyle: const TextStyle(
                              color: Colors.black38,
                              fontSize: 15,
                              decoration: TextDecoration.none),
                          border: InputBorder.none,
                          hintText: "Search"),
                      style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 15,
                          decoration: TextDecoration.none),
                    ),
                  ),
                ),
              ],
            ),
            Widgets.heightSpaceH2,
            SizedBox(
                height: 35,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount:
                  Data.billingTab.length, // Number of items in the list
                  itemBuilder: (context, index) {
                    return GestureDetector(
                        onTap: () {
                          controller
                              .selectItem(index); // Update the selected index
                        },
                        child: Obx(()=>Container(padding: EdgeInsets.symmetric(horizontal: 15,vertical: 5),
                          margin: const EdgeInsets.only(right: 10),

                          decoration: BoxDecoration(
                            color: controller.selectedIndex.value == index
                                ? ColorConstants.primaryColor // Color when selected
                                : Colors.white, // Default color
                            borderRadius: BorderRadius.circular(15),
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            Data.billingTab[index],
                            style: TextStyle(
                              color: controller.selectedIndex.value == index
                                  ? Colors.white
                                  : Colors.black54,
                            ),
                          ),
                        ),
                        ));
                  },
                )
            ),  Widgets.heightSpaceH3,
            ListView.separated(
                physics: NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  return Widgets.financeCard();
                },
                separatorBuilder: (context, index) {
                  return Widgets.heightSpaceH1;
                },
                itemCount: 5),
          ],
        ),
      ),
    );
  }
  void payBill(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.8,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Padding(
              padding: const EdgeInsets.all(15.0),
              child: Column(crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    'Pay Insurance Bill',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),Widgets.heightSpaceH1,Widgets.divider(),Widgets.heightSpaceH2,
                  RichText(
                      text: TextSpan(
                          text: 'Make sure to ',
                          style: TextStyle(color: Colors.black45, fontSize: 12),
                          children: [
                            TextSpan(
                              text: 'fill up the wallet ',
                              style: TextStyle(
                                color: Colors.blue,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  Get.toNamed(AppRoutes.userAddWallet);

                                },
                            ),
                            TextSpan(
                                text: ', before initiating this transaction.',
                                style: TextStyle(
                                  color: Colors.black45,
                                  fontSize: 12,
                                )),
                          ])),
                  Widgets.heightSpaceH2,
                  Widgets.columTexts(title: 'Billing No.', value: 'YOU32323'),
                  Widgets.heightSpaceH2,

                  Widgets.columTexts(title: 'Payment Amount', value: '\$34.44'),
                  Widgets.heightSpaceH2,

                  Widgets.columTexts(title: 'Payment Method', value: 'Ensuram  E-Wallet'),
                  Widgets.heightSpaceH3,
                  Widgets.divider(),
                  Widgets.heightSpaceH2,
                  CustomButton(
                    label: "PAY BY ENSURAM",
                    color: ColorConstants.secondaryColor,
                    onTap: () {},
                    textStyle: TextStyle(
                        fontSize: 12, color: Colors.white, fontWeight: FontWeight.w600),
                  )

                ],
              ),
            );
          },
        );
      },
    );
  }



}
