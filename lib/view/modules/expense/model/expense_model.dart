class Expense {
  String? expenseType;
  String? expenseDate;
  String? expenseAmount;

  Expense({this.expenseType, this.expenseDate, this.expenseAmount});

  Expense.fromJson(Map<String, dynamic> json) {
    expenseType = json['expense_type'];
    expenseDate = json['expense_date'];
    expenseAmount = json['expense_amount'].toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['expense_type'] = this.expenseType;
    data['expense_date'] = this.expenseDate;
    data['expense_amount'] = this.expenseAmount;
    return data;
  }
}
