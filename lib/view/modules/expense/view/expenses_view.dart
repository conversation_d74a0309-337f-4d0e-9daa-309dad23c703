import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/widgets/shimmer_effect.dart' show ShimmerListSkeleton;
import 'package:ensuram/core/widgets/text_widgets.dart';
import 'package:ensuram/view/modules/laboratory/controller/lab_controller.dart';
import 'package:ensuram/view/modules/laboratory/view/lab_tests_view.dart';
import 'package:ensuram/view/modules/pharmacy/controller/pharmacy_controller.dart';
import 'package:ensuram/view/modules/pharmacy/view/pharmacy_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/widgets.dart';
import '../controller/expense_controller.dart';

class ExpenseTrackerView extends StatefulWidget {
  const ExpenseTrackerView({super.key});

  @override
  State<ExpenseTrackerView> createState() => _ExpenseTrackerViewState();
}

class _ExpenseTrackerViewState extends State<ExpenseTrackerView> {


 ExpenseController controller =Get.put(ExpenseController());
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    scrollController.addListener(scrollListener);
  }

  scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent &&
        controller.totalOrdersCount.value >
            controller.orders.length) {
      controller.fetchOrders(
          page: controller.currentOrdersPage.value + 1);
      controller
          .currentOrdersPage.value++; // Increment the page counter
    }
  }
  @override
  Widget build(BuildContext context) {
  return Scaffold(


    backgroundColor: ColorConstants.halfWhite,
    appBar: AppBar(
      backgroundColor: ColorConstants.backgroundColor,
      elevation: 0,
      leadingWidth: 30,centerTitle: true,
      title: Texts.textBlock("Expense Tracker",
          size: 20, fontWeight: FontWeight.w700),

    ),
    body: widgetOrdersCard(),
  );
  }

  Widget widgetOrdersCard() {
    return Obx(() {
      return SingleChildScrollView(controller: scrollController,padding: EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

            widgetExpensesCard(),  Widgets.heightSpaceH2,


            controller.isOrdersLoading.value
                ? const ShimmerListSkeleton()
                : controller.orders.isNotEmpty
                ? ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  return Widgets.expenseCard(controller.orders[index]);
                },
                separatorBuilder: (context, index) {
                  return Widgets.heightSpaceH1;
                },
                itemCount:
                controller.orders.length ?? 0)
                : Widgets.noRecordsFound(title: "No expenses Found"),
            if (controller.isOrdersLoading.value)
              Center(child: CircularProgressIndicator()),

            Widgets.heightSpaceH2, Widgets.heightSpaceH2, Widgets.heightSpaceH2,
          ],
        ),
      );
    });
  }
 widgetExpensesCard() {
   return Container(
     width: 1.sw,
     padding: EdgeInsets.all(15),
     decoration: Widgets.blockDecoration,
     child: Column(
       crossAxisAlignment: CrossAxisAlignment.start,
       children: [
         Texts.textBlock("Track your Expense", size: 15),
         Widgets.heightSpaceH2,
         Row(
           children: [
             Expanded(
               child: Widgets.buildDropdown(
                 textColor: Colors.black54,color: ColorConstants.halfWhite,
                 align: TextAlign.start,
                 value: controller.startDate.value.isEmpty ? null : controller.startDate.value,
                 hint: "Start Date",
                 textSize: 12,
                 onTap: () async {
                   final DateTime? picked = await showDatePicker(
                     context: context,
                     initialDate: DateTime.now(),
                     firstDate: DateTime(2020),
                     lastDate: DateTime.now().add(Duration(days: 365)),
                     builder: (context, child) {
                       return Theme(
                         data: Theme.of(context).copyWith(
                           colorScheme: ColorScheme.light(
                             primary: ColorConstants.primaryColor,
                           ),
                         ),
                         child: child!,
                       );
                     },
                   );
                   if (picked != null) {
                     // Format date as yyyy-MM-dd (e.g., 2025-05-25)
                     String year = picked.year.toString();
                     String month = picked.month.toString().padLeft(2, '0');
                     String day = picked.day.toString().padLeft(2, '0');
                     controller.startDate.value = "$year-$month-$day";
                   }
                 },
                 icon: Icons.calendar_month,
                 iconSize: 15,

               ),
             ),
             SizedBox(
               width: 10,
             ),
             Expanded(
               child: Widgets.buildDropdown(
                 textColor: Colors.black54,
                 align: TextAlign.start,
                 icon: Icons.calendar_month,
                 value: controller.endDate.value.isEmpty ? null : controller.endDate.value,
                 hint: "End Date",
                 textSize: 12,
                 iconSize: 15,
                 onTap: () async {
                   final DateTime? picked = await showDatePicker(
                     context: context,
                     initialDate: DateTime.now(),
                     firstDate: DateTime(2020),
                     lastDate: DateTime.now().add(Duration(days: 365)),
                     builder: (context, child) {
                       return Theme(
                         data: Theme.of(context).copyWith(
                           colorScheme: ColorScheme.light(
                             primary: ColorConstants.primaryColor,
                           ),
                         ),
                         child: child!,
                       );
                     },
                   );
                   if (picked != null) {
                     // Format date as yyyy-MM-dd (e.g., 2025-05-25)
                     String year = picked.year.toString();
                     String month = picked.month.toString().padLeft(2, '0');
                     String day = picked.day.toString().padLeft(2, '0');
                     controller.endDate.value = "$year-$month-$day";
                   }
                 },
                 color: ColorConstants.halfWhite,
               ),
             ),
           ],
         ),
         Widgets.heightSpaceH2,
         CustomButton(
           label: "SEARCH",
           color: ColorConstants.primaryColor,
           onTap: () {
             // Validate dates if needed
             if (controller.startDate.value.isNotEmpty && controller.endDate.value.isNotEmpty) {
               DateTime start = DateTime.parse(controller.startDate.value);
               DateTime end = DateTime.parse(controller.endDate.value);

               if (end.isBefore(start)) {
                 // Show error message if end date is before start date
                 Widgets.showSnackBar("Alert", "End date cannot be before start date");
                 return;
               }
             }else if(controller.startDate.value.isEmpty || controller.endDate.value.isEmpty){
               Widgets.showSnackBar("Alert", "Please select start and end date");
               return;
             }

             // Reset page counter and fetch expenses with date filters
             controller.currentOrdersPage.value = 0;
             controller.orders.clear(); // Clear existing orders
             controller.fetchOrders(page: 1);
           },
           textStyle: TextStyle(
             fontSize: 12,
             color: Colors.white,
             fontWeight: FontWeight.w600
           ),
         )
       ],
     ),
   );
 }
}
