import 'package:ensuram/view/modules/expense/model/expense_model.dart';
import 'package:ensuram/view/modules/pharmacy/pharmacy_order_model.dart';
import 'package:get/get.dart';
import 'package:ensuram/core/services/http_service.dart';

import '../../../../core/constants/api_endpoints.dart';
import '../../laboratory/model/lab_book_model.dart';

class ExpenseController extends GetxController {
  RxList orders = <Expense>[].obs;
  RxBool isOrdersLoading = false.obs;
  RxBool isOrdersMoreLoading = false.obs;
  RxInt totalOrdersCount = 0.obs;
  RxInt currentOrdersPage = 0.obs;
  RxInt selectedIndex = 0.obs;

  // Add these properties for date filtering
  RxString startDate = ''.obs;
  RxString endDate = ''.obs;

  void selectItem(int index) {
    selectedIndex.value = index;
  }

  fetchOrders({int page = 1}) async {
    try {
      if (isOrdersLoading.value) return;
      if (page == 1) {
        isOrdersLoading.value = true;
      } else {
        isOrdersMoreLoading.value = true;
      }

      var response = await ApiService.getData(
          "${Endpoints.getExpenses}?page=$page&limit=15&startDate=${startDate.value}&endDate=${endDate.value}");
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          orders.clear();
          totalOrdersCount.value = 0;
          currentOrdersPage.value = 1;
        }
        orders.addAll(
          (response.data['expenses'] as List)
              .map((e) => Expense.fromJson(e))
              .toList(),
        );

        totalOrdersCount.value =
            int.parse(response.data['pagination']['total_records'].toString());
      }
    } catch (e) {
      print(e);
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    } finally {
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    }
  }
}
