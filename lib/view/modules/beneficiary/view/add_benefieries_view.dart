import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/utils/extensions.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../controller/beneficiary_controller.dart';

class AddBeneficiaryView extends StatelessWidget {
   AddBeneficiaryView({super.key});
  BeneficiaryController controller = Get.find();

  @override
  Widget build(BuildContext context) {

    return GestureDetector(onTap: (){

      context.hideKeyboard();
    },
      child: Scaffold(
        backgroundColor: ColorConstants.halfWhite,
        appBar: AppBar(
          leading: InkWell(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.arrow_back,
              color: ColorConstants.primaryBlackColor,
            ),
          ),
          backgroundColor: ColorConstants.backgroundColor,
          elevation: 0,
          centerTitle: true,
          title: Texts.textBlock(
              "Add Beneficiary",
              size: 17,
              fontWeight: FontWeight.w700),
          actions: [],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(15.0),
          child: Column(
            children: [
              Obx(() => Stack(
                clipBehavior: Clip.none,
                children: [
                  controller.selectedImage.value != null
                      ? CircleAvatar(
                          radius: 60,
                          backgroundImage: FileImage(controller.selectedImage.value!),
                        )
                      : CircleAvatar(
                          radius: 60,
                          backgroundImage: AssetImage(Assets.avatarSquareIcon),
                        ),
                  Positioned(
                    right: 1,
                    bottom: 0,
                    child: InkWell(
                      onTap: () {
                        controller.showImageSourceDialog(context);
                      },
                      child: Container(
                        height: 36,
                        width: 36,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(Icons.camera_alt, color: ColorConstants.secondaryColor),
                      ),
                    ),
                  ),
                ],
              )),
              Widgets.heightSpaceH3,
              Row(
                children: [
                  Expanded(
                    child: EntryField(
                      controller: controller.firstNameController,
                      label: "First Name",
                      hint: "e.g John",
                      borderRadius: 10,
                    ),
                  ),
                  SizedBox(width: 10),
                  Expanded(
                    child: EntryField(
                      controller: controller.lastNameController,
                      label: "Last Name",
                      hint: "e.g Win",
                      borderRadius: 10,
                    ),
                  ),
                ],
              ),
              Obx(() => CustomDropdown(
                value: controller.formattedDate.isEmpty ? null : controller.formattedDate,
                label: "Date of Birth",textColor: Colors.black,
                onTap: () => controller.selectDate(context),
                hint: 'mm/dd/yyyy',
              )),
              Widgets.heightSpaceH2,
              Obx(() => CustomDropdown(
                value: controller.selectedGender.value,
                label: "Gender",textColor: Colors.black,
                onTap: () {
                  Get.bottomSheet(
                    Container(
                      padding: EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: controller.genderOptions.map((gender) =>
                          ListTile(
                            title: Text(gender),
                            onTap: () {
                              controller.selectGender(gender);
                              Get.back();
                            },
                          )
                        ).toList(),
                      ),
                    ),
                  );
                },
                hint: 'e.g male',
              )),
              Widgets.heightSpaceH2,
              Obx(() => CustomDropdown(
                value: controller.selectedRelationship.value,
                label: "Relationship",
                textColor: Colors.black,
                onTap: () {
                  Get.bottomSheet(
                    Container(
                      padding: EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                      ),
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: controller.relationshipOptions.map((relationship) =>
                            ListTile(
                              title: Text(relationship),
                              onTap: () {
                                controller.selectedRelationship.value = relationship;
                                Get.back();
                              },
                            )
                          ).toList(),
                        ),
                      ),
                    ),
                  );
                },
                hint: 'Select relationship',
              )),
              Widgets.heightSpaceH2,
              EntryField(
                controller: controller.idNumberController,
                label: "ID Number",
                hint: "e.g 2323232323",textInputType: TextInputType.text,
                borderRadius: 10,
              ),
              EntryField(
                controller: controller.phoneNumberController,
                label: "Phone Number",textInputType: TextInputType.phone,
                hint: "e.g 2323232323",
                borderRadius: 10,
              ),
              Widgets.heightSpaceH2,
              CustomButton(
                onTap: controller.submit,
                label: "SUBMIT",
              )
            ],
          ),
        ),
      ),
    );
  }
}