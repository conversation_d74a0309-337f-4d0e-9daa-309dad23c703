import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/routes/app_routes.dart';
import 'package:ensuram/core/widgets/custom_dialog.dart';
import 'package:ensuram/view/modules/beneficiary/view/update_benefieries_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../core/widgets/shimmer_effect.dart';
import '../controller/beneficiary_controller.dart';
import '../model/beneificery_model.dart';

class UserBenefieriesListView extends StatefulWidget {
  const UserBenefieriesListView({super.key});

  @override
  State<UserBenefieriesListView> createState() =>
      _UserBenefieriesListViewState();
}

class _UserBenefieriesListViewState extends State<UserBenefieriesListView> {
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    controller.fetchBenefiecieries(page: 1);
    scrollController.addListener(scrollListener);
  }

  scrollListener() {
    if (scrollController.position.pixels ==
            scrollController.position.maxScrollExtent &&
        controller.totalCount.value > controller.benefieries.length) {
      controller.fetchBenefiecieries(page: controller.currentPage.value + 1);
      controller.currentPage.value++; // Increment the page counter
    }
  }

  final controller = Get.put(BeneficiaryController());


  Widget widgetListView() {
    return Obx(() {
      return SingleChildScrollView(
        controller: scrollController,
        padding: EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(10)),
              child: TextField(
                textAlignVertical: TextAlignVertical.center,
                // focusNode: _searchFocusNode,
                // controller: spotController.searchController,
                onChanged: (value) {},
                decoration: InputDecoration(
                    prefixIcon: IconButton(
                      padding: EdgeInsets.zero,
                      icon: const Icon(CupertinoIcons.search,
                          color: Colors.black38, size: 23),
                      onPressed: () {
                        // Perform the search here
                      },
                    ),
                    hintStyle: const TextStyle(
                        color: Colors.black38,
                        fontSize: 15,
                        decoration: TextDecoration.none),
                    border: InputBorder.none,
                    hintText: "Search Beneficiary"),
                style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 15,
                    decoration: TextDecoration.none),
              ),
            ),
            Widgets.heightSpaceH2,
            controller.isLoading.value
                ? const ShimmerListSkeleton()
                : controller.benefieries.isNotEmpty
                    ? ListView.separated(
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          Beneficary beneficiary =
                              controller.benefieries[index];
                          return Widgets.beneficiaryCard(
                              beneficiary: beneficiary,
                              onEditTap: () {
                                controller.firstNameController.text = beneficiary.firstName ?? "";
                                controller.lastNameController.text = beneficiary.lastName ?? "";
                                controller.idNumberController.text = beneficiary.idNumber ?? "";
                                controller.phoneNumberController.text = beneficiary.phoneNumber ?? "";
                                controller.selectedDate.value = DateTime.parse(beneficiary.dob ?? "");
                                controller.selectedGender.value = beneficiary.gender ?? "";
                                controller.currentImage.value = beneficiary.photo ?? "";
controller.selectedImage.value=null;
                                controller.currentEditingBeneficiaryId.value = beneficiary.id ?? "";
                                controller.selectedRelationship.value = beneficiary.relationship ?? "";
                                Get.to(() => UpdateBeneficiaryView());
                              },
                              onDeleteTap: ()
                              {
                                controller.deleteBeneficiary(beneficiary.beneficiaryId ?? "");


                              });
                        },
                        separatorBuilder: (context, index) {
                          return Widgets.heightSpaceH1;
                        },
                        itemCount: controller.benefieries.length ?? 0)
                    : Widgets.noRecordsFound(
                        title: "You have no beneficiaries"),
            if (controller.isLoading.value)
              Center(child: CircularProgressIndicator()),
            Widgets.heightSpaceH2,
            Widgets.heightSpaceH2,
            Widgets.heightSpaceH2,
          ],
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Beneficiaries",
            size: 17, fontWeight: FontWeight.w700),
        actions: [],
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: ColorConstants.secondaryColor,
        onPressed: () {
          controller.firstNameController.clear();
          controller.lastNameController.clear();
          controller.idNumberController.clear();
          controller.phoneNumberController.clear();controller.selectedRelationship.value = null;
          controller.selectedDate.value = null;
          controller.selectedGender.value = null;
          controller.selectedImage.value = null;
          Get.toNamed(AppRoutes.userAddBeneficiary);
        },
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
      ),
      body: widgetListView(),
    );
  }
}
