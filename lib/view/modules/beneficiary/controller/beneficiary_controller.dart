import 'dart:convert';
import 'dart:io';
import 'package:ensuram/core/constants/api_endpoints.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/services/http_service.dart';
import 'package:ensuram/core/widgets/text_widgets.dart';
import 'package:ensuram/core/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:http/http.dart' as http;

import '../../../../controller/user_controller.dart';
import '../../contributions/model/organization_model.dart';
import '../model/beneificery_model.dart';

class BeneficiaryController extends GetxController {
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final idNumberController = TextEditingController();
  final phoneNumberController = TextEditingController();
  RxList benefieries = <Beneficary>[].obs;
  RxBool isLoading = false.obs;
  RxBool isMoreLoading = false.obs;
  RxInt totalCount = 0.obs;
  RxInt currentPage = 0.obs;
  final Rx<DateTime?> selectedDate = Rx<DateTime?>(null);
  final Rx<String?> selectedGender = Rx<String?>(null);
  final Rx<String?> selectedRelationship = Rx<String?>(null);
  final Rx<File?> selectedImage = Rx<File?>(null);

  final List<String> genderOptions = ['Male', 'Female'];

  final List<String> relationshipOptions = [
    'Self',
    'Husband',
    'Wife',
    'Child',
    'Brother',
    'Sister',
    'Mother',
    'Father',
    'Grand father',
    'Grand mother',
    'Uncle',
    'Aunty',
    'Father-in-law',
    'Mother-in-law',
    'Sister-in-law',
    'Brother-in-law',
    'Friend',
    'Helper',"Partner"
  ];

  void selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      selectedDate.value = picked;
    }
  }

  showImageSourceDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Texts.textBlock("Select Image Source",
                      align: TextAlign.center),
                  GestureDetector(
                    onTap: () => Get.back(),
                    child: const Icon(Icons.clear, color: Colors.black54),
                  ),
                ],
              ),
              Widgets.heightSpaceH2,
              ListTile(
                dense: true,
                contentPadding: EdgeInsets.zero,
                leading: const Icon(Icons.camera_alt),
                title: const Text('Take Photo'),
                onTap: () {
                  Get.back();
                  pickImage(ImageSource.camera);
                },
              ),
              Divider(color: Colors.black26, thickness: .5),
              ListTile(
                dense: true,
                contentPadding: EdgeInsets.zero,
                leading: const Icon(Icons.photo_library),
                title: const Text('Choose from Gallery'),
                onTap: () {
                  Get.back();
                  pickImage(ImageSource.gallery);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  String get formattedDate {
    return selectedDate.value != null
        ? DateFormat('MM/dd/yyyy').format(selectedDate.value!)
        : '';
  }

  void selectGender(String gender) {
    selectedGender.value = gender;
  }

  addBeneficiary() async {
    Widgets.showLoader("Loading");
    var request = http.MultipartRequest(
        'POST', Uri.parse('${Endpoints.baseURL}${Endpoints.addBeneficiary}'));

    if (selectedImage.value != null) {
      var pic = await http.MultipartFile.fromPath(
          'photo', selectedImage.value?.path ?? "");
      request.files.add(pic);
    }

    request.headers['Authorization'] =
        'Bearer ${Get.find<UserController>().token ?? ""}';
    request.fields['first_name'] = firstNameController.text;
    request.fields['last_name'] = lastNameController.text;
    request.fields['phone_number'] = phoneNumberController.text;
    request.fields['id_number'] = idNumberController.text;
    request.fields['gender'] = genderOptions[selectedGender.value == "Male" ? 0 : 1];

    request.fields['dob'] = selectedDate.value != null
        ? DateFormat('yyyy-MM-dd').format(selectedDate.value!)
        : '';
request.fields['relationship'] = selectedRelationship.value ?? "";
print(request.fields);
    var response = await request.send();

    Widgets.hideLoader();
    var data = await response.stream.bytesToString();
    var decodedData = jsonDecode(data);
    print(decodedData);
    if (response.statusCode == 200) {
      if (decodedData['status'] == 'success') {   Get.back();fetchBenefiecieriesBackground(page: 1);
        Widgets.showSnackBar("Success", "Beneficiary added successfully");

      } else {
        Widgets.showSnackBar("Error", "Something went wrong");
      }
    } else {
      Widgets.showSnackBar("Error", decodedData['message'] ?? "");
    }
  }

  pickImage(ImageSource source, {bool isProfile = false}) async {
    try {
      final XFile? file = await ImagePicker().pickImage(source: source);
      if (file != null) {
        // Crop the image
        final croppedFile = await ImageCropper().cropImage(
          sourcePath: file.path,
          aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
          compressQuality: 70,
          uiSettings: [
            AndroidUiSettings(
              toolbarTitle: 'Crop Image',
              toolbarColor: ColorConstants.primaryColor,
              toolbarWidgetColor: Colors.white,
              initAspectRatio: CropAspectRatioPreset.square,
              lockAspectRatio: true,
            ),
          ],
        );

        if (croppedFile != null) {
          selectedImage.value = File(croppedFile.path);
          update(); // Trigger UI update
        }
      }
    } catch (e) {
      print('Error picking image: $e');
      Widgets.showSnackBar("Error", "Failed to pick image");
    }
  }

  void submit() {
    // Validate and submit beneficiary data
    if (firstNameController.text.isEmpty ||
        lastNameController.text.isEmpty ||
        selectedDate.value == null ||selectedRelationship.value == null ||
        selectedGender.value == null) {
      Widgets.showSnackBar('Error', 'Please fill all required fields');
      return;
    }
    // id_number
    // phone_number
    // first_name
    // last_name
    // relationship
    // dob
    // gender
    // id_number
    // phone_number
    // first_name
    // last_name
    // relationship
    // dob (required, string)
    // gender
    // id_number
    // phone_number
    // photo
    // Process submission
    // TODO: Implement API call or data storage
    addBeneficiary();
  }
  fetchBenefiecieries({int page = 1}) async {
    try {
      if (isLoading.value) return;
      if (page == 1) {
        isLoading.value = true;
      } else {
        isMoreLoading.value = true;
      }

      var response = await ApiService.getData(
          "${Endpoints.getUserBeneficiaries}?page=$page&limit=15");
      isLoading.value = false;
      isMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          benefieries.clear();
          totalCount.value = 0;
          currentPage.value = 1;
        }
        benefieries.addAll(
          (response.data['data'] as List)
              .map((e) => Beneficary.fromJson(e))
              .toList(),
        );

        totalCount.value =
            int.parse(response.data['pagination']['total'].toString());
      }
    } catch (e) {
      print(e);
      isLoading.value = false;
      isMoreLoading.value = false;
    } finally {
      isLoading.value = false;
      isMoreLoading.value = false;
    }
  }
  fetchBenefiecieriesBackground({int page = 1}) async {
    try {

      var response = await ApiService.getData(
          "${Endpoints.getUserBeneficiaries}?page=$page&limit=15");

      if (response.status == true) {
        if (page == 1) {
          benefieries.clear();
          totalCount.value = 0;
          currentPage.value = 1;
        }
        benefieries.addAll(
          (response.data['data'] as List)
              .map((e) => Beneficary.fromJson(e))
              .toList(),
        );

        totalCount.value =
            int.parse(response.data['pagination']['total'].toString());
      }
    } catch (e) {
      print(e);
      isLoading.value = false;
      isMoreLoading.value = false;
    } finally {
      isLoading.value = false;
      isMoreLoading.value = false;
    }
  }

  @override
  void onClose() {
    firstNameController.dispose();
    lastNameController.dispose();
    idNumberController.dispose();
    phoneNumberController.dispose();
    super.onClose();
  }


  deleteBeneficiary(String id) async {
    try {
      Widgets.showLoader("Deleting beneficiary");
      var response = await ApiService.postData(
        Endpoints.deleteBeneficiary,
        {"beneficiary_id": id}
      );
      Widgets.hideLoader();

      if (response.status == true) {

        fetchBenefiecieriesBackground(page: 1);
      } else {
        Widgets.showSnackBar("Error", response.message ?? "Failed to delete beneficiary");
      }
    } catch (e) {
      print(e);
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", "An error occurred while deleting beneficiary");
    }
  }

  // Edit beneficiary - populate form with existing data
  // editBeneficiary(Beneficary beneficiary) {
  //   // Populate form fields with beneficiary data
  //   firstNameController.text = beneficiary.firstName ?? "";
  //   lastNameController.text = beneficiary.lastName ?? "";
  //   idNumberController.text = beneficiary.idNumber ?? "";
  //   phoneNumberController.text = beneficiary.phoneNumber ?? "";
  //
  //   // Set gender
  //   if (beneficiary.gender != null) {
  //     selectedGender.value = beneficiary.gender == "M" ? "Male" : "Female";
  //   }
  //
  //   // Set relationship
  //   if (beneficiary.relationship != null) {
  //     selectedRelationship.value = beneficiary.relationship;
  //   }
  //
  //   // Set date of birth if available
  //   if (beneficiary.dob != null) {
  //     try {
  //       selectedDate.value = DateTime.parse(beneficiary.dob!);
  //     } catch (e) {
  //       print("Error parsing date: $e");
  //     }
  //   }
  //
  //   // Store beneficiary ID for update
  //   currentEditingBeneficiaryId.value = beneficiary.id;
  //
  //   // Navigate to edit screen
  //   Get.toNamed(AppRoutes.userUpdateBeneficiary);
  // }

  // Search beneficiaries

  // Add this property to track which beneficiary is being edited
  final RxString currentEditingBeneficiaryId = "".obs;
  final RxString currentImage = "".obs;

  // Update beneficiary
  updateBeneficiary() async {
    if (firstNameController.text.isEmpty ||
        lastNameController.text.isEmpty ||
        selectedDate.value == null ||
        selectedGender.value == null ||
        selectedRelationship.value == null) {
      Widgets.showSnackBar('Error', 'Please fill all required fields');
      return;
    }

    try {
      Widgets.showLoader("Updating beneficiary");

      // Create multipart request
      var request = http.MultipartRequest(
          'POST', Uri.parse('${Endpoints.baseURL}${Endpoints.updateBeneficiary}'));


      if (selectedImage.value != null) {
        var pic = await http.MultipartFile.fromPath(
            'photo', selectedImage.value?.path ?? "");
        request.files.add(pic);
      }
      // Add authorization header
      request.headers['Authorization'] =
          'Bearer ${Get.find<UserController>().token ?? ""}';

      // Add form fields
      request.fields['id'] = currentEditingBeneficiaryId.value;
      request.fields['first_name'] = firstNameController.text;
      request.fields['last_name'] = lastNameController.text;
      request.fields['dob'] = selectedDate.value != null
          ? DateFormat('yyyy-MM-dd').format(selectedDate.value!)
          : '';
      request.fields['gender'] = selectedGender.value!;
      request.fields['relationship'] = selectedRelationship.value ?? "self";
      request.fields['id_number'] = idNumberController.text;
      request.fields['phone_number'] = phoneNumberController.text;

      // Send request
      var response = await request.send();
      var data = await response.stream.bytesToString();
      var decodedData = jsonDecode(data);

      Widgets.hideLoader();

      if (response.statusCode == 200) {
        if (decodedData['status'] == 'success') {
          Get.back();
          fetchBenefiecieriesBackground(page: 1);
          Widgets.showSnackBar("Success", decodedData['message'] ?? "Beneficiary updated successfully");

        } else {
          Widgets.showSnackBar("Error", decodedData['message'] ?? "Failed to update beneficiary");
        }
      } else {
        Widgets.showSnackBar("Error", decodedData['message'] ?? "Failed to update beneficiary");
      }
    } catch (e) {
      print(e);
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", "An error occurred while updating beneficiary");
    }
  }
}
