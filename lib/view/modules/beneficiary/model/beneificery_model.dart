class Beneficary {
  String? id;
  String? beneficiaryId;
  String? firstName;
  String? lastName;
  String? name;
  String? dob;
  String? gender;
  String? idNumber;
  String? phoneNumber;
  String? photo;
  String? createdAt;String? relationship;

  Beneficary(
      {this.id,
        this.beneficiaryId,
        this.firstName,
        this.lastName,
        this.name,
        this.dob,
        this.gender,
        this.idNumber,
        this.phoneNumber,
        this.photo,
        this.createdAt});

  Beneficary.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    beneficiaryId = json['beneficiary_id'];
    firstName = json['first_name'];relationship = json['relationship'];
    lastName = json['last_name'];
    name = json['name'];
    dob = json['dob'];
    gender = json['gender'];
    idNumber = json['id_number'];
    phoneNumber = json['phone_number'];
    photo = json['photo'];
    createdAt = json['created_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['beneficiary_id'] = this.beneficiaryId;
    data['first_name'] = this.firstName;
    data['last_name'] = this.lastName;
    data['name'] = this.name;
    data['dob'] = this.dob;data['relationship'] = this.relationship;
    data['gender'] = this.gender;
    data['id_number'] = this.idNumber;
    data['phone_number'] = this.phoneNumber;
    data['photo'] = this.photo;
    data['created_at'] = this.createdAt;
    return data;
  }
}
