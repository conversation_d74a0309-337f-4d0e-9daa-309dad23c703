import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../controller/user_controller.dart';
import '../../../../core/widgets/shimmer_effect.dart';


class NotificationsView extends StatefulWidget {
  const NotificationsView({super.key});

  @override
  State<NotificationsView> createState() => _NotificationsViewState();
}

class _NotificationsViewState extends State<NotificationsView> {

  UserController controller = Get.find();
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    scrollController.addListener(scrollListener);
    controller.fetchNotifcations(page: 1);
  }

  scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent &&
        controller.totalOrdersCount.value >
            controller.notifications.length) {
      controller.fetchNotifcations(
          page: controller.currentOrdersPage.value + 1);
      controller
          .currentOrdersPage.value++; // Increment the page counter
    }
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Notifications",
            size: 17, fontWeight: FontWeight.w700),
        actions: [],
      ),
      body: widgetOrdersCard(),

    );
  }

  Widget widgetOrdersCard() {
    return Obx(() {
      return SingleChildScrollView(controller: scrollController,padding: EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [


            controller.isNotificationLoading.value
                ? const ShimmerListSkeleton()
                : controller.notifications.isNotEmpty
                ? ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  return Widgets.notificationCard(controller.notifications[index]);
                },
                separatorBuilder: (context, index) {
                  return Widgets.heightSpaceH1;
                },
                itemCount:
                controller.notifications.length ?? 0)
                : Widgets.noRecordsFound(title: "No Orders Found"),
            if (controller.isOrdersMoreLoading.value)
              Center(child: CircularProgressIndicator()),

            Widgets.heightSpaceH2, Widgets.heightSpaceH2, Widgets.heightSpaceH2,
          ],
        ),
      );
    });
  }
}
