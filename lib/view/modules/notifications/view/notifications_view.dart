import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../controller/user_controller.dart';
import '../../../../core/widgets/shimmer_effect.dart';

class NotificationView extends StatelessWidget {
UserController controller = Get.find();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Notifications",
            size: 17, fontWeight: FontWeight.w700),
        actions: [],
      ),
      body: Obx(() => controller.isNotificationLoading.value
          ? const ShimmerListSkeleton()
          : ListView.separated(padding: EdgeInsets.all(15),
          physics: BouncingScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (context, index) {
            return Widgets.notificationCard();
          },
          separatorBuilder: (context, index) {
            return Widgets.heightSpaceH1;
          },
          itemCount: controller.notifications.length)),
      
    );
  }


}
