import 'package:ensuram/view/modules/contributions/model/invite_org_model.dart';
import 'package:ensuram/view/modules/expense/model/expense_model.dart';
import 'package:ensuram/view/modules/pharmacy/pharmacy_order_model.dart';
import 'package:get/get.dart';
import 'package:ensuram/core/services/http_service.dart';

import '../../../../core/constants/api_endpoints.dart';
import '../../../../core/widgets/widgets.dart';
import '../../laboratory/model/lab_book_model.dart';
import '../model/organization_model.dart';

class ContributionsController extends GetxController {
  RxList organizations = <Organization>[].obs;
  RxBool isLoading = false.obs;
  RxBool isMoreLoading = false.obs;
  RxInt totalCount = 0.obs;
  RxInt currentPage = 0.obs;
  RxList invites = <InviteOrg>[].obs;
  RxBool isInvitesLoading = false.obs;
RxString selectedOrgId = "".obs;
  RxList orders = <OrganizationBill>[].obs;
  RxBool isOrdersLoading = false.obs;
  RxBool isOrdersMoreLoading = false.obs;
  RxInt totalOrdersCount = 0.obs;
  RxInt currentOrdersPage = 0.obs;
  RxInt selectedIndex = 0.obs;

  // Add these properties for date filtering
  RxString startDate = ''.obs;
  RxString endDate = ''.obs;

  void selectItem(int index) {
    selectedIndex.value = index;
  }

  fetchOrders({int page = 1}) async {
    try {
      if (isOrdersLoading.value) return;
      if (page == 1) {
        isOrdersLoading.value = true;
      } else {
        isOrdersMoreLoading.value = true;
      }

      var response = await ApiService.getData(
          "${Endpoints.getUserOrganization}?page=$page&limit=15&startDate=${startDate.value}&endDate=${endDate.value}&org_id=${selectedOrgId.value}");
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          orders.clear();
          totalOrdersCount.value = 0;
          currentOrdersPage.value = 1;
        }
        orders.addAll(
          (response.data['data'] as List)
              .map((e) => OrganizationBill.fromJson(e))
              .toList(),
        );

        totalOrdersCount.value =
            int.parse(response.data['pagination']['total_records'].toString());
      }
    } catch (e) {
      print(e);
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    } finally {
      isOrdersLoading.value = false;
      isOrdersMoreLoading.value = false;
    }
  }
  fetchOrgInvites() async {
    try {

isInvitesLoading.value = true;
      var response = await ApiService.getData(
          Endpoints.getOrgInvites);
    isInvitesLoading.value = false;
      if (response.status == true) {

         invites.clear();

        invites.addAll(
          (response.data['data'] as List)
              .map((e) => InviteOrg.fromJson(e))
              .toList(),
        );

      }
    } catch (e) {
      print(e);
      isInvitesLoading.value = false;
    } finally {
      isInvitesLoading.value = false;
    }
  }
  actionOnInvite(String action,String inviteId) async {
    try {

      Widgets.showLoader("Loading");
      var response = await ApiService.postData(
          Endpoints.actionOrganization,{
            "status":action,
            "id":inviteId

      });
      Widgets.hideLoader();
      if (response.status == true) {

        fetchOrgInvites();fetchOrganization(page: 1);

      }
    } catch (e) {
      print(e);
      Widgets.hideLoader();
    } finally {
      Widgets.hideLoader();
    }
  }

  fetchOrganization({int page = 1}) async {
    try {
      if (isLoading.value) return;
      if (page == 1) {
        isLoading.value = true;
      } else {
        isMoreLoading.value = true;
      }

      var response = await ApiService.getData(
          "${Endpoints.getUserOrganizations}?page=$page&limit=15");
      isLoading.value = false;
      isMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          organizations.clear();
          totalCount.value = 0;
          currentPage.value = 1;
        }
        organizations.addAll(
          (response.data['data'] as List)
              .map((e) => Organization.fromJson(e))
              .toList(),
        );

        totalCount.value =
            int.parse(response.data['pagination']['total_records'].toString());
      }
    } catch (e) {
      print(e);
      isLoading.value = false;
      isMoreLoading.value = false;
    } finally {
      isLoading.value = false;
      isMoreLoading.value = false;
    }
  }
}
