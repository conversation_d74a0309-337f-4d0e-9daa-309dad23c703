class InviteOrg {
  String? id;
  String? orgId;
  String? userId;
  String? status;
  String? companyName;

  InviteOrg({this.id, this.orgId, this.userId, this.status, this.companyName});

  InviteOrg.fromJson(Map<String, dynamic> json) {
    id = json['id'].toString();
    orgId = json['org_id'].toString();
    userId = json['user_id'].toString();
    status = json['status'];
    companyName = json['company_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['org_id'] = this.orgId;
    data['user_id'] = this.userId;
    data['status'] = this.status;
    data['company_name'] = this.companyName;
    return data;
  }
}
class OrganizationBill {
  String? amount;
  String? dateDone;

  OrganizationBill({this.amount, this.dateDone});

  OrganizationBill.fromJson(Map<String, dynamic> json) {
    amount = json['amount'];
    dateDone = json['date_done'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['amount'] = this.amount;
    data['date_done'] = this.dateDone;
    return data;
  }
}
