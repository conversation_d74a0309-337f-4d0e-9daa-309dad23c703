class Organization {
  String? companyName;
  String? orgId;
  String? totalAmount;

  Organization({this.companyName, this.orgId, this.totalAmount});

  Organization.fromJson(Map<String, dynamic> json) {
    companyName = json['company_name'];
    orgId = json['org_id'].toString();
    totalAmount = json['total_amount'].toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['company_name'] = this.companyName;
    data['org_id'] = this.orgId;
    data['total_amount'] = this.totalAmount;
    return data;
  }
}
