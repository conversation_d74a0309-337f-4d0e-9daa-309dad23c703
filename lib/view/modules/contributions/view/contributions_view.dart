import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/view/modules/contributions/view/organization_detail_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../core/constants/constants_list.dart';
import '../../../../core/routes/app_routes.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/shimmer_effect.dart';
import '../controller/contributions_controller.dart';
import '../model/invite_org_model.dart';
import '../model/organization_model.dart';

class ContributionsView extends StatefulWidget {
  const ContributionsView({super.key});

  @override
  State<ContributionsView> createState() => _ContributionsViewState();
}

class _ContributionsViewState extends State<ContributionsView> {


  final ContributionsController controller = Get.put(ContributionsController());
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
controller.fetchOrgInvites();controller.fetchOrganization(page: 1);
    scrollController.addListener(scrollListener);
  }

  scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent &&
        controller.totalCount.value >
            controller.organizations.length) {
      controller.fetchOrganization(
          page: controller.currentPage.value + 1);
      controller
          .currentPage.value++; // Increment the page counter
    }
  }



  Widget widgetOrdersCard() {
    return Obx(() {
      return SingleChildScrollView(controller: scrollController,padding: EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            controller.isInvitesLoading.value
                ? const ShimmerListSkeleton()
                : controller.invites.isNotEmpty
                ? ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemBuilder: (context, index) {InviteOrg invite = controller.invites[index];
                  return  Container(
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(border: Border.all(color:Colors.cyan.shade100 ),
                        color: Colors.cyan.shade50,
                        borderRadius: BorderRadius.circular(10)),
                    child: Column(
                      children: [
                        Texts.textBlock(invite.companyName??"",
                            size: 15, fontWeight: FontWeight.w500),
                        Widgets.heightSpaceH1,
                        Texts.textNormal(
                          "${invite.companyName} wants to add you to their Health contribution list. If you accept, they will appear in your health contribution records, and your personal information (Ensuram ID, First name, Last name, Email) will be shared with them. You will then start receiving their contributions for your health expenses within the Ensuram platform.",
                          size: 12,
                        ), Widgets.heightSpaceH2,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                           InkWell(onTap: (){

                             controller.actionOnInvite("accepted",invite.id ?? "");
                           },
                              child: Container(
                                padding:
                                EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                                decoration: BoxDecoration(
                                    color: Colors.green,
                                    borderRadius: BorderRadius.circular(5)),
                                child: Texts.textBlock("Accept ",
                                    fontWeight: FontWeight.w800,
                                    color: Colors.white,
                                    size: 12),
                              ),
                            ),
                            SizedBox(
                              width: 15,
                            ),
                            InkWell(onTap: (){

                              controller.actionOnInvite("declined",invite.id ?? "");
                            },
                              child: Container(
                                padding:
                                EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                                decoration: BoxDecoration(
                                    color: Colors.red,
                                    borderRadius: BorderRadius.circular(5)),
                                child: Texts.textBlock("Decline",
                                    fontWeight: FontWeight.w800,
                                    color: Colors.white,
                                    size: 12),
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  );
                },
                separatorBuilder: (context, index) {
                  return Widgets.heightSpaceH1;
                },
                itemCount:
                controller.invites.length ?? 0)
                : SizedBox.shrink(),
              Widgets.heightSpaceH2,


            controller.isLoading.value
                ? const ShimmerListSkeleton()
                : controller.organizations.isNotEmpty
                ? ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemBuilder: (context, index) {Organization order = controller.organizations[index];
                  return Container(

                    padding: const EdgeInsets.all(12.0),
                    decoration: Widgets.blockDecoration,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 2,
                          height: 50,
                          decoration: BoxDecoration(
                            color: Colors.green,
                            borderRadius: BorderRadius.circular(2.0),
                          ),
                        ),
                        const SizedBox(width: 8.0),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Texts.textBlock(
                                  order.companyName??"",
                                  fontWeight: FontWeight.w500,size:14,color: ColorConstants.secondaryColor

                              ),
                              const SizedBox(height: 4.0),
                              Text(
                                '\$${order.totalAmount}',
                                style: TextStyle(
                                  fontSize: 16.0,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),

                            ],
                          ),
                        ),
                        const SizedBox(width: 8.0),
                        Column(crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            const Icon(
                              Icons.calendar_today,
                              color: Colors.grey,
                              size: 24.0,
                            ),Widgets.heightSpaceH2,
                            GestureDetector(onTap: (){
                      controller.selectedOrgId.value = order.orgId!;
                              Get.to(()=>OrganizationDetailView());
                            },
                              child: Container(
                                padding:
                                EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                                decoration: BoxDecoration(
                                    color: ColorConstants.primaryColor,
                                    borderRadius: BorderRadius.circular(5)),
                                child: Texts.textBlock("See Details",
                                    fontWeight: FontWeight.w800,
                                    color: Colors.white,
                                    size: 10),
                              ),
                            )
                          ],
                        ),
                      ],
                    ),
                  );
                },
                separatorBuilder: (context, index) {
                  return Widgets.heightSpaceH1;
                },
                itemCount:
                controller.organizations.length ?? 0)
                : Widgets.noRecordsFound(title: "You have no contributions"),
            if (controller.isLoading.value)
              Center(child: CircularProgressIndicator()),

            Widgets.heightSpaceH2, Widgets.heightSpaceH2, Widgets.heightSpaceH2,
          ],
        ),
      );
    });
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Contributions",
            size: 20, fontWeight: FontWeight.w700),
        actions: [],
      ),
      body: widgetOrdersCard(),
    );
  }

  void payBill(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.white,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          maxChildSize: 0.8,
          minChildSize: 0.3,
          initialChildSize: 0.5,
          builder: (context, scrollController) {
            return Padding(
              padding: const EdgeInsets.all(15.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    'Pay Insurance Bill',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  Widgets.heightSpaceH1,
                  Widgets.divider(),
                  Widgets.heightSpaceH2,
                  RichText(
                      text: TextSpan(
                          text: 'Make sure to ',
                          style: TextStyle(color: Colors.black45, fontSize: 12),
                          children: [
                            TextSpan(
                              text: 'fill up the wallet ',
                              style: TextStyle(
                                color: Colors.blue,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  Get.toNamed(AppRoutes.userAddWallet);
                                },
                            ),
                            TextSpan(
                                text: ', before initiating this transaction.',
                                style: TextStyle(
                                  color: Colors.black45,
                                  fontSize: 12,
                                )),
                          ])),
                  Widgets.heightSpaceH2,
                  Widgets.columTexts(title: 'Billing No.', value: 'YOU32323'),
                  Widgets.heightSpaceH2,

                  Widgets.columTexts(title: 'Payment Amount', value: '\$34.44'),
                  Widgets.heightSpaceH2,

                  Widgets.columTexts(
                      title: 'Payment Method', value: 'Ensuram  E-Wallet'),
                  Widgets.heightSpaceH3,
                  Widgets.divider(),
                  Widgets.heightSpaceH2,
                  CustomButton(
                    label: "PAY BILL",
                    color: ColorConstants.secondaryColor,
                    onTap: () {},
                    textStyle: TextStyle(
                        fontSize: 12,
                        color: Colors.white,
                        fontWeight: FontWeight.w600),
                  )
                ],
              ),
            );
          },
        );
      },
    );
  }
}
