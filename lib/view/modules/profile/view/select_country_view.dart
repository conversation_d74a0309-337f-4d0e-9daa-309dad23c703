import 'package:ensuram/model/country_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../controller/location_controller.dart';
import '../../../../core/constants/assets_constants.dart';
import '../../../../core/constants/color_constants.dart';
import '../../../../core/constants/padding_constants.dart';
import '../../../../core/widgets/entry_field.dart';
import '../../../../core/widgets/shimmer_effect.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../../../../core/widgets/widgets.dart';

class SelectCountryView extends StatefulWidget {
  const SelectCountryView({super.key});

  @override
  State<SelectCountryView> createState() => _SelectCountryViewState();
}

class _SelectCountryViewState extends State<SelectCountryView> {
  late LocationController homeController;

  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    homeController = Get.put(LocationController());
homeController.fetchCountries("",page: 1);
    scrollController.addListener(scrollListener);
  }

  scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent &&
        homeController.totalCountries.value > homeController.countries.length) {
      homeController.fetchCountries(homeController.searchFieldController.text,
          page: homeController.currentCountriesPage.value + 1);
      homeController.currentCountriesPage.value++; // Increment the page counter
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () => Get.back(),
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock(
            "Select Country",
            size: 17,
            fontWeight: FontWeight.w700
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(15.0),
            child: EntrySearchField(
              prefixIcon: Assets.searchIcon,
              onChange: (v) {
                homeController.fetchCountries(v,page: 1);
              },
              hint: "Search Country",
            ),
          ),

         buildListWidget(),
        ],
      ),
    );
  }


  Widget buildListWidget() {
    return Obx(
          () {
        return Expanded(
          child: ListView(
            shrinkWrap: true,
            padding: const EdgeInsets.symmetric(horizontal: 15,vertical: 0),
            controller: scrollController,
            children: [
              homeController.isCountriesLoading.value
                  ? const ShimmerListSkeleton()
                  : homeController.countries.isNotEmpty
                  ? ListView.separated(
                  physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    Country country = homeController.countries[index];
                    return Container(

                      child: ListTile(
                        contentPadding: EdgeInsets.zero,
                        onTap: () {
                          Get.back(result: {
                            'country': country.name,
                          });
                        },
                        leading: Icon(
                          Icons.location_pin,
                          color: ColorConstants.primaryColor,
                        ),
                        minLeadingWidth: 20,
                        title: Text(country.name ?? ""),

                      ),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return Widgets.divider();
                  },
                  itemCount:  homeController.countries.length ?? 0)
                  : Widgets.noRecordsFound(title: "No Country"),
              if (homeController.isCountriesMoreLoading.value) Widgets.widgetLoader(),
            ],
          ),
        );
      },
    );
  }
}
