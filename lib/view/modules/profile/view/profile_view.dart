import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/routes/app_routes.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../controller/user_controller.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../controller/profile_controller.dart';

class ProfileView extends StatelessWidget {
   ProfileView({super.key});
 ProfileController controller = Get.put(ProfileController());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                GetBuilder<ProfileController>(
                  builder: (profileController) {
                    return AdvancedAvatar(
                      animated: true,
                      size: 110,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: ColorConstants.secondaryColor,
                        border: Border.all(
                          color: Colors.grey,
                          width: 0.0,
                        ),
                      ),
                      child:Get.find<UserController>().userModel?.userPhoto != null
                          ? Widgets.networkImage(
                          "${Endpoints.domain}/customer/${Get.find<UserController>().userModel?.userId}/profile/${Get.find<UserController>().userModel?.userPhoto}",
                          width: 150,
                          height: 150)
                          : Icon(
                        Icons.person,
                        size: 65,
                        color: Colors.white,
                      ),
                    );
                  },
                ),

              ],
            ),
            Widgets.heightSpaceH3,
            Widgets.profileMenu(
              text: "Edit Profile",
              icon: Icons.edit,
              isBadge: false,
              press: () => Get.toNamed(AppRoutes.userEditProfile),
            ),
            Widgets.profileMenu(
              text: "Edit Location",
              icon: Icons.edit_location_alt_outlined,
              isBadge: false,
              press: () => Get.toNamed(AppRoutes.userEditLocation),
            ),
            Widgets.profileMenu(
              text: "Change Password",
              icon: Icons.lock_outline,
              isBadge: false,
              press: () => Get.toNamed(AppRoutes.changePassword),
            ),
            Widgets.profileMenu(
              text: "Log Out",
              isBadge: false,
              icon: Icons.logout,
              press: () {Get.find<UserController>().logout();

              },
            ),
            Widgets.heightSpaceH2,
            CustomButton(
              label: "Delete Account",
              onTap: () {},
              color: Colors.red,
              icon: Icon(
                Icons.delete_outline,
                color: Colors.white,
                size: 18,
              ),
            )
          ],
        ),
      ),
    );
  }
}
