import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:ensuram/view/modules/profile/view/select_city_view.dart';
import 'package:ensuram/view/modules/profile/view/select_country_view.dart';
import 'package:ensuram/view/modules/profile/view/select_state_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../controller/user_controller.dart';
import '../../../../model/country_model.dart';
import '../controller/profile_controller.dart';

class EditLocationView extends StatefulWidget {
  const EditLocationView({super.key});

  @override
  State<EditLocationView> createState() => _EditLocationViewState();
}

class _EditLocationViewState extends State<EditLocationView> {
  var selectedCountry = "".obs;
  var selectedCity = "".obs;
  var selectedState = "".obs;

  final UserController userController = Get.find<UserController>();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController zipCodeController = TextEditingController();
  ProfileController controller = Get.find();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    addressController.text = userController.userModel?.userAddress ?? '';
    zipCodeController.text = userController.userModel?.userZipcode ?? '';
    selectedCountry.value = userController.userModel?.userCountry ?? '';
    selectedCity.value = userController.userModel?.userCity ?? '';
    selectedState.value = userController.userModel?.userRegion ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () => Get.back(),
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Edit Location",
            size: 17, fontWeight: FontWeight.w700),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [
            Obx(() => CustomDropdown(
                  value: selectedCountry.value == ""
                      ? null
                      : selectedCountry.value,
                  label: "Country",
                  textColor: selectedCountry.value == ""
                      ? Colors.black45
                      : Colors.black,
                  onTap: () {
                    Get.to(() => SelectCountryView())?.then((result) {
                      if (result != null) {
                        selectedCountry.value = result['country'];
                      }
                    });
                  },
                  hint: 'Select here',
                )),
            Widgets.heightSpaceH2,
            EntryField(
              controller: addressController,
              label: "Address",
              hint: "Write here",
              borderRadius: 10,
            ),
            Obx(() => CustomDropdown(
                  textColor:
                      selectedState.value == "" ? Colors.black45 : Colors.black,
                  value: selectedState.value == "" ? null : selectedState.value,
                  label: "State",
                  onTap: () {
                    if (selectedCountry.value == "") {
                      Widgets.showSnackBar("Error", "Please select a country");
                      return;
                    }
                    Get.to(() =>
                            SelectStateView(country: selectedCountry.value))
                        ?.then((result) {
                      if (result != null) {
                        selectedState.value = result['state'];
                      }
                    });
                  },
                  hint: 'Select here',
                )),
            Widgets.heightSpaceH2,
            Obx(() => CustomDropdown(
                  textColor:
                      selectedCity.value == "" ? Colors.black45 : Colors.black,
                  value: selectedCity.value == "" ? null : selectedCity.value,
                  label: "City",
                  onTap: () {
                    if (selectedState.value == "") {
                      Widgets.showSnackBar("Error", "Please select a state");
                      return;
                    }
                    Get.to(() => SelectCityView(
                          state: selectedState.value,
                        ))?.then((result) {
                      if (result != null) {
                        selectedCity.value = result['city'];
                      }
                    });
                  },
                  hint: 'Select here',
                )),
            Widgets.heightSpaceH2,
            EntryField(
              label: "Zip Code",
              controller: zipCodeController,
              hint: "e.g 2323232323",
              borderRadius: 10,
            ),
            Widgets.heightSpaceH2,
            CustomButton(
              onTap: () {
                if (selectedCountry.value == "") {
                  Widgets.showSnackBar("Error", "Please select a country");
                  return;
                }
                if (addressController.text.isEmpty) {
                  Widgets.showSnackBar("Error", "Please enter your address");
                  return;
                }
                if (selectedState.value == "") {
                  Widgets.showSnackBar("Error", "Please select a state");
                  return;
                }if (selectedCity.value == "") {
                  Widgets.showSnackBar("Error", "Please select a city");
                  return;
                }if (zipCodeController.text.isEmpty) {
                  Widgets.showSnackBar("Error", "Please enter your zip code");
                  return;
                }
                var payload = {
                  "user_country": selectedCountry.value,
                  "user_address": addressController.text,
                  "user_region": selectedState.value,
                  "user_city": selectedCity.value,
                  "user_zipcode": zipCodeController.text
                };
                controller.updateLocation(payload);
              },
              label: "SAVE CHANGES",
            )
          ],
        ),
      ),
    );
  }
}
