import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../controller/profile_controller.dart';

class ChangePasswordView extends StatelessWidget {
   ChangePasswordView({super.key});

   TextEditingController currentPasswordController = TextEditingController();
   TextEditingController newPasswordController = TextEditingController();
   TextEditingController retypePasswordController = TextEditingController();
ProfileController controller = Get.find();
  @override
  Widget build(BuildContext context) {
    return Scaffold( backgroundColor:ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock(
            "Change Password",
            size: 17,
            fontWeight: FontWeight.w700),actions: [],

      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [

          EntryField(controller: currentPasswordController,label:"Current Password",hint:"Enter here",borderRadius: 10,),
           EntryField(controller: newPasswordController,label:"New Password",hint:"Enter here",borderRadius: 10,),
            EntryField(controller: retypePasswordController,label:"Re-type Password",hint:"Enter here",borderRadius: 10,),

            Widgets.heightSpaceH2,CustomButton(onTap:(){


              if (currentPasswordController.text.isEmpty) {
                Widgets.showSnackBar("Error", "Please enter your address");
                return;
              }
            if (newPasswordController.text.isEmpty) {
                Widgets.showSnackBar("Error", "Please enter your address");
                return;
              }if (retypePasswordController.text!=newPasswordController.text) {
                Widgets.showSnackBar("Error", "Password does not match");
                return;
              }
              var payload = {

                  "current_password": currentPasswordController.text,
                  "new_password": newPasswordController.text,

              };
              controller.updatePassword(payload);

            },label:"SAVE CHANGES",)
          ],
        ),
      ),
    );
  }
}
