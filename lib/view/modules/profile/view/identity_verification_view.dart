import 'dart:convert';
import 'dart:io';

import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:ensuram/view/modules/profile/view/select_country_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:smile_id/smile_id.dart';
import 'package:smile_id/smile_id_document_verification.dart';
import 'package:smile_id/smile_id_smart_selfie_capture_view.dart';
import 'package:smile_id/smileid_messages.g.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../controller/user_controller.dart';
import '../../../../model/country_model.dart';
import '../controller/profile_controller.dart';

class IdentityVerificationView extends StatefulWidget {
  const IdentityVerificationView({super.key});

  @override
  State<IdentityVerificationView> createState() => _IdentityVerificationViewState();
}

class _IdentityVerificationViewState extends State<IdentityVerificationView> {
  Rx<File?> selectedDocument = Rx<File?>(null);
  RxString documentName = ''.obs;

  RxString selectedIdType = ''.obs;
  final idTypes = [
    'Voters Card',
    'National ID',
    'Passport',
    'Drivers License'
  ];

  Map<String, String> idTypeToSmileIDType = {
    'Voters Card': 'VOTERS_ID',
    'National ID': 'NATIONAL_ID',
    'Passport': 'PASSPORT',
    'Drivers License': 'DRIVERS_LICENSE'
  };

  var selectedCountry ="".obs;
  var selectedCity = Rx<City?>(null).obs;
  var selectedState = Rx<City?>(null).obs;

  final UserController userController = Get.find<UserController>();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController zipCodeController = TextEditingController();
  final TextEditingController salutationController = TextEditingController();
  final Rx<DateTime?> selectedDate = Rx<DateTime?>(null);
  ProfileController controller = Get.put(ProfileController());
  RxString selectedSalutation = ''.obs;
  RxString selectedDOB = ''.obs;

  Widget selfieCaptureButton(BuildContext context) {
    return ElevatedButton(
      child: const Text("Selfie Capture"),
      onPressed: () {
        Navigator.of(context).push(
          MaterialPageRoute<void>(
            builder: (BuildContext context) => Scaffold(
                body: SmileIDSmartSelfieCaptureView(
                  showConfirmationDialog: false,
                  showInstructions: true,
                  showAttribution: false,
                  allowAgentMode: false,
                  useStrictMode: true,
                  onSuccess: (String? result) {
                    // Your success handling logic
                    Map<String, dynamic> jsonResult = json.decode(result ?? '{}');
                    String formattedResult = jsonEncode(jsonResult);
                    final snackBar = SnackBar(content: Text("Success: $formattedResult"));
                    ScaffoldMessenger.of(context).showSnackBar(snackBar);
                    Navigator.of(context).pop();
                  },
                  onError: (String errorMessage) {
                    // Your error handling logic
                    final snackBar = SnackBar(content: Text("Error: $errorMessage"));
                    ScaffoldMessenger.of(context).showSnackBar(snackBar);
                    Navigator.of(context).pop();
                  },
                )),
          ),
        );
      },
    );
  }
  Future<void> initPlatformState() async {
    if (!mounted) return;

    try {
      String partnerId = '7221';
      String authToken = "Mq+D6qftDjJj8dqsj+87bo1WBjaEo8OLozHCxZ2TihwwKByQYoSLO6oYEQQ+FvBBn+GEbIhleO+8hWVozHPQ+/ZirzwJwYIwvC7K/s7hS1eY+DYhpjZQ4YI5Zth+l+xkh9qlglVj/q5Y57UK4Ti2TT5ZkulsidAV4TvX85ahYYw=";
      String prodBaseUrl = 'https://api.smileidentity.com/v1/';
      String sandboxBaseUrl = 'https://testapi.smileidentity.com/v1/';
      SmileID.initializeWithConfig(
          useSandbox: true,
          config: FlutterConfig(
              partnerId: partnerId,
              authToken: authToken,
              prodBaseUrl: prodBaseUrl,
              sandboxBaseUrl: sandboxBaseUrl
          ),
          enableCrashReporting: false
      );
    } catch (e) {
      print('Error initializing SmileID: $e');
    }
  }
  @override
  void initState() {
    // TODO: implement initState
    super.initState(); initPlatformState();
    addressController.text = userController.userModel?.userAddress ?? '';
    zipCodeController.text = userController.userModel?.userZipcode ?? '';
    selectedDOB.value = userController.userModel?.userCreated ?? '';
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () => Get.back(),
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Identity Verification",
            size: 17, fontWeight: FontWeight.w700),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [
            Obx(() => CustomDropdown(
              value: selectedCountry.value == ""
                  ? null
                  : selectedCountry.value,
              label: "Country",
              textColor: selectedCountry.value == ""
                  ? Colors.black45
                  : Colors.black,
              onTap: () {
                Get.to(() => SelectCountryView())?.then((result) {
                  if (result != null) {
                    selectedCountry.value = result['country'];
                  }
                });
              },
              hint: 'Select here',
            )),
            Widgets.heightSpaceH2,

            Obx(() => CustomDropdown(textColor: selectedIdType.value.isEmpty ? Colors.black45 : Colors.black,
              value: selectedIdType.value.isEmpty ? null : selectedIdType.value,
              label: "ID Type",
              onTap: () {
                Get.bottomSheet(
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Texts.textBlock("Select ID Type", size: 16),
                            IconButton(
                              onPressed: () => Get.back(),
                              icon: Icon(Icons.close),
                            ),
                          ],
                        ),
                        Divider(),
                        ...idTypes.map((type) => ListTile(
                          title: Text(type),
                          onTap: () {
                            selectedIdType.value = type;
                            Get.back();
                          },
                        )).toList(),
                      ],
                    ),
                  ),
                  backgroundColor: Colors.transparent,
                );
              },
              hint: 'Select here',
            )),

            Widgets.heightSpaceH2,
            EntryField(
              label: "ID Number",
              hint: "e.g 2323232323",
              borderRadius: 10,
            ),
            Widgets.heightSpaceH2,

            CustomButton(
              onTap: () {
                if (selectedCountry.value == "") {
                  Widgets.showSnackBar("Error", "Please select a country");
                  return;
                }
                if (selectedIdType.value.isEmpty) {
                  Widgets.showSnackBar("Error", "Please select an ID type");
                  return;
                }


              },
              label: "START VERIFICATION",
            )
          ],
        ),
      ),
    );
  }
}
