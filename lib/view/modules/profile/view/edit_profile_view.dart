import 'package:ensuram/core/constants/assets_constants.dart';
import 'package:ensuram/core/constants/color_constants.dart';
import 'package:ensuram/core/widgets/custom_button.dart';
import 'package:ensuram/core/widgets/custom_dropdown.dart';
import 'package:ensuram/core/widgets/entry_field.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../controller/user_controller.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../../../../core/utils/date_utils.dart';
import '../controller/profile_controller.dart';

class EditProfileView extends StatefulWidget {
  const EditProfileView({super.key});

  @override
  State<EditProfileView> createState() => _EditProfileViewState();
}

class _EditProfileViewState extends State<EditProfileView> {

  final UserController userController = Get.find<UserController>();
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController salutationController = TextEditingController();
  final Rx<DateTime?> selectedDate = Rx<DateTime?>(null);
  ProfileController controller = Get.find();
  RxString selectedSalutation = ''.obs;
  RxString selectedDOB = ''.obs;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();controller.selectedImage.value = null;
    firstNameController.text = userController.userModel?.userFirstname ?? '';
    lastNameController.text = userController.userModel?.userLastname ?? '';
    selectedSalutation.value = userController.userModel?.userSalutation ?? '';
    selectedDOB.value = userController.userModel?.userDOB ?? '';

  }
  @override
  Widget build(BuildContext context) {

    return Scaffold(
      backgroundColor: ColorConstants.halfWhite,
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.primaryBlackColor,
          ),
        ),
        backgroundColor: ColorConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Texts.textBlock("Edit Profile",
            size: 17, fontWeight: FontWeight.w700),
        actions: [],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                GetBuilder<ProfileController>(
                  builder: (profileController) {
                    return AdvancedAvatar(
                      animated: true,
                      size: 110,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: ColorConstants.secondaryColor,
                        border: Border.all(
                          color: Colors.grey,
                          width: 0.0,
                        ),
                      ),
                      child: profileController.selectedImage.value != null
                          ? Image.file(
                            profileController.selectedImage.value!,
                            width: 150,
                            height: 150,
                            fit: BoxFit.cover,
                          )
                          : userController.userModel?.userPhoto != null
                              ? Widgets.networkImage(
                                 "${Endpoints.domain}/customer/${userController.userModel?.userId}/profile/${userController.userModel?.userPhoto}",
                                  width: 150,
                                  height: 150)
                              : Icon(
                                  Icons.person,
                                  size: 65,
                                  color: Colors.white,
                                ),
                    );
                  },
                ),
                Positioned(
                  right: 4,
                  bottom: 4,
                  child: InkWell(
                    onTap: () {
                      controller.showImageSourceDialog(context);
                    },
                    child: CircleAvatar(
                      radius: 13,
                      backgroundColor: ColorConstants.backgroundColor,
                      child: Icon(
                        Icons.camera_alt,
                        color: ColorConstants.secondaryGreyColor,
                        size: 15,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            Widgets.heightSpaceH3,
            EntryField(
              controller: firstNameController,
              label: "First Name",
              hint: "e,g John",
              borderRadius: 10,
            ),
            EntryField(
              controller: lastNameController,
              label: "Last Name",
              hint: "e.g Win",
              borderRadius: 10,
            ),
            Obx(() => CustomDropdown(
              value: selectedSalutation.value == ""
                  ? null
                  : selectedSalutation.value,
              label: "Salutation",
              textColor: selectedSalutation.value == ""
                  ? Colors.black45
                  : Colors.black,
              hint: "Select here",
              onTap: () {
                Get.bottomSheet(
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius:
                      BorderRadius.vertical(top: Radius.circular(20)),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Texts.textBlock("Select Salutation", size: 16),
                            IconButton(
                              onPressed: () => Get.back(),
                              icon: Icon(Icons.close),
                            ),
                          ],
                        ),
                        Divider(),
                        ListTile(
                          title: Text('Mr.'),
                          onTap: () {
                            selectedSalutation.value = 'Mr.';
                            Get.back();
                          },
                        ),
                        ListTile(
                          title: Text('Mrs.'),
                          onTap: () {
                            selectedSalutation.value = 'Mrs.';
                            Get.back();
                          },
                        ),
                        ListTile(
                          title: Text('Ms.'),
                          onTap: () {
                            selectedSalutation.value = 'Ms.';
                            Get.back();
                          },
                        ),
                      ],
                    ),
                  ),
                  backgroundColor: Colors.transparent,
                );
              },
            )),
            Widgets.heightSpaceH2,
            Obx(
                  () => CustomDropdown(
                value: selectedDOB.value == "" ? null : selectedDOB.value,
                label: "Date of Birth",
                onTap: () async {
                  final DateTime? picked = await showDatePicker(
                    context: context,
                    initialDate: selectedDate.value ?? DateTime.now(),
                    firstDate: DateTime(1900),
                    lastDate: DateTime.now(),
                    builder: (context, child) {
                      return Theme(
                        data: Theme.of(context).copyWith(
                          colorScheme: ColorScheme.light(
                            primary: ColorConstants.primaryColor,
                          ),
                        ),
                        child: child!,
                      );
                    },
                  );
                  if (picked != null) {
                    selectedDate.value = picked;
                    selectedDOB.value = Utils.formatDate(picked);
                  }
                },
                hint: 'yyyy-mm-dd',
                textColor: selectedDOB.value == "" ? Colors.black45 : Colors.black,
              ),
            ),
            Widgets.heightSpaceH2,
            Widgets.heightSpaceH2,
            CustomButton(
              onTap: () {
                var payload = {
                  "user_firstname": firstNameController.text,
                  "user_lastname": lastNameController.text,
                  "user_salutation": selectedSalutation.value,
                  "user_birthdate": selectedDOB.value,
                };
                controller.updateProfile(payload);
              },
              label: "SAVE CHANGES",
            )
          ],
        ),
      ),
    );
  }
}
