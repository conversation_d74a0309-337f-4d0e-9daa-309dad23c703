import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;
import '../../../../controller/user_controller.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../../../../core/constants/color_constants.dart';
import '../../../../core/services/http_service.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../../../../core/widgets/widgets.dart';
import '../../../../model/country_model.dart';

class ProfileController extends GetxController {
  Rx<File?> selectedImage = Rx<File?>(null);
  RxString imageUrl = ''.obs;
  var selectedCountry = Country().obs;
  var selectedCity = City().obs;
  var selectedState = City().obs;

  updateLocation(var payload) async {
    try {
      Widgets.showLoader("Loading.. ");

      var response =
          await ApiService.putData(Endpoints.updateLocation, payload);

      Widgets.hideLoader();

      if (response.status == true) {
        Widgets.showSnackBar("Success", response.message ?? "");
        Get.find<UserController>().fetchUserDetails();
      } else {
        Widgets.showSnackBar("Error", response.message ?? "");
      }
    } catch (e) {
      Widgets.hideLoader();
    } finally {
      Widgets.hideLoader();
    }
  }

  updatePassword(var payload) async {
    try {
      Widgets.showLoader("Loading.. ");

      var response =
          await ApiService.putData(Endpoints.updatePassword, payload);

      Widgets.hideLoader();

      if (response.status == true) {
        Widgets.showSnackBar("Success", response.message ?? "");
      } else {
        Widgets.showSnackBar("Error", response.message ?? "");
      }
    } catch (e) {
      Widgets.hideLoader();
    } finally {
      Widgets.hideLoader();
    }
  }

  updateProfile(var payload) async {
    Widgets.showLoader("Loading");
    var request = http.MultipartRequest(
        'POST', Uri.parse('${Endpoints.baseURL}${Endpoints.updateProfile}'));

    if (selectedImage.value != null) {
      var pic = await http.MultipartFile.fromPath(
          'user_photo', selectedImage.value?.path ?? "");
      request.files.add(pic);
    }

    request.headers['Authorization'] =
        'Bearer ${Get.find<UserController>().token ?? ""}';
    request.fields['user_firstname'] = payload['user_firstname'];
    request.fields['user_lastname'] = payload['user_lastname'];
    request.fields['user_salutation'] = payload['user_salutation'];
    request.fields['user_birthdate'] = payload['user_birthdate'];
    var response = await request.send();

    Widgets.hideLoader();
    var data = await response.stream.bytesToString();
    var decodedData = jsonDecode(data);print(decodedData);
    if (response.statusCode == 200) {


      if (decodedData['status'] == 'success') {
        Widgets.showSnackBar("Success", decodedData['message'] ?? "");
        Get.find<UserController>().fetchUserDetails();
      } else {
        Widgets.showSnackBar("Error", "Something went wrong");
      }
    } else {
     Widgets.showSnackBar("Error",decodedData['message'] ?? "");
    }
  }

  pickImage(ImageSource source, {bool isProfile = false}) async {
    try {
      final XFile? file = await ImagePicker().pickImage(source: source);
      if (file != null) {
        // Crop the image
        final croppedFile = await ImageCropper().cropImage(
          sourcePath: file.path,
          aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
          compressQuality: 70,
          uiSettings: [
            AndroidUiSettings(
              toolbarTitle: 'Crop Image',
              toolbarColor: ColorConstants.primaryColor,
              toolbarWidgetColor: Colors.white,
              initAspectRatio: CropAspectRatioPreset.square,
              lockAspectRatio: true,
            ),
          ],
        );

        if (croppedFile != null) {
          selectedImage.value = File(croppedFile.path);
          update(); // Trigger UI update
        }
      }
    } catch (e) {
      print('Error picking image: $e');
      Widgets.showSnackBar("Error", "Failed to pick image");
    }
  }

  showImageSourceDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Texts.textBlock("Select Image Source",
                      align: TextAlign.center),
                  GestureDetector(
                    onTap: () => Get.back(),
                    child: const Icon(Icons.clear, color: Colors.black54),
                  ),
                ],
              ),
              Widgets.heightSpaceH2,
              ListTile(
                dense: true,
                contentPadding: EdgeInsets.zero,
                leading: const Icon(Icons.camera_alt),
                title: const Text('Take Photo'),
                onTap: () {
                  Get.back();
                  pickImage(ImageSource.camera);
                },
              ),
              Divider(color: Colors.black26, thickness: .5),
              ListTile(
                dense: true,
                contentPadding: EdgeInsets.zero,
                leading: const Icon(Icons.photo_library),
                title: const Text('Choose from Gallery'),
                onTap: () {
                  Get.back();
                  pickImage(ImageSource.gallery);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
