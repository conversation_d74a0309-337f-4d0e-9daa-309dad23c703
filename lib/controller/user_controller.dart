import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:ensuram/view/dashboard/view/dashboard_view.dart';
import 'package:ensuram/view/modules/authentication/view/login_view.dart';
import 'package:ensuram/view/starting/boarding_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';

import 'package:shared_preferences/shared_preferences.dart';

import '../core/constants/api_endpoints.dart';
import '../core/services/http_service.dart';
import '../core/widgets/widgets.dart';
import '../model/user_model.dart';
import '../view/modules/notifications/model/notifications_model.dart';

class UserController extends GetxController {
  User? userModel;
  String? token;
  String? password;
  bool isLoggedIn = false;


  RxBool isOrdersMoreLoading = false.obs;
  RxInt totalOrdersCount = 0.obs;
  RxInt currentOrdersPage = 0.obs;
  RxList notifications = <Notifcations>[].obs;
  RxBool isNotificationLoading = false.obs;
  // var member = MemberDetail().obs; // Observable variable for member details
@override
  void onInit() {
    fetchUser();

    super.onInit();

  }
  Future<void> saveUser(User userModel, String token) async {
    try {
      final SharedPreferences sharedUser =
          await SharedPreferences.getInstance();
      final userString = jsonEncode(userModel);
      sharedUser.setString('user', userString);
      sharedUser.setString('token', token);


    } catch (e) {
      print('Error saving user: $e');
    }
  }

  Future<void> eraseUser() async {
    try {
      final SharedPreferences sharedUser =
          await SharedPreferences.getInstance();
      sharedUser.remove('user');
      sharedUser.remove('token');

      sharedUser.clear();
      update();
    } catch (e) {
      print('Error erasing user: $e');
    }
  }

  Future<void> fetchUser() async {
    try {
      final SharedPreferences sharedUser =
          await SharedPreferences.getInstance();
      userModel = User.fromJson(jsonDecode(sharedUser.getString('user')!));
      token = sharedUser.getString('token');

      update();
    } catch (e) {
      print('Error fetching user: $e');
    }
  }

  void navigateToNextScreen() async {
    await fetchUser();
    Timer(const Duration(seconds: 2), () {
      Get.offAll(
            () => token != null ? UserDashboardView():BoardingView(),
      );
    });

  }

  fetchUserDetails() async {
    try {   Widgets.showLoader("Loading.. ");
      var response = await ApiService.getData(Endpoints.userInfo);
Widgets.hideLoader();
      if (response.status == true) {
        User userModel = User.fromJson(response.data['user']);

       await  saveUser(userModel, token ?? "");
      await   fetchUser();

      } else {}
    } catch (e) {
    } finally {Widgets.hideLoader();}
  }
  fetchUserDetailsBackground() async {
    try {
    var response = await ApiService.getData(Endpoints.userInfo);

    if (response.status == true) {
      User userModel = User.fromJson(response.data['user']);

      await  saveUser(userModel, token ?? "");
      await   fetchUser();

    } else {}
    } catch (e) {
    } finally {Widgets.hideLoader();}
  }


  logout() async {


    await eraseUser();
    Get.offAll(
      () => UserLoginView(),
    );
  }
  //
  // void requestDeletionAccount() async {
  //   try {
  //     Widgets.showLoader("Loading.. ");
  //
  //     var response = await ApiService.postData(Endpoints.deleteAccount, {});
  //     Get.back();
  //     Widgets.hideLoader();
  //
  //     if (response.status == true) {
  //       Widgets.showSnackBar("Success", "Account Deleted");
  //       logout();
  //     } else {}
  //   } catch (e) {
  //     Widgets.hideLoader();
  //   }
  // }
  //
  // void requestLogoutAccount() async {
  //   try {
  //     Get.back();
  //     Widgets.showLoader("Loading.. ");
  //
  //     var response = await ApiService.getData(Endpoints.logoutAccount);
  //
  //     Widgets.hideLoader();
  //
  //     if (response.status == true) {
  //       logout();
  //     } else {}
  //   } catch (e) {
  //     Widgets.hideLoader();
  //   }
  // }
  // void updateFcmToken() async {
  //   try {
  //     String? token = await getToken();
  //     var response = await ApiService.postData(
  //         Endpoints.updateToken, {"fcm_token": token});
  //   } catch (e) {}
  // }
  //
  //
  //
  // getToken() async {
  //   final FirebaseMessaging Fcm = FirebaseMessaging.instance;
  //
  //   String? token = await Fcm.getToken();
  //   log("Firebase Messaging Token: $token");
  //   return token;
  // }
  fetchNotifcations({int page = 1}) async {
    try {
      if (isNotificationLoading.value) return;
      if (page == 1) {
        isNotificationLoading.value = true;
      } else {
        isOrdersMoreLoading.value = true;
      }

      var response = await ApiService.getData(
          "${Endpoints.fetchNotifications}?page=$page&limit=15");
      isNotificationLoading.value = false;
      isOrdersMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          notifications.clear();
          totalOrdersCount.value = 0;
          currentOrdersPage.value = 1;
        }
       notifications.addAll(
          (response.data['data'] as List)
              .map((e) => Notifcations.fromJson(e))
              .toList(),
        );

        totalOrdersCount.value =
            int.parse(response.data['pagination']['total_count'].toString());
      }
    } catch (e) {
      print(e);
      isNotificationLoading.value = false;
      isOrdersMoreLoading.value = false;
    } finally {
      isNotificationLoading.value = false;
      isOrdersMoreLoading.value = false;
    }
  }
void updateNotificationStatus() async {
  try {

    var response = await ApiService.postData(
        Endpoints.updateotificationStatus, {});
  } catch (e) {}
}
}
