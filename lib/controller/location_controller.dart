import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../core/constants/api_endpoints.dart';
import '../core/services/http_service.dart';
import '../model/country_model.dart';

class LocationController extends GetxController {


  TextEditingController searchFieldController = TextEditingController();

  RxList countries = <Country>[].obs;
  RxBool isCountriesLoading = false.obs;
  RxBool isCountriesMoreLoading = false.obs;
  RxInt totalCountries = 0.obs;
  RxInt currentCountriesPage = 0.obs;

  RxList cities = <City>[].obs;
  RxBool isCitiesLoading = false.obs;
  RxBool isCitiesMoreLoading = false.obs;
  RxInt totalCities = 0.obs;
  RxInt currentCitiesPage = 0.obs;

  RxList statesList = <States>[].obs;
  RxBool isStatesLoading = false.obs;
  RxBool isStatesMoreLoading = false.obs;
  RxInt totalStates = 0.obs;
  RxInt currentStatesPage = 0.obs;


  fetchCountries(String? query,{int page = 1}) async {
    try {


      if (isCountriesLoading.value) return;
      if (page == 1) {
        isCountriesLoading.value = true;
      } else {
        isCountriesMoreLoading.value = true;
      }
      var response = await ApiService.getDataLocation("${Endpoints.countries}?search=$query&page=$page",
      );
      isCountriesLoading.value = false;
      isCountriesMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          countries.clear();
          totalCountries.value = 0;
          currentCountriesPage.value = 1;
        }

        countries.addAll(
          (response.data['data'] as List)
              .map((e) => Country.fromJson(e))
              .toList(),

        );

        totalCountries.value = response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {print(e);
    isCountriesLoading.value = false;
    isCountriesMoreLoading.value = false;
    } finally {
      isCountriesLoading.value = false;
      isCountriesMoreLoading.value = false;
    }
  }
  fetchCities(String? query,{int page = 1,String? state}) async {
    try {


      if (isCitiesLoading.value) return;
      if (page == 1) {
        isCitiesLoading.value = true;
      } else {
        isCitiesMoreLoading.value = true;
      }
      var response = await ApiService.getDataLocation("${Endpoints.cities}?search=$query&state=$state&page=$page",
      );
      isCitiesLoading.value = false;
      isCitiesMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          cities.clear();
          totalCities.value = 0;
          currentCitiesPage.value = 1;
        }

        cities.addAll(
          (response.data['data'] as List)
              .map((e) => City.fromJson(e))
              .toList(),

        );

        totalCities.value = response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {print(e);
    isCitiesLoading.value = false;
    isCitiesMoreLoading.value = false;
    } finally {
      isCitiesLoading.value = false;
      isCitiesMoreLoading.value = false;
    }
  }
  fetchState(String? query,{int page = 1,String? country}) async {
    try {


      if (isStatesLoading.value) return;
      if (page == 1) {
        isStatesLoading.value = true;
      } else {
        isStatesMoreLoading.value = true;
      }
      var response = await ApiService.getDataLocation("${Endpoints.states}?search=$query&country=$country&page=$page",
      );
      isStatesLoading.value = false;
      isStatesMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          statesList.clear();
          totalStates.value = 0;
          currentStatesPage.value = 1;
        }

        statesList.addAll(
          (response.data['data'] as List)
              .map((e) => States.fromJson(e))
              .toList(),

        );

        totalStates.value = response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {print(e);
    isStatesLoading.value = false;
    isStatesMoreLoading.value = false;
    } finally {
      isStatesLoading.value = false;
      isStatesMoreLoading.value = false;
    }
  }

}