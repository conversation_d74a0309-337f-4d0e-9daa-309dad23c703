plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.rixxsol.enusram"
    compileSdk = 35
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'  // Update this to match Java version
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.rixxsol.enusram"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.debug
            minifyEnabled false
            shrinkResources false
        }
    }

    buildFeatures {
        viewBinding true
    }

    dependencies {
        implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.6.2"
        implementation "androidx.lifecycle:lifecycle-common-java8:2.6.2"
        implementation "androidx.fragment:fragment-ktx:1.6.2"
        implementation "org.jetbrains.kotlinx:kotlinx-metadata-jvm:0.9.0"
        // Try with version 0.11.0
    }

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
        exclude 'META-INF/*.kotlin_module'
        // Add these lines to exclude Kotlin metadata files
        exclude 'META-INF/*.kotlin_metadata'
        exclude 'META-INF/*.kotlin_builtins'
        exclude 'kotlin/**'
        exclude 'kotlinx/**'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation "androidx.appcompat:appcompat:1.6.1"
    implementation "androidx.core:core-ktx:1.12.0"
    implementation "org.jetbrains.kotlinx:kotlinx-metadata-jvm:0.9.0"
    // Remove any references to version 0.11.0
}
