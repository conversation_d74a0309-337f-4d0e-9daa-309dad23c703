# Keep Kotlin Metadata
-keep class kotlin.Metadata { *; }

# Keep kotlinx.metadata
-dontwarn kotlinx.metadata.**
-keep class kotlinx.metadata.** { *; }

# Keep SmileID classes
-keep class com.smileidentity.** { *; }
-dontwarn com.smileidentity.**

# Keep Flutter plugins
-keep class io.flutter.plugins.** { *; }
-keep class io.flutter.plugin.** { *; }

# Keep R8 from stripping interface information
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class * extends com.bumptech.glide.module.AppGlideModule {
 <init>(...);
}
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}

# Keep Kotlin Coroutines
-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler {}
-keepclassmembernames class kotlinx.** {
    volatile <fields>;
}