                        -H/Users/<USER>/FlutterDev/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-<PERSON>ANDROID_PLATFORM=android-21
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Downloads/projects/ensuramproducts/enusram/build/app/intermediates/cxx/RelWithDebInfo/6f6o6865/obj/x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Downloads/projects/ensuramproducts/enusram/build/app/intermediates/cxx/RelWithDebInfo/6f6o6865/obj/x86
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-B/Users/<USER>/Downloads/projects/ensuramproducts/enusram/android/app/.cxx/RelWithDebInfo/6f6o6865/x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2